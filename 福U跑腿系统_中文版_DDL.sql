-- 福U跑腿系统 - 中文版数据库设计
-- 所有表名和字段名均使用中文
-- 生成时间: 2025-01-10

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===========================
-- 1. 核心用户表
-- ===========================

-- 用户表（强实体）
DROP TABLE IF EXISTS `用户`;
CREATE TABLE `用户` (
  `用户编号` bigint NOT NULL AUTO_INCREMENT COMMENT '全局用户编号',
  `设备类型` tinyint NOT NULL COMMENT '设备类型: 0=PC端, 1=小程序',
  `创建时间` datetime NOT NULL COMMENT '创建时间',
  `登录时间` datetime NOT NULL COMMENT '上次登录时间',
  `登录IP` varchar(20) NOT NULL COMMENT '登录IP地址',
  `登录地区` varchar(50) NOT NULL COMMENT '登录地区',
  `用户类型` int NOT NULL COMMENT '用户类型: 0=超级管理员, 1=校区管理员, 2=普通管理员, 3=普通用户, 4=跑腿用户',
  `创建人` bigint NOT NULL COMMENT '创建人编号',
  `更新时间` datetime NOT NULL COMMENT '更新时间',
  `更新人` bigint NOT NULL COMMENT '更新人编号',
  PRIMARY KEY (`用户编号`),
  INDEX `索引_用户类型` (`用户类型`),
  INDEX `索引_创建时间` (`创建时间`)
) ENGINE=InnoDB COMMENT='用户基础信息表';

-- PC用户表（强实体，与用户一对一关系）
DROP TABLE IF EXISTS `PC用户`;
CREATE TABLE `PC用户` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `用户编号` bigint NOT NULL COMMENT '关联用户表',
  `用户名` varchar(25) NOT NULL COMMENT '登录用户名',
  `密码` varchar(100) NOT NULL COMMENT '登录密码',
  `手机号` varchar(11) NOT NULL COMMENT '手机号码',
  `真实姓名` varchar(20) NOT NULL COMMENT '真实姓名',
  `学生证地址` varchar(255) DEFAULT NULL COMMENT '学生证图片地址',
  `身份证地址` varchar(255) DEFAULT NULL COMMENT '身份证图片地址',
  `性别` tinyint NOT NULL COMMENT '性别: 0=女, 1=男',
  `状态` tinyint NOT NULL DEFAULT 0 COMMENT '账户状态: 0=禁用, 1=启用',
  `头像` varchar(255) NOT NULL COMMENT '头像地址',
  `邮箱` varchar(50) DEFAULT NULL COMMENT '邮箱地址',
  `邮箱启用` tinyint DEFAULT NULL COMMENT '是否启用邮箱',
  PRIMARY KEY (`编号`),
  UNIQUE KEY `唯一_用户编号` (`用户编号`),
  UNIQUE KEY `唯一_用户名` (`用户名`),
  CONSTRAINT `外键_PC用户_用户编号` FOREIGN KEY (`用户编号`) REFERENCES `用户` (`用户编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='PC端用户表（平台管理员）';

-- 微信用户表（强实体，与用户一对一关系）
DROP TABLE IF EXISTS `微信用户`;
CREATE TABLE `微信用户` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `用户编号` bigint NOT NULL COMMENT '关联用户表',
  `微信标识` varchar(128) NOT NULL COMMENT '小程序唯一标识',
  `头像` varchar(255) NOT NULL COMMENT '头像地址',
  `昵称` varchar(10) NOT NULL COMMENT '用户昵称',
  `手机号` varchar(11) DEFAULT NULL COMMENT '手机号码',
  `积分` int NOT NULL DEFAULT 0 COMMENT '用户积分',
  `是否跑腿员` tinyint NOT NULL DEFAULT 0 COMMENT '是否跑腿员: 0=否, 1=是',
  `可否下单` tinyint NOT NULL DEFAULT 0 COMMENT '是否可下单: 0=否, 1=是',
  `可否接单` tinyint NOT NULL DEFAULT 0 COMMENT '是否可接单: 0=否, 1=是',
  `学校编号` bigint DEFAULT NULL COMMENT '绑定学校编号',
  `真实姓名` varchar(20) DEFAULT NULL COMMENT '跑腿员真实姓名',
  `性别` tinyint DEFAULT NULL COMMENT '跑腿员性别',
  `信用分` int DEFAULT NULL COMMENT '信用分数',
  PRIMARY KEY (`编号`),
  UNIQUE KEY `唯一_用户编号` (`用户编号`),
  UNIQUE KEY `唯一_微信标识` (`微信标识`),
  INDEX `索引_学校编号` (`学校编号`),
  CONSTRAINT `外键_微信用户_用户编号` FOREIGN KEY (`用户编号`) REFERENCES `用户` (`用户编号`) ON DELETE CASCADE,
  CONSTRAINT `外键_微信用户_学校` FOREIGN KEY (`学校编号`) REFERENCES `学校` (`编号`) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='微信用户表（小程序用户）';

-- ===========================
-- 2. 学校相关表
-- ===========================

-- 学校表（强实体）
DROP TABLE IF EXISTS `学校`;
CREATE TABLE `学校` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `管理员编号` bigint NOT NULL COMMENT '管理员用户编号',
  `城市编码` char(6) DEFAULT NULL COMMENT '城市行政编码',
  `学校名称` varchar(100) NOT NULL COMMENT '学校名称',
  `学校标志` varchar(255) NOT NULL COMMENT '学校logo地址',
  `创建时间` datetime NOT NULL COMMENT '创建时间',
  `更新时间` datetime NOT NULL COMMENT '更新时间',
  `状态` tinyint NOT NULL COMMENT '学校状态: 0=禁用, 1=启用',
  `平台收益占比` tinyint NOT NULL COMMENT '平台收益占比',
  `代理收益占比` tinyint NOT NULL COMMENT '代理收益占比',
  `跑腿收益占比` tinyint NOT NULL COMMENT '跑腿收益占比',
  `底价` decimal(10,2) NOT NULL COMMENT '订单底价',
  PRIMARY KEY (`编号`),
  UNIQUE KEY `唯一_学校名称` (`学校名称`),
  INDEX `索引_管理员编号` (`管理员编号`),
  INDEX `索引_状态` (`状态`),
  CONSTRAINT `外键_学校_管理员编号` FOREIGN KEY (`管理员编号`) REFERENCES `用户` (`用户编号`) ON DELETE RESTRICT
) ENGINE=InnoDB COMMENT='学校信息表';

-- 学校区域表（强实体，与学校多对一关系）
DROP TABLE IF EXISTS `学校区域`;
CREATE TABLE `学校区域` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `学校编号` bigint NOT NULL COMMENT '学校编号',
  `类型` tinyint NOT NULL COMMENT '区域类型: 0=区域, 1=楼栋',
  `名称` varchar(12) NOT NULL COMMENT '区域名称',
  `经度` varchar(50) DEFAULT NULL COMMENT '地理经度',
  `纬度` varchar(50) DEFAULT NULL COMMENT '地理纬度',
  `父区域编号` bigint DEFAULT NULL COMMENT '父级区域编号',
  `备注` varchar(20) DEFAULT NULL COMMENT '备注信息',
  `创建时间` datetime NOT NULL COMMENT '创建时间',
  `创建人` bigint NOT NULL COMMENT '创建人编号',
  `更新时间` datetime NOT NULL COMMENT '修改时间',
  `更新人` bigint NOT NULL COMMENT '修改人编号',
  PRIMARY KEY (`编号`),
  INDEX `索引_学校编号` (`学校编号`),
  INDEX `索引_父区域编号` (`父区域编号`),
  CONSTRAINT `外键_学校区域_学校` FOREIGN KEY (`学校编号`) REFERENCES `学校` (`编号`) ON DELETE CASCADE,
  CONSTRAINT `外键_学校区域_父区域` FOREIGN KEY (`父区域编号`) REFERENCES `学校区域` (`编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='学校区域表（楼栋管理）';

-- 标签表（强实体，与学校多对一关系）
DROP TABLE IF EXISTS `标签`;
CREATE TABLE `标签` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `学校编号` bigint NOT NULL COMMENT '学校编号',
  `标签名称` varchar(10) NOT NULL COMMENT '标签名称',
  `备注` varchar(50) DEFAULT NULL COMMENT '备注信息',
  `服务类型` int NOT NULL COMMENT '服务类型: 0=帮取送, 1=代买, 2=万能服务',
  `创建时间` datetime NOT NULL COMMENT '创建时间',
  `创建人` bigint NOT NULL COMMENT '创建人编号',
  `更新时间` datetime NOT NULL COMMENT '更新时间',
  `更新人` bigint NOT NULL COMMENT '更新人编号',
  PRIMARY KEY (`编号`),
  INDEX `索引_学校编号` (`学校编号`),
  INDEX `索引_服务类型` (`服务类型`),
  CONSTRAINT `外键_标签_学校` FOREIGN KEY (`学校编号`) REFERENCES `学校` (`编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='订单标签表';

-- ===========================
-- 3. 订单相关表
-- ===========================

-- 订单表（强实体）
DROP TABLE IF EXISTS `订单`;
CREATE TABLE `订单` (
  `订单编号` bigint NOT NULL COMMENT '订单编号（非自增）',
  `学校编号` bigint NOT NULL COMMENT '学校编号',
  `服务类型` int NOT NULL COMMENT '服务类型: 0=帮取送, 1=代买, 2=万能服务',
  `标签` varchar(10) NOT NULL COMMENT '订单标签',
  `物品重量` varchar(20) DEFAULT NULL COMMENT '物品重量',
  `起点地址` json DEFAULT NULL COMMENT '起点地址信息',
  `终点地址` json NOT NULL COMMENT '终点地址信息',
  `详细描述` varchar(100) NOT NULL COMMENT '订单详细描述',
  `是否定时` tinyint NOT NULL COMMENT '是否指定时间: 0=否, 1=是',
  `指定时间` datetime DEFAULT NULL COMMENT '指定送达时间',
  `自动取消时长` int NOT NULL COMMENT '未接单自动取消时间（秒）',
  `性别要求` tinyint NOT NULL COMMENT '跑腿员性别要求: 0=女, 1=男, 2=不限',
  `预估商品价格` decimal(10,2) DEFAULT NULL COMMENT '预估商品价格',
  `订单总金额` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `订单状态` tinyint NOT NULL COMMENT '订单状态',
  `创建时间` datetime NOT NULL COMMENT '创建时间',
  `下单用户编号` bigint NOT NULL COMMENT '下单用户编号',
  `跑腿员编号` bigint DEFAULT NULL COMMENT '跑腿员编号',
  PRIMARY KEY (`订单编号`),
  INDEX `索引_学校编号` (`学校编号`),
  INDEX `索引_下单用户编号` (`下单用户编号`),
  INDEX `索引_跑腿员编号` (`跑腿员编号`),
  INDEX `索引_订单状态` (`订单状态`),
  INDEX `索引_创建时间` (`创建时间`),
  CONSTRAINT `外键_订单_学校` FOREIGN KEY (`学校编号`) REFERENCES `学校` (`编号`) ON DELETE RESTRICT,
  CONSTRAINT `外键_订单_下单用户` FOREIGN KEY (`下单用户编号`) REFERENCES `用户` (`用户编号`) ON DELETE RESTRICT,
  CONSTRAINT `外键_订单_跑腿员` FOREIGN KEY (`跑腿员编号`) REFERENCES `用户` (`用户编号`) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='订单主表';

-- 订单支付表（弱实体，与订单一对一关系）
DROP TABLE IF EXISTS `订单支付`;
CREATE TABLE `订单支付` (
  `订单编号` bigint NOT NULL COMMENT '订单编号',
  `附加金额` decimal(10,2) DEFAULT NULL COMMENT '附加金额',
  `实付金额` decimal(10,2) DEFAULT NULL COMMENT '实际支付金额',
  `支付状态` tinyint NOT NULL COMMENT '支付状态: 0=未支付, 1=已支付, 2=退款中, 3=已退款',
  `付款时间` datetime DEFAULT NULL COMMENT '付款时间',
  `退款中时间` datetime DEFAULT NULL COMMENT '退款中时间',
  `退款时间` datetime DEFAULT NULL COMMENT '退款完成时间',
  `是否使用优惠券` tinyint NOT NULL COMMENT '是否使用优惠券: 0=否, 1=是',
  `优惠券编号` bigint DEFAULT NULL COMMENT '优惠券编号',
  `优惠金额` decimal(10,2) DEFAULT NULL COMMENT '优惠金额',
  PRIMARY KEY (`订单编号`),
  INDEX `索引_支付状态` (`支付状态`),
  INDEX `索引_优惠券编号` (`优惠券编号`),
  CONSTRAINT `外键_订单支付_订单` FOREIGN KEY (`订单编号`) REFERENCES `订单` (`订单编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='订单支付信息表';

-- 订单进度表（弱实体，与订单一对一关系）
DROP TABLE IF EXISTS `订单进度`;
CREATE TABLE `订单进度` (
  `订单编号` bigint NOT NULL COMMENT '订单编号',
  `接单时间` datetime DEFAULT NULL COMMENT '接单时间',
  `开始配送时间` datetime DEFAULT NULL COMMENT '开始配送时间',
  `送达时间` datetime DEFAULT NULL COMMENT '送达时间',
  `完成时间` datetime DEFAULT NULL COMMENT '订单完成时间',
  `完成类型` tinyint DEFAULT NULL COMMENT '完成类型',
  `取消时间` datetime DEFAULT NULL COMMENT '取消时间',
  `取消原因` varchar(100) DEFAULT NULL COMMENT '取消原因',
  `取消人类型` int DEFAULT NULL COMMENT '取消人类型',
  `取消人编号` bigint DEFAULT NULL COMMENT '取消人编号',
  PRIMARY KEY (`订单编号`),
  CONSTRAINT `外键_订单进度_订单` FOREIGN KEY (`订单编号`) REFERENCES `订单` (`订单编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='订单进度跟踪表';

-- 订单聊天表（强实体，与订单一对多关系）
DROP TABLE IF EXISTS `订单聊天`;
CREATE TABLE `订单聊天` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `订单编号` bigint NOT NULL COMMENT '订单编号',
  `发送者编号` bigint DEFAULT NULL COMMENT '发送者编号',
  `发送者类型` int DEFAULT NULL COMMENT '发送者类型',
  `接收者编号列表` varchar(255) DEFAULT NULL COMMENT '接收者编号列表',
  `消息类型` int DEFAULT NULL COMMENT '消息类型',
  `消息内容` varchar(255) DEFAULT NULL COMMENT '消息内容',
  `已读编号列表` varchar(255) DEFAULT NULL COMMENT '已读用户编号列表',
  `创建时间` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`编号` DESC),
  INDEX `索引_订单编号` (`订单编号`),
  INDEX `索引_创建时间` (`创建时间`),
  CONSTRAINT `外键_订单聊天_订单` FOREIGN KEY (`订单编号`) REFERENCES `订单` (`订单编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='订单聊天记录表';

-- 订单申诉表（强实体，与订单一对多关系）
DROP TABLE IF EXISTS `订单申诉`;
CREATE TABLE `订单申诉` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `订单编号` bigint NOT NULL COMMENT '订单编号',
  `学校编号` bigint DEFAULT NULL COMMENT '学校编号',
  `申诉时间` datetime DEFAULT NULL COMMENT '申诉时间',
  `申诉理由` varchar(100) DEFAULT NULL COMMENT '申诉理由',
  `申诉状态` tinyint DEFAULT NULL COMMENT '申诉状态: 0=不通过, 1=通过, 2=申诉中',
  `更新时间` datetime DEFAULT NULL COMMENT '更新时间',
  `备注` varchar(100) DEFAULT NULL COMMENT '申诉备注',
  `更新人编号` bigint DEFAULT NULL COMMENT '更新人编号',
  `更新人类型` int DEFAULT NULL COMMENT '更新人类型',
  PRIMARY KEY (`编号`),
  INDEX `索引_订单编号` (`订单编号`),
  INDEX `索引_学校编号` (`学校编号`),
  INDEX `索引_申诉状态` (`申诉状态`),
  CONSTRAINT `外键_订单申诉_订单` FOREIGN KEY (`订单编号`) REFERENCES `订单` (`订单编号`) ON DELETE CASCADE,
  CONSTRAINT `外键_订单申诉_学校` FOREIGN KEY (`学校编号`) REFERENCES `学校` (`编号`) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='订单申诉处理表';

-- 订单附件表（强实体，与订单一对多关系）
DROP TABLE IF EXISTS `订单附件`;
CREATE TABLE `订单附件` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `订单编号` bigint NOT NULL COMMENT '订单编号',
  `文件原名` varchar(100) NOT NULL COMMENT '文件原始名称',
  `文件地址` varchar(255) NOT NULL COMMENT '文件存储地址',
  `文件类型` varchar(50) NOT NULL COMMENT '文件后缀类型',
  `文件大小` int NOT NULL COMMENT '文件大小（字节）',
  `附件类型` int NOT NULL COMMENT '附件类型: 1=订单附件, 2=完成凭证, 3=申诉凭证',
  PRIMARY KEY (`编号`),
  INDEX `索引_订单编号` (`订单编号`),
  INDEX `索引_附件类型` (`附件类型`),
  CONSTRAINT `外键_订单附件_订单` FOREIGN KEY (`订单编号`) REFERENCES `订单` (`订单编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='订单附件管理表';

-- ===========================
-- 4. 财务相关表
-- ===========================

-- 钱包表（弱实体，与用户一对一关系）
DROP TABLE IF EXISTS `钱包`;
CREATE TABLE `钱包` (
  `用户编号` bigint NOT NULL COMMENT '用户编号',
  `当前余额` decimal(10,2) NOT NULL COMMENT '当前可用余额',
  `已提现金额` decimal(10,2) NOT NULL COMMENT '已提现金额',
  `创建时间` datetime NOT NULL COMMENT '创建时间',
  `更新时间` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`用户编号`),
  CONSTRAINT `外键_钱包_用户` FOREIGN KEY (`用户编号`) REFERENCES `用户` (`用户编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='用户钱包表';

-- 资金流水表（强实体）
DROP TABLE IF EXISTS `资金流水`;
CREATE TABLE `资金流水` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `订单编号` bigint DEFAULT NULL COMMENT '订单编号',
  `代理编号` bigint DEFAULT NULL COMMENT '代理编号',
  `代理收益` decimal(10,2) DEFAULT NULL COMMENT '代理收益金额',
  `跑腿员编号` bigint DEFAULT NULL COMMENT '跑腿员编号',
  `跑腿收益` decimal(10,2) DEFAULT NULL COMMENT '跑腿收益金额',
  `用户编号` bigint DEFAULT NULL COMMENT '用户编号',
  `用户收益` decimal(10,2) DEFAULT NULL COMMENT '用户收益金额',
  `平台收益` decimal(10,2) DEFAULT NULL COMMENT '平台收益金额',
  `创建时间` datetime DEFAULT NULL COMMENT '创建时间',
  `流水类型` tinyint UNSIGNED DEFAULT NULL COMMENT '流水类型: 订单收益/跑腿提现/代理提现',
  PRIMARY KEY (`编号`),
  INDEX `索引_订单编号` (`订单编号`),
  INDEX `索引_代理编号` (`代理编号`),
  INDEX `索引_跑腿员编号` (`跑腿员编号`),
  INDEX `索引_用户编号` (`用户编号`),
  INDEX `索引_创建时间` (`创建时间`),
  CONSTRAINT `外键_资金流水_订单` FOREIGN KEY (`订单编号`) REFERENCES `订单` (`订单编号`) ON DELETE SET NULL,
  CONSTRAINT `外键_资金流水_代理` FOREIGN KEY (`代理编号`) REFERENCES `用户` (`用户编号`) ON DELETE SET NULL,
  CONSTRAINT `外键_资金流水_跑腿员` FOREIGN KEY (`跑腿员编号`) REFERENCES `用户` (`用户编号`) ON DELETE SET NULL,
  CONSTRAINT `外键_资金流水_用户` FOREIGN KEY (`用户编号`) REFERENCES `用户` (`用户编号`) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='资金流水记录表';

-- 提现记录表（强实体，与用户一对多关系）
DROP TABLE IF EXISTS `提现记录`;
CREATE TABLE `提现记录` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `用户编号` bigint NOT NULL COMMENT '用户编号',
  `提现金额` decimal(10,2) NOT NULL COMMENT '提现金额',
  `提现平台` varchar(10) NOT NULL COMMENT '提现平台',
  `卡号` varchar(100) NOT NULL COMMENT '银行卡号或账号',
  `审核状态` tinyint NOT NULL COMMENT '审核状态: 0=驳回, 1=通过, 2=审核中',
  `用户类型` tinyint NOT NULL COMMENT '用户类型',
  `用户备注` varchar(50) NOT NULL COMMENT '用户备注',
  `审核人编号` bigint NOT NULL COMMENT '审核人编号',
  `审核时间` datetime NOT NULL COMMENT '审核时间',
  `创建时间` datetime NOT NULL COMMENT '创建时间',
  `审核反馈` varchar(50) DEFAULT NULL COMMENT '审核反馈',
  PRIMARY KEY (`编号`),
  INDEX `索引_用户编号` (`用户编号`),
  INDEX `索引_审核状态` (`审核状态`),
  CONSTRAINT `外键_提现记录_用户` FOREIGN KEY (`用户编号`) REFERENCES `用户` (`用户编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='提现申请记录表';

-- ===========================
-- 5. 其他辅助表
-- ===========================

-- 地址表（强实体，与用户一对多关系）
DROP TABLE IF EXISTS `地址`;
CREATE TABLE `地址` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `用户编号` bigint NOT NULL COMMENT '用户编号',
  `地点名称` varchar(50) NOT NULL COMMENT '地点名称',
  `详细地址` varchar(50) NOT NULL COMMENT '详细地址',
  `经度` varchar(50) NOT NULL COMMENT '地理经度',
  `纬度` varchar(50) NOT NULL COMMENT '地理纬度',
  `联系人姓名` varchar(20) NOT NULL COMMENT '联系人姓名',
  `联系电话` varchar(11) NOT NULL COMMENT '联系电话',
  `是否默认` tinyint NOT NULL COMMENT '是否默认地址: 0=否, 1=是',
  `创建时间` datetime NOT NULL COMMENT '创建时间',
  `创建人` bigint NOT NULL COMMENT '创建人编号',
  `更新时间` datetime NOT NULL COMMENT '修改时间',
  `更新人` bigint NOT NULL COMMENT '修改人编号',
  PRIMARY KEY (`编号`),
  INDEX `索引_用户编号` (`用户编号`),
  CONSTRAINT `外键_地址_用户` FOREIGN KEY (`用户编号`) REFERENCES `用户` (`用户编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='用户地址管理表';

-- 跑腿申请表（强实体）
DROP TABLE IF EXISTS `跑腿申请`;
CREATE TABLE `跑腿申请` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `用户编号` bigint NOT NULL COMMENT '用户编号',
  `学校编号` bigint NOT NULL COMMENT '学校编号',
  `学校名称` varchar(100) NOT NULL COMMENT '学校名称',
  `真实姓名` varchar(20) NOT NULL COMMENT '真实姓名',
  `性别` tinyint NOT NULL COMMENT '性别: 0=女, 1=男',
  `学生证地址` varchar(255) NOT NULL COMMENT '学生证图片地址',
  `创建时间` datetime NOT NULL COMMENT '创建时间',
  `申请状态` tinyint NOT NULL COMMENT '申请状态: 0=驳回, 1=通过, 2=申请中',
  `备注` varchar(100) DEFAULT NULL COMMENT '备注信息',
  `更新时间` datetime NOT NULL COMMENT '更新时间',
  `更新人` bigint NOT NULL COMMENT '更新人编号',
  PRIMARY KEY (`编号`),
  INDEX `索引_用户编号` (`用户编号`),
  INDEX `索引_学校编号` (`学校编号`),
  INDEX `索引_申请状态` (`申请状态`),
  CONSTRAINT `外键_跑腿申请_用户` FOREIGN KEY (`用户编号`) REFERENCES `用户` (`用户编号`) ON DELETE CASCADE,
  CONSTRAINT `外键_跑腿申请_学校` FOREIGN KEY (`学校编号`) REFERENCES `学校` (`编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='跑腿员申请表';

-- ===========================
-- 6. 系统管理表
-- ===========================

-- 权限表（强实体）
DROP TABLE IF EXISTS `权限`;
CREATE TABLE `权限` (
  `编号` bigint NOT NULL AUTO_INCREMENT,
  `权限名称` varchar(50) NOT NULL COMMENT '权限名称',
  `父级编号` bigint DEFAULT 0 COMMENT '父级权限编号',
  `排序` int DEFAULT 0 COMMENT '排序字段',
  `权限标识` varchar(50) DEFAULT NULL COMMENT '权限标识符',
  PRIMARY KEY (`编号`),
  INDEX `索引_父级编号` (`父级编号`)
) ENGINE=InnoDB COMMENT='系统权限表';

-- 角色权限关联表（关联实体）
DROP TABLE IF EXISTS `角色权限`;
CREATE TABLE `角色权限` (
  `角色编号` bigint NOT NULL COMMENT '角色编号',
  `权限编号` bigint NOT NULL COMMENT '权限编号',
  PRIMARY KEY (`角色编号`, `权限编号`),
  CONSTRAINT `外键_角色权限_权限` FOREIGN KEY (`权限编号`) REFERENCES `权限` (`编号`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='角色权限关联表';

-- 对象存储表（强实体）
DROP TABLE IF EXISTS `对象存储`;
CREATE TABLE `对象存储` (
  `编号` bigint NOT NULL COMMENT '对象存储主键',
  `文件大小` int NOT NULL COMMENT '文件大小',
  `文件名称` varchar(255) NOT NULL DEFAULT '' COMMENT '存储文件名',
  `文件类型` int NOT NULL DEFAULT 0 COMMENT '文件类型',
  `原始名称` varchar(255) NOT NULL DEFAULT '' COMMENT '原始文件名',
  `文件后缀` varchar(10) NOT NULL DEFAULT '' COMMENT '文件后缀',
  `访问地址` varchar(500) NOT NULL COMMENT 'URL访问地址',
  `创建时间` datetime DEFAULT NULL COMMENT '创建时间',
  `创建人` varchar(64) DEFAULT '' COMMENT '上传人',
  `更新时间` datetime DEFAULT NULL COMMENT '更新时间',
  `更新人` varchar(64) DEFAULT '' COMMENT '更新人',
  `服务商` varchar(20) NOT NULL DEFAULT 'minio' COMMENT '存储服务商',
  PRIMARY KEY (`编号`)
) ENGINE=InnoDB COMMENT='对象存储管理表';

-- 存储配置表（强实体）
DROP TABLE IF EXISTS `存储配置`;
CREATE TABLE `存储配置` (
  `编号` bigint NOT NULL COMMENT '主键',
  `配置键` varchar(20) NOT NULL DEFAULT '' COMMENT '配置键名',
  `访问密钥` varchar(255) DEFAULT '' COMMENT '访问密钥',
  `秘密密钥` varchar(255) DEFAULT '' COMMENT '秘密密钥',
  `存储桶名称` varchar(255) DEFAULT '' COMMENT '存储桶名称',
  `前缀` varchar(255) DEFAULT '' COMMENT '路径前缀',
  `访问端点` varchar(255) DEFAULT '' COMMENT '访问端点',
  `自定义域名` varchar(255) DEFAULT '' COMMENT '自定义域名',
  `是否HTTPS` char(1) DEFAULT 'N' COMMENT '是否使用HTTPS: Y=是, N=否',
  `区域` varchar(255) DEFAULT '' COMMENT '存储区域',
  `访问策略` char(1) NOT NULL DEFAULT '1' COMMENT '桶权限类型: 0=私有, 1=公开, 2=自定义',
  `状态` char(1) DEFAULT '1' COMMENT '是否默认: 0=是, 1=否',
  `扩展字段` varchar(255) DEFAULT '' COMMENT '扩展字段',
  `创建时间` datetime DEFAULT NULL COMMENT '创建时间',
  `更新时间` datetime DEFAULT NULL COMMENT '更新时间',
  `备注` varchar(500) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`编号`)
) ENGINE=InnoDB COMMENT='对象存储配置表';

-- 统计数据表（强实体）
DROP TABLE IF EXISTS `统计数据`;
CREATE TABLE `统计数据` (
  `编号` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `总订单量` int NOT NULL DEFAULT 0 COMMENT '总订单数量',
  `取消订单量` int NOT NULL DEFAULT 0 COMMENT '取消订单数量',
  `申诉订单量` int NOT NULL DEFAULT 0 COMMENT '申诉订单数量',
  `完成订单量` int NOT NULL DEFAULT 0 COMMENT '完成订单数量',
  `完单率` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '完单率百分比',
  `帮取送订单量` int NOT NULL DEFAULT 0 COMMENT '帮取送订单数量',
  `代买订单量` int NOT NULL DEFAULT 0 COMMENT '代买订单数量',
  `万能服务订单量` int NOT NULL DEFAULT 0 COMMENT '万能服务订单数量',
  `帮取送占比` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '帮取送订单占比',
  `代买占比` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '代买订单占比',
  `万能服务占比` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '万能订单占比',
  `总收款金额` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '总收款金额',
  `总退款金额` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '总退款金额',
  `平台总收益` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '平台总收益',
  `代理总收益` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '代理总收益',
  `跑腿总收益` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '跑腿总收益',
  `总访问量` int NOT NULL DEFAULT 0 COMMENT '总访问次数',
  `独立访客数` int NOT NULL DEFAULT 0 COMMENT '独立访问用户数',
  `恶意请求数` int NOT NULL DEFAULT 0 COMMENT '恶意请求数量',
  `新增用户数` int NOT NULL DEFAULT 0 COMMENT '新增用户数量',
  `活跃用户数` int NOT NULL DEFAULT 0 COMMENT '活跃用户数量',
  `新增跑腿用户数` int NOT NULL DEFAULT 0 COMMENT '新增跑腿用户数',
  `活跃跑腿用户数` int NOT NULL DEFAULT 0 COMMENT '活跃跑腿用户数',
  `统计日期` date NOT NULL COMMENT '统计日期',
  PRIMARY KEY (`编号`),
  UNIQUE KEY `唯一_统计日期` (`统计日期`)
) ENGINE=InnoDB COMMENT='每日统计数据表';

SET FOREIGN_KEY_CHECKS = 1;

-- ===========================
-- E-R图关系说明
-- ===========================
/*
主要实体关系（中文版）：
1. 用户 (1:1) PC用户 - 用户拥有PC账户
2. 用户 (1:1) 微信用户 - 用户拥有微信账户
3. 用户 (1:1) 钱包 - 用户拥有钱包
4. 用户 (1:N) 地址 - 用户拥有多个地址
5. 用户 (1:N) 学校 - 用户管理多个学校
6. 用户 (1:N) 跑腿申请 - 用户可多次申请跑腿
7. 用户 (1:N) 提现记录 - 用户有多个提现记录
8. 学校 (1:N) 微信用户 - 学校绑定多个微信用户
9. 学校 (1:N) 学校区域 - 学校有多个区域
10. 学校 (1:N) 标签 - 学校有多个标签
11. 学校 (1:N) 订单 - 学校有多个订单
12. 微信用户 (1:N) 订单 - 微信用户下多个订单（下单用户编号）
13. 微信用户 (1:N) 订单 - 微信用户接多个订单（跑腿员编号）
14. 订单 (1:1) 订单支付 - 订单有一个支付记录
15. 订单 (1:1) 订单进度 - 订单有一个进度记录
16. 订单 (1:N) 订单聊天 - 订单有多个聊天记录
17. 订单 (1:N) 订单申诉 - 订单有多个申诉记录
18. 订单 (1:N) 订单附件 - 订单有多个附件
19. 订单 (1:N) 资金流水 - 订单产生多个资金流水
20. 权限 (M:N) 角色权限 - 权限和角色多对多关系

实体类型说明：
- 强实体：用户、学校、订单、标签、权限等
- 弱实体：钱包、订单支付、订单进度（依赖其他实体存在）
- 关联实体：角色权限（多对多关系的中间表）
*/

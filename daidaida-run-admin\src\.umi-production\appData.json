{"cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "pkg": {"name": "ant-design-pro", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/charts": "^2.2.6", "@ant-design/icons": "^4.8.1", "@ant-design/pro-components": "^2.6.48", "@umijs/route-utils": "^2.2.2", "antd": "^5.13.2", "antd-style": "^3.6.1", "classnames": "^2.5.1", "lodash": "^4.17.21", "moment": "^2.30.1", "omit.js": "^2.0.2", "querystring": "^0.2.1", "rc-menu": "^9.12.4", "rc-util": "^5.38.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0"}, "devDependencies": {"@ant-design/pro-cli": "^3.3.0", "@testing-library/react": "^13.4.0", "@types/classnames": "^2.3.1", "@types/express": "^4.17.21", "@types/history": "^4.7.11", "@types/jest": "^29.5.11", "@types/lodash": "^4.14.202", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-helmet": "^6.1.11", "@umijs/fabric": "^2.14.1", "@umijs/lint": "^4.1.1", "@umijs/max": "^4.1.1", "cross-env": "^7.0.3", "eslint": "^8.56.0", "express": "^4.18.2", "gh-pages": "^3.2.3", "husky": "^7.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "prettier": "^2.8.8", "react-dev-inspector": "^1.9.0", "swagger-ui-dist": "^4.19.1", "ts-node": "^10.9.2", "typescript": "^5.3.3", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=12.0.0"}}, "pkgPath": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "preset", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 35}, "enableBy": "register", "type": "preset", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/max/dist/preset": {"config": {}, "time": {"hooks": {}, "register": 8}, "enableBy": "register", "type": "preset", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/max/dist/preset.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/max/dist/preset", "key": "preset"}, "umi-presets-pro": {"config": {}, "time": {"hooks": {"onStart": [2]}, "register": 4}, "enableBy": "register", "type": "preset", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/umi-presets-pro/dist/index.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "umi-presets-pro", "key": "umiPresetsPro"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [107]}, "register": 22}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 12}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\react", "react-dom": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\react-dom", "react-router": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\react-router", "react-router-dom": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 68}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 134}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {}, "register": 15}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {}, "register": 31}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 3}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 8}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 27}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 8}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 41}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {}, "register": 62}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 19}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/plugins/dist/access": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/access.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/access", "key": "access"}, "./node_modules/@umijs/plugins/dist/analytics": {"config": {"onChange": "reload"}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/analytics.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/analytics", "key": "analytics"}, "./node_modules/@umijs/plugins/dist/antd": {"config": {}, "time": {"hooks": {"modifyConfig": [2], "modifyAppData": [0]}, "register": 7}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/antd.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/antd", "key": "antd"}, "./node_modules/@umijs/plugins/dist/dva": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/dva.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/dva", "key": "dva"}, "./node_modules/@umijs/plugins/dist/initial-state": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/initial-state.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/initial-state", "key": "initialState"}, "./node_modules/@umijs/plugins/dist/layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {"modifyConfig": [0], "addLayouts": [0], "modifyAppData": [0]}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/layout.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/layout", "key": "layout"}, "./node_modules/@umijs/plugins/dist/locale": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/locale.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/locale", "key": "locale"}, "./node_modules/@umijs/plugins/dist/mf": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/mf.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/mf", "key": "mf"}, "./node_modules/@umijs/plugins/dist/model": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/model.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/model", "key": "model"}, "./node_modules/@umijs/plugins/dist/moment2dayjs": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/moment2dayjs.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/moment2dayjs", "key": "moment2dayjs"}, "./node_modules/@umijs/plugins/dist/qiankun": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/qiankun.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/qiankun", "key": "qiankun"}, "./node_modules/@umijs/plugins/dist/qiankun/master": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/qiankun/master.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/qiankun/master", "key": "<PERSON><PERSON><PERSON>n-master"}, "./node_modules/@umijs/plugins/dist/qiankun/slave": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/qiankun/slave.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/qiankun/slave", "key": "qiankun-slave"}, "./node_modules/@umijs/plugins/dist/react-query": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/react-query.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/react-query", "key": "reactQuery"}, "./node_modules/@umijs/plugins/dist/request": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/request.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/request", "key": "request"}, "./node_modules/@umijs/plugins/dist/styled-components": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/styled-components.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/styled-components", "key": "styledComponents"}, "./node_modules/@umijs/plugins/dist/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/tailwindcss.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/tailwindcss", "key": "tailwindcss"}, "./node_modules/@umijs/plugins/dist/valtio": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/plugins/dist/valtio.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/plugins/dist/valtio", "key": "valtio"}, "./node_modules/@umijs/max/dist/plugins/maxAlias": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/max/dist/plugins/maxAlias.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/max/dist/plugins/maxAlias", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/max/dist/plugins/maxAppData": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/max/dist/plugins/maxAppData.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/max/dist/plugins/maxAppData", "key": "maxAppData"}, "./node_modules/@umijs/max/dist/plugins/maxChecker": {"config": {}, "time": {"hooks": {"onCheckPkgJSON": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/max/dist/plugins/maxChecker.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/max/dist/plugins/maxChecker", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/umi-presets-pro/dist/features/proconfig": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/umi-presets-pro/dist/features/proconfig.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/umi-presets-pro/dist/features/proconfig", "key": "proconfig"}, "./node_modules/umi-presets-pro/dist/features/maxtabs": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/umi-presets-pro/dist/features/maxtabs.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/umi-presets-pro/dist/features/maxtabs", "key": "maxtabs"}, "@umijs/max-plugin-openapi": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 281}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/max-plugin-openapi/dist/index.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "@umijs/max-plugin-openapi", "key": "openAPI"}, "./node_modules/@alita/plugins/dist/keepalive": {"config": {}, "time": {"hooks": {}, "register": 165}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@alita/plugins/dist/keepalive.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@alita/plugins/dist/keepalive", "key": "keepalive"}, "./node_modules/@alita/plugins/dist/tabs-layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/node_modules/@alita/plugins/dist/tabs-layout.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@alita/plugins/dist/tabs-layout", "key": "tabsLayout"}, "@umijs/request-record": {"config": {"default": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}}, "time": {"hooks": {"modifyConfig": [0], "modifyAppData": [0]}, "register": 61}, "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/request-record/dist/cjs/index.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "@umijs/request-record", "key": "requestRecord"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "build", "args": {"_": []}, "userConfig": {"hash": true, "routes": [{"path": "/user", "layout": false, "routes": [{"name": "login", "path": "/user/login", "component": "./User/Login"}]}, {"path": "/welcome", "name": "welcome", "icon": "smile", "component": "./Welcome"}, {"path": "/admin", "name": "admin", "icon": "crown", "access": "canAdmin", "routes": [{"path": "/admin", "redirect": "/admin/sub-page"}, {"path": "/admin/sub-page", "name": "sub-page", "component": "./Admin"}]}, {"name": "user-manage", "icon": "user", "path": "/userManage", "routes": [{"path": "/userManage/pc", "name": "pc-user", "component": "./userManage/pc", "perms": "system:userpc:view", "access": "routeFilter"}, {"path": "/userManage/xcx", "name": "mini-program-user", "component": "./userManage/xcx", "perms": "system:userwx:view", "access": "routeFilter"}]}, {"name": "system", "icon": "setting", "path": "/system", "routes": [{"path": "/system/config", "name": "configuration", "component": "./system/config", "perms": "system:system:config:view", "access": "routeFilter"}, {"path": "/system/permission", "name": "permission", "component": "./system/permission", "perms": "system:system:perms:view", "access": "routeFilter"}, {"path": "/system/monitor", "name": "monitor", "component": "./system/monitor", "perms": "system:system:monitor:view", "access": "routeFilter"}, {"path": "/system/carousel", "name": "carousel", "component": "./system/carousel", "perms": "system:system:carousel:view", "access": "routeFilter"}]}, {"name": "order", "icon": "fileText", "path": "/order", "routes": [{"path": "/order", "component": "./order/index", "perms": "order:order:view", "access": "routeFilter"}, {"path": "/order/detail/:id", "component": "./order/detail", "perms": "order:detail:view", "access": "routeFilter"}]}, {"name": "tag", "icon": "book", "path": "/tag", "component": "./tag", "perms": "order:tag:view", "access": "routeFilter"}, {"name": "workorder", "icon": "exception", "path": "/workorder", "routes": [{"path": "/workorder/runnerApply", "name": "runner-application", "component": "./workorder/runnerApply", "perms": "runnerApply:view", "access": "routeFilter"}, {"path": "/workorder/orderAppeal", "name": "order-appeal", "component": "./workorder/orderAppeal", "perms": "order:appeal:view", "access": "routeFilter"}]}, {"name": "capital", "icon": "propertySafety", "path": "/capital", "routes": [{"path": "/capital/wallet", "name": "wallet", "component": "./capital/wallet", "perms": "payment:withdraw:view", "access": "routeFilter"}, {"path": "/capital/withdrawal", "name": "withdrawal", "component": "./capital/withdrawal", "perms": "payment:recode:view", "access": "routeFilter"}, {"path": "/capital/flow", "name": "capital-flow", "component": "./capital/flow", "perms": "payment:flow:view", "access": "routeFilter"}, {"path": "/capital/mywallet", "name": "my-wallet", "component": "./capital/mywallet", "perms": "payment:mywallet:view", "access": "routeFilter"}, {"path": "/capital/myflow", "name": "my-flow", "component": "./capital/myflow", "perms": "payment:myflow:view", "access": "routeFilter"}]}, {"name": "school", "icon": "home", "path": "/school", "routes": [{"path": "/school/campus", "name": "campus", "component": "./school/campus", "perms": "address:school:view", "access": "routeFilter"}, {"path": "/school/region", "name": "region", "component": "./school/region", "perms": "address:region:view", "access": "routeFilter"}]}, {"name": "oss", "icon": "star", "path": "/oss", "routes": [{"path": "/oss/manage", "name": "oss-manage", "component": "./oss/manage", "perms": "oss:oss:view", "access": "routeFilter"}, {"path": "/oss/config", "name": "oss-config", "component": "./oss/config", "perms": "oss:config:view", "access": "routeFilter"}]}, {"name": "个人中心", "icon": "star", "path": "/profile", "hideInMenu": true, "component": "./User/profile"}, {"path": "/", "redirect": "/welcome"}, {"path": "*", "layout": false, "component": "./404"}], "theme": {"root-entry-name": "variable"}, "ignoreMomentLocale": true, "proxy": {"/dev/": {"target": "http://localhost:8081", "changeOrigin": true, "pathRewrite": {"^/dev/": ""}}}, "fastRefresh": true, "model": {}, "initialState": {}, "title": "Daidaida", "layout": {"locale": true, "navTheme": "light", "colorPrimary": "#722ED1", "layout": "top", "contentWidth": "Fluid", "fixedHeader": true, "fixSiderbar": true, "pwa": true, "title": "代代达跑腿工作室", "logo": "https://cdn.daidaida.xyz/daidaida888/2025/05/26/ff99290effe84530a5a7920aff2eb8a8jpg", "token": {}, "splitMenus": false, "siderMenuType": "sub"}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "locale": {"default": "zh-CN", "antd": true, "baseNavigator": true}, "antd": {"configProvider": {"componentSize": "middle", "strict": false}}, "request": {}, "access": {}, "headScripts": [{"src": "/scripts/loading.js", "async": true}], "presets": ["umi-presets-pro"], "openAPI": [{"requestLibPath": "import { request } from 'umi'", "schemaPath": "http://localhost:8081/v3/api-docs", "projectName": "test-swagger"}], "mfsu": {"strategy": "normal"}, "esbuildMinifyIIFE": true, "requestRecord": {}}, "mainConfigFile": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\config\\config.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/", "svgr": {}, "publicPath": "/", "mfsu": {"strategy": "normal"}, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\react", "react-dom": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\react-dom", "react-router": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\react-router", "react-router-dom": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\react-router-dom", "@": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src", "@@": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/src/.umi-production", "regenerator-runtime": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\@umijs\\preset-umi\\node_modules\\regenerator-runtime", "antd": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\antd", "moment": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\dayjs", "@umijs/max": "@@/exports"}, "requestRecord": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}, "hash": true, "routes": [{"path": "/user", "layout": false, "routes": [{"name": "login", "path": "/user/login", "component": "./User/Login"}]}, {"path": "/welcome", "name": "welcome", "icon": "smile", "component": "./Welcome"}, {"path": "/admin", "name": "admin", "icon": "crown", "access": "canAdmin", "routes": [{"path": "/admin", "redirect": "/admin/sub-page"}, {"path": "/admin/sub-page", "name": "sub-page", "component": "./Admin"}]}, {"name": "user-manage", "icon": "user", "path": "/userManage", "routes": [{"path": "/userManage/pc", "name": "pc-user", "component": "./userManage/pc", "perms": "system:userpc:view", "access": "routeFilter"}, {"path": "/userManage/xcx", "name": "mini-program-user", "component": "./userManage/xcx", "perms": "system:userwx:view", "access": "routeFilter"}]}, {"name": "system", "icon": "setting", "path": "/system", "routes": [{"path": "/system/config", "name": "configuration", "component": "./system/config", "perms": "system:system:config:view", "access": "routeFilter"}, {"path": "/system/permission", "name": "permission", "component": "./system/permission", "perms": "system:system:perms:view", "access": "routeFilter"}, {"path": "/system/monitor", "name": "monitor", "component": "./system/monitor", "perms": "system:system:monitor:view", "access": "routeFilter"}, {"path": "/system/carousel", "name": "carousel", "component": "./system/carousel", "perms": "system:system:carousel:view", "access": "routeFilter"}]}, {"name": "order", "icon": "fileText", "path": "/order", "routes": [{"path": "/order", "component": "./order/index", "perms": "order:order:view", "access": "routeFilter"}, {"path": "/order/detail/:id", "component": "./order/detail", "perms": "order:detail:view", "access": "routeFilter"}]}, {"name": "tag", "icon": "book", "path": "/tag", "component": "./tag", "perms": "order:tag:view", "access": "routeFilter"}, {"name": "workorder", "icon": "exception", "path": "/workorder", "routes": [{"path": "/workorder/runnerApply", "name": "runner-application", "component": "./workorder/runnerApply", "perms": "runnerApply:view", "access": "routeFilter"}, {"path": "/workorder/orderAppeal", "name": "order-appeal", "component": "./workorder/orderAppeal", "perms": "order:appeal:view", "access": "routeFilter"}]}, {"name": "capital", "icon": "propertySafety", "path": "/capital", "routes": [{"path": "/capital/wallet", "name": "wallet", "component": "./capital/wallet", "perms": "payment:withdraw:view", "access": "routeFilter"}, {"path": "/capital/withdrawal", "name": "withdrawal", "component": "./capital/withdrawal", "perms": "payment:recode:view", "access": "routeFilter"}, {"path": "/capital/flow", "name": "capital-flow", "component": "./capital/flow", "perms": "payment:flow:view", "access": "routeFilter"}, {"path": "/capital/mywallet", "name": "my-wallet", "component": "./capital/mywallet", "perms": "payment:mywallet:view", "access": "routeFilter"}, {"path": "/capital/myflow", "name": "my-flow", "component": "./capital/myflow", "perms": "payment:myflow:view", "access": "routeFilter"}]}, {"name": "school", "icon": "home", "path": "/school", "routes": [{"path": "/school/campus", "name": "campus", "component": "./school/campus", "perms": "address:school:view", "access": "routeFilter"}, {"path": "/school/region", "name": "region", "component": "./school/region", "perms": "address:region:view", "access": "routeFilter"}]}, {"name": "oss", "icon": "star", "path": "/oss", "routes": [{"path": "/oss/manage", "name": "oss-manage", "component": "./oss/manage", "perms": "oss:oss:view", "access": "routeFilter"}, {"path": "/oss/config", "name": "oss-config", "component": "./oss/config", "perms": "oss:config:view", "access": "routeFilter"}]}, {"name": "个人中心", "icon": "star", "path": "/profile", "hideInMenu": true, "component": "./User/profile"}, {"path": "/", "redirect": "/welcome"}, {"path": "*", "layout": false, "component": "./404"}], "theme": {"blue-base": "#1890ff", "blue-1": "#e6f7ff", "blue-2": "#bae7ff", "blue-3": "#91d5ff", "blue-4": "#69c0ff", "blue-5": "#40a9ff", "blue-6": "#1890ff", "blue-7": "#096dd9", "blue-8": "#0050b3", "blue-9": "#003a8c", "blue-10": "#002766", "purple-base": "#722ed1", "purple-1": "#f9f0ff", "purple-2": "#efdbff", "purple-3": "#d3adf7", "purple-4": "#b37feb", "purple-5": "#9254de", "purple-6": "#722ed1", "purple-7": "#531dab", "purple-8": "#391085", "purple-9": "#22075e", "purple-10": "#120338", "cyan-base": "#13c2c2", "cyan-1": "#e6fffb", "cyan-2": "#b5f5ec", "cyan-3": "#87e8de", "cyan-4": "#5cdbd3", "cyan-5": "#36cfc9", "cyan-6": "#13c2c2", "cyan-7": "#08979c", "cyan-8": "#006d75", "cyan-9": "#00474f", "cyan-10": "#002329", "green-base": "#52c41a", "green-1": "#f6ffed", "green-2": "#d9f7be", "green-3": "#b7eb8f", "green-4": "#95de64", "green-5": "#73d13d", "green-6": "#52c41a", "green-7": "#389e0d", "green-8": "#237804", "green-9": "#135200", "green-10": "#092b00", "magenta-base": "#eb2f96", "magenta-1": "#fff0f6", "magenta-2": "#ffd6e7", "magenta-3": "#ffadd2", "magenta-4": "#ff85c0", "magenta-5": "#f759ab", "magenta-6": "#eb2f96", "magenta-7": "#c41d7f", "magenta-8": "#9e1068", "magenta-9": "#780650", "magenta-10": "#520339", "pink-base": "#eb2f96", "pink-1": "#fff0f6", "pink-2": "#ffd6e7", "pink-3": "#ffadd2", "pink-4": "#ff85c0", "pink-5": "#f759ab", "pink-6": "#eb2f96", "pink-7": "#c41d7f", "pink-8": "#9e1068", "pink-9": "#780650", "pink-10": "#520339", "red-base": "#f5222d", "red-1": "#fff1f0", "red-2": "#ffccc7", "red-3": "#ffa39e", "red-4": "#ff7875", "red-5": "#ff4d4f", "red-6": "#f5222d", "red-7": "#cf1322", "red-8": "#a8071a", "red-9": "#820014", "red-10": "#5c0011", "orange-base": "#fa8c16", "orange-1": "#fff7e6", "orange-2": "#ffe7ba", "orange-3": "#ffd591", "orange-4": "#ffc069", "orange-5": "#ffa940", "orange-6": "#fa8c16", "orange-7": "#d46b08", "orange-8": "#ad4e00", "orange-9": "#873800", "orange-10": "#612500", "yellow-base": "#fadb14", "yellow-1": "#feffe6", "yellow-2": "#ffffb8", "yellow-3": "#fffb8f", "yellow-4": "#fff566", "yellow-5": "#ffec3d", "yellow-6": "#fadb14", "yellow-7": "#d4b106", "yellow-8": "#ad8b00", "yellow-9": "#876800", "yellow-10": "#614700", "volcano-base": "#fa541c", "volcano-1": "#fff2e8", "volcano-2": "#ffd8bf", "volcano-3": "#ffbb96", "volcano-4": "#ff9c6e", "volcano-5": "#ff7a45", "volcano-6": "#fa541c", "volcano-7": "#d4380d", "volcano-8": "#ad2102", "volcano-9": "#871400", "volcano-10": "#610b00", "geekblue-base": "#2f54eb", "geekblue-1": "#f0f5ff", "geekblue-2": "#d6e4ff", "geekblue-3": "#adc6ff", "geekblue-4": "#85a5ff", "geekblue-5": "#597ef7", "geekblue-6": "#2f54eb", "geekblue-7": "#1d39c4", "geekblue-8": "#10239e", "geekblue-9": "#061178", "geekblue-10": "#030852", "lime-base": "#a0d911", "lime-1": "#fcffe6", "lime-2": "#f4ffb8", "lime-3": "#eaff8f", "lime-4": "#d3f261", "lime-5": "#bae637", "lime-6": "#a0d911", "lime-7": "#7cb305", "lime-8": "#5b8c00", "lime-9": "#3f6600", "lime-10": "#254000", "gold-base": "#faad14", "gold-1": "#fffbe6", "gold-2": "#fff1b8", "gold-3": "#ffe58f", "gold-4": "#ffd666", "gold-5": "#ffc53d", "gold-6": "#faad14", "gold-7": "#d48806", "gold-8": "#ad6800", "gold-9": "#874d00", "gold-10": "#613400", "preset-colors": "pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,", "theme": "default", "ant-prefix": "ant", "html-selector": "html", "primary-color": "#1890ff", "primary-color-hover": "#40a9ff", "primary-color-active": "#096dd9", "primary-color-outline": "rgba(24, 144, 255, 0.2)", "processing-color": "#1890ff", "info-color": "#1890ff", "info-color-deprecated-bg": "#e6f7ff", "info-color-deprecated-border": "#91d5ff", "success-color": "#52c41a", "success-color-hover": "#73d13d", "success-color-active": "#389e0d", "success-color-outline": "rgba(82, 196, 26, 0.2)", "success-color-deprecated-bg": "#f6ffed", "success-color-deprecated-border": "#b7eb8f", "warning-color": "#faad14", "warning-color-hover": "#ffc53d", "warning-color-active": "#d48806", "warning-color-outline": "rgba(250, 173, 20, 0.2)", "warning-color-deprecated-bg": "#fffbe6", "warning-color-deprecated-border": "#ffe58f", "error-color": "#ff4d4f", "error-color-hover": "#ff7875", "error-color-active": "#d9363e", "error-color-outline": "rgba(255, 77, 79, 0.2)", "error-color-deprecated-bg": "#fff2f0", "error-color-deprecated-border": "#ffccc7", "highlight-color": "#ff4d4f", "normal-color": "#d9d9d9", "white": "#fff", "black": "#000", "primary-1": "#e6f7ff", "primary-2": "#bae7ff", "primary-3": "#91d5ff", "primary-4": "#69c0ff", "primary-5": "#40a9ff", "primary-6": "#1890ff", "primary-7": "#096dd9", "primary-8": "#0050b3", "primary-9": "#003a8c", "primary-10": "#002766", "component-background": "#fff", "popover-background": "#fff", "popover-customize-border-color": "#f0f0f0", "font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "code-family": "'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace", "text-color": "rgba(0, 0, 0, 0.85)", "text-color-secondary": "rgba(0, 0, 0, 0.45)", "text-color-inverse": "#fff", "icon-color": "inherit", "icon-color-hover": "rgba(0, 0, 0, 0.75)", "heading-color": "rgba(0, 0, 0, 0.85)", "text-color-dark": "rgba(255, 255, 255, 0.85)", "text-color-secondary-dark": "rgba(255, 255, 255, 0.65)", "text-selection-bg": "#1890ff", "font-variant-base": "tabular-nums", "font-feature-settings-base": "tnum", "font-size-base": "14px", "font-size-lg": "16px", "font-size-sm": "12px", "heading-1-size": "38px", "heading-2-size": "30px", "heading-3-size": "24px", "heading-4-size": "20px", "heading-5-size": "16px", "line-height-base": "1.5715", "border-radius-base": "2px", "border-radius-sm": "2px", "control-border-radius": "2px", "arrow-border-radius": "2px", "padding-lg": "24px", "padding-md": "16px", "padding-sm": "12px", "padding-xs": "8px", "padding-xss": "4px", "control-padding-horizontal": "12px", "control-padding-horizontal-sm": "8px", "margin-lg": "24px", "margin-md": "16px", "margin-sm": "12px", "margin-xs": "8px", "margin-xss": "4px", "height-base": "32px", "height-lg": "40px", "height-sm": "24px", "item-active-bg": "#e6f7ff", "item-hover-bg": "#f5f5f5", "iconfont-css-prefix": "anticon", "link-color": "#1890ff", "link-hover-color": "#40a9ff", "link-active-color": "#096dd9", "link-decoration": "none", "link-hover-decoration": "none", "link-focus-decoration": "none", "link-focus-outline": "0", "ease-base-out": "cubic-bezier(0.7, 0.3, 0.1, 1)", "ease-base-in": "cubic-bezier(0.9, 0, 0.3, 0.7)", "ease-out": "cubic-bezier(0.215, 0.61, 0.355, 1)", "ease-in": "cubic-bezier(0.55, 0.055, 0.675, 0.19)", "ease-in-out": "cubic-bezier(0.645, 0.045, 0.355, 1)", "ease-out-back": "cubic-bezier(0.12, 0.4, 0.29, 1.46)", "ease-in-back": "cubic-bezier(0.71, -0.46, 0.88, 0.6)", "ease-in-out-back": "cubic-bezier(0.71, -0.46, 0.29, 1.46)", "ease-out-circ": "cubic-bezier(0.08, 0.82, 0.17, 1)", "ease-in-circ": "cubic-bezier(0.6, 0.04, 0.98, 0.34)", "ease-in-out-circ": "cubic-bezier(0.78, 0.14, 0.15, 0.86)", "ease-out-quint": "cubic-bezier(0.23, 1, 0.32, 1)", "ease-in-quint": "cubic-bezier(0.755, 0.05, 0.855, 0.06)", "ease-in-out-quint": "cubic-bezier(0.86, 0, 0.07, 1)", "border-color-base": "#d9d9d9", "border-color-split": "#f0f0f0", "border-color-inverse": "#fff", "border-width-base": "1px", "border-style-base": "solid", "outline-blur-size": "0", "outline-width": "2px", "outline-color": "#1890ff", "outline-fade": "20%", "background-color-light": "#fafafa", "background-color-base": "#f5f5f5", "disabled-color": "rgba(0, 0, 0, 0.25)", "disabled-bg": "#f5f5f5", "disabled-active-bg": "#e6e6e6", "disabled-color-dark": "rgba(255, 255, 255, 0.35)", "shadow-color": "rgba(0, 0, 0, 0.15)", "shadow-color-inverse": "#fff", "box-shadow-base": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "shadow-1-up": "0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-down": "0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-left": "-6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-right": "6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-2": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "btn-font-weight": "400", "btn-border-radius-base": "2px", "btn-border-radius-sm": "2px", "btn-border-width": "1px", "btn-border-style": "solid", "btn-shadow": "0 2px 0 rgba(0, 0, 0, 0.015)", "btn-primary-shadow": "0 2px 0 rgba(0, 0, 0, 0.045)", "btn-text-shadow": "0 -1px 0 rgba(0, 0, 0, 0.12)", "btn-primary-color": "#fff", "btn-primary-bg": "#1890ff", "btn-default-color": "rgba(0, 0, 0, 0.85)", "btn-default-bg": "#fff", "btn-default-border": "#d9d9d9", "btn-danger-color": "#fff", "btn-danger-bg": "#ff4d4f", "btn-danger-border": "#ff4d4f", "btn-disable-color": "rgba(0, 0, 0, 0.25)", "btn-disable-bg": "#f5f5f5", "btn-disable-border": "#d9d9d9", "btn-default-ghost-color": "#fff", "btn-default-ghost-bg": "transparent", "btn-default-ghost-border": "#fff", "btn-font-size-lg": "16px", "btn-font-size-sm": "14px", "btn-padding-horizontal-base": "15px", "btn-padding-horizontal-lg": "15px", "btn-padding-horizontal-sm": "7px", "btn-height-base": "32px", "btn-height-lg": "40px", "btn-height-sm": "24px", "btn-line-height": "1.5715", "btn-circle-size": "32px", "btn-circle-size-lg": "40px", "btn-circle-size-sm": "24px", "btn-square-size": "32px", "btn-square-size-lg": "40px", "btn-square-size-sm": "24px", "btn-square-only-icon-size": "16px", "btn-square-only-icon-size-sm": "14px", "btn-square-only-icon-size-lg": "18px", "btn-group-border": "#40a9ff", "btn-link-hover-bg": "transparent", "btn-text-hover-bg": "rgba(0, 0, 0, 0.018)", "checkbox-size": "16px", "checkbox-color": "#1890ff", "checkbox-check-color": "#fff", "checkbox-check-bg": "#fff", "checkbox-border-width": "1px", "checkbox-border-radius": "2px", "checkbox-group-item-margin-right": "8px", "descriptions-bg": "#fafafa", "descriptions-title-margin-bottom": "20px", "descriptions-default-padding": "16px 24px", "descriptions-middle-padding": "12px 24px", "descriptions-small-padding": "8px 16px", "descriptions-item-padding-bottom": "16px", "descriptions-item-trailing-colon": "true", "descriptions-item-label-colon-margin-right": "8px", "descriptions-item-label-colon-margin-left": "2px", "descriptions-extra-color": "rgba(0, 0, 0, 0.85)", "divider-text-padding": "1em", "divider-orientation-margin": "5%", "divider-color": "rgba(0, 0, 0, 0.06)", "divider-vertical-gutter": "8px", "dropdown-selected-color": "#1890ff", "dropdown-menu-submenu-disabled-bg": "#fff", "dropdown-selected-bg": "#e6f7ff", "empty-font-size": "14px", "radio-size": "16px", "radio-top": "0.2em", "radio-border-width": "1px", "radio-dot-size": "8px", "radio-dot-color": "#1890ff", "radio-dot-disabled-color": "rgba(0, 0, 0, 0.2)", "radio-solid-checked-color": "#fff", "radio-button-bg": "#fff", "radio-button-checked-bg": "#fff", "radio-button-color": "rgba(0, 0, 0, 0.85)", "radio-button-hover-color": "#40a9ff", "radio-button-active-color": "#096dd9", "radio-button-padding-horizontal": "15px", "radio-disabled-button-checked-bg": "#e6e6e6", "radio-disabled-button-checked-color": "rgba(0, 0, 0, 0.25)", "radio-wrapper-margin-right": "8px", "screen-xs": "480px", "screen-xs-min": "480px", "screen-sm": "576px", "screen-sm-min": "576px", "screen-md": "768px", "screen-md-min": "768px", "screen-lg": "992px", "screen-lg-min": "992px", "screen-xl": "1200px", "screen-xl-min": "1200px", "screen-xxl": "1600px", "screen-xxl-min": "1600px", "screen-xs-max": "575px", "screen-sm-max": "767px", "screen-md-max": "991px", "screen-lg-max": "1199px", "screen-xl-max": "1599px", "grid-columns": "24", "layout-header-background": "#001529", "layout-header-height": "64px", "layout-header-padding": "0 50px", "layout-header-color": "rgba(0, 0, 0, 0.85)", "layout-footer-padding": "24px 50px", "layout-footer-background": "#f0f2f5", "layout-sider-background": "#001529", "layout-trigger-height": "48px", "layout-trigger-background": "#002140", "layout-trigger-color": "#fff", "layout-zero-trigger-width": "36px", "layout-zero-trigger-height": "42px", "layout-sider-background-light": "#fff", "layout-trigger-background-light": "#fff", "layout-trigger-color-light": "rgba(0, 0, 0, 0.85)", "zindex-badge": "auto", "zindex-table-fixed": "2", "zindex-affix": "10", "zindex-back-top": "10", "zindex-picker-panel": "10", "zindex-popup-close": "10", "zindex-modal": "1000", "zindex-modal-mask": "1000", "zindex-message": "1010", "zindex-notification": "1010", "zindex-popover": "1030", "zindex-dropdown": "1050", "zindex-picker": "1050", "zindex-popoconfirm": "1060", "zindex-tooltip": "1070", "zindex-image": "1080", "animation-duration-slow": "0.3s", "animation-duration-base": "0.2s", "animation-duration-fast": "0.1s", "collapse-panel-border-radius": "2px", "dropdown-menu-bg": "#fff", "dropdown-vertical-padding": "5px", "dropdown-edge-child-vertical-padding": "4px", "dropdown-font-size": "14px", "dropdown-line-height": "22px", "label-required-color": "#ff4d4f", "label-color": "rgba(0, 0, 0, 0.85)", "form-warning-input-bg": "#fff", "form-item-margin-bottom": "24px", "form-item-trailing-colon": "true", "form-vertical-label-padding": "0 0 8px", "form-vertical-label-margin": "0", "form-item-label-font-size": "14px", "form-item-label-height": "32px", "form-item-label-colon-margin-right": "8px", "form-item-label-colon-margin-left": "2px", "form-error-input-bg": "#fff", "input-height-base": "32px", "input-height-lg": "40px", "input-height-sm": "24px", "input-padding-horizontal": "11px", "input-padding-horizontal-base": "11px", "input-padding-horizontal-sm": "7px", "input-padding-horizontal-lg": "11px", "input-padding-vertical-base": "4px", "input-padding-vertical-sm": "0px", "input-padding-vertical-lg": "6.5px", "input-placeholder-color": "#bfbfbf", "input-color": "rgba(0, 0, 0, 0.85)", "input-icon-color": "rgba(0, 0, 0, 0.85)", "input-border-color": "#d9d9d9", "input-bg": "#fff", "input-number-hover-border-color": "#40a9ff", "input-number-handler-active-bg": "#f4f4f4", "input-number-handler-hover-bg": "#40a9ff", "input-number-handler-bg": "#fff", "input-number-handler-border-color": "#d9d9d9", "input-addon-bg": "#fafafa", "input-hover-border-color": "#40a9ff", "input-disabled-bg": "#f5f5f5", "input-outline-offset": "0 0", "input-icon-hover-color": "rgba(0, 0, 0, 0.85)", "input-disabled-color": "rgba(0, 0, 0, 0.25)", "mentions-dropdown-bg": "#fff", "mentions-dropdown-menu-item-hover-bg": "#fff", "select-border-color": "#d9d9d9", "select-item-selected-color": "rgba(0, 0, 0, 0.85)", "select-item-selected-font-weight": "600", "select-dropdown-bg": "#fff", "select-item-selected-bg": "#e6f7ff", "select-item-active-bg": "#f5f5f5", "select-dropdown-vertical-padding": "5px", "select-dropdown-font-size": "14px", "select-dropdown-line-height": "22px", "select-dropdown-height": "32px", "select-background": "#fff", "select-clear-background": "#fff", "select-selection-item-bg": "#f5f5f5", "select-selection-item-border-color": "#f0f0f0", "select-single-item-height-lg": "40px", "select-multiple-item-height": "24px", "select-multiple-item-height-lg": "32px", "select-multiple-item-spacing-half": "2px", "select-multiple-disabled-background": "#f5f5f5", "select-multiple-item-disabled-color": "#bfbfbf", "select-multiple-item-disabled-border-color": "#d9d9d9", "cascader-bg": "#fff", "cascader-item-selected-bg": "#e6f7ff", "cascader-menu-bg": "#fff", "cascader-menu-border-color-split": "#f0f0f0", "cascader-dropdown-vertical-padding": "5px", "cascader-dropdown-edge-child-vertical-padding": "4px", "cascader-dropdown-font-size": "14px", "cascader-dropdown-line-height": "22px", "anchor-bg": "transparent", "anchor-border-color": "#f0f0f0", "anchor-link-top": "4px", "anchor-link-left": "16px", "anchor-link-padding": "4px 0 4px 16px", "tooltip-max-width": "250px", "tooltip-color": "#fff", "tooltip-bg": "rgba(0, 0, 0, 0.75)", "tooltip-arrow-width": "11.3137085px", "tooltip-distance": "14.3137085px", "tooltip-arrow-color": "rgba(0, 0, 0, 0.75)", "tooltip-border-radius": "2px", "popover-bg": "#fff", "popover-color": "rgba(0, 0, 0, 0.85)", "popover-min-width": "177px", "popover-min-height": "32px", "popover-arrow-width": "11.3137085px", "popover-arrow-color": "#fff", "popover-arrow-outer-color": "#fff", "popover-distance": "15.3137085px", "popover-padding-horizontal": "16px", "modal-header-padding-vertical": "16px", "modal-header-padding-horizontal": "24px", "modal-header-bg": "#fff", "modal-header-padding": "16px 24px", "modal-header-border-width": "1px", "modal-header-border-style": "solid", "modal-header-title-line-height": "22px", "modal-header-title-font-size": "16px", "modal-header-border-color-split": "#f0f0f0", "modal-header-close-size": "54px", "modal-content-bg": "#fff", "modal-heading-color": "rgba(0, 0, 0, 0.85)", "modal-close-color": "rgba(0, 0, 0, 0.45)", "modal-footer-bg": "transparent", "modal-footer-border-color-split": "#f0f0f0", "modal-footer-border-style": "solid", "modal-footer-padding-vertical": "10px", "modal-footer-padding-horizontal": "16px", "modal-footer-border-width": "1px", "modal-mask-bg": "rgba(0, 0, 0, 0.45)", "modal-confirm-title-font-size": "16px", "modal-border-radius": "2px", "progress-default-color": "#1890ff", "progress-remaining-color": "#f5f5f5", "progress-info-text-color": "rgba(0, 0, 0, 0.85)", "progress-radius": "100px", "progress-steps-item-bg": "#f3f3f3", "progress-text-font-size": "1em", "progress-text-color": "rgba(0, 0, 0, 0.85)", "progress-circle-text-font-size": "1em", "menu-inline-toplevel-item-height": "40px", "menu-item-height": "40px", "menu-item-group-height": "1.5715", "menu-collapsed-width": "80px", "menu-bg": "#fff", "menu-popup-bg": "#fff", "menu-item-color": "rgba(0, 0, 0, 0.85)", "menu-inline-submenu-bg": "#fafafa", "menu-highlight-color": "#1890ff", "menu-highlight-danger-color": "#ff4d4f", "menu-item-active-bg": "#e6f7ff", "menu-item-active-danger-bg": "#fff1f0", "menu-item-active-border-width": "3px", "menu-item-group-title-color": "rgba(0, 0, 0, 0.45)", "menu-item-vertical-margin": "4px", "menu-item-font-size": "14px", "menu-item-boundary-margin": "8px", "menu-item-padding-horizontal": "20px", "menu-item-padding": "0 20px", "menu-horizontal-line-height": "46px", "menu-icon-margin-right": "10px", "menu-icon-size": "14px", "menu-icon-size-lg": "16px", "menu-item-group-title-font-size": "14px", "menu-dark-color": "rgba(255, 255, 255, 0.65)", "menu-dark-danger-color": "#ff4d4f", "menu-dark-bg": "#001529", "menu-dark-arrow-color": "#fff", "menu-dark-inline-submenu-bg": "#000c17", "menu-dark-highlight-color": "#fff", "menu-dark-item-active-bg": "#1890ff", "menu-dark-item-active-danger-bg": "#ff4d4f", "menu-dark-selected-item-icon-color": "#fff", "menu-dark-selected-item-text-color": "#fff", "menu-dark-item-hover-bg": "transparent", "spin-dot-size-sm": "14px", "spin-dot-size": "20px", "spin-dot-size-lg": "32px", "table-bg": "#fff", "table-header-bg": "#fafafa", "table-header-color": "rgba(0, 0, 0, 0.85)", "table-header-sort-bg": "#f5f5f5", "table-row-hover-bg": "#fafafa", "table-selected-row-color": "inherit", "table-selected-row-bg": "#e6f7ff", "table-selected-row-hover-bg": "#dcf4ff", "table-expanded-row-bg": "#fbfbfb", "table-padding-vertical": "16px", "table-padding-horizontal": "16px", "table-padding-vertical-md": "12px", "table-padding-horizontal-md": "8px", "table-padding-vertical-sm": "8px", "table-padding-horizontal-sm": "8px", "table-border-color": "#f0f0f0", "table-border-radius-base": "2px", "table-footer-bg": "#fafafa", "table-footer-color": "rgba(0, 0, 0, 0.85)", "table-header-bg-sm": "#fafafa", "table-font-size": "14px", "table-font-size-md": "14px", "table-font-size-sm": "14px", "table-header-cell-split-color": "rgba(0, 0, 0, 0.06)", "table-header-sort-active-bg": "rgba(0, 0, 0, 0.04)", "table-fixed-header-sort-active-bg": "#f5f5f5", "table-header-filter-active-bg": "rgba(0, 0, 0, 0.04)", "table-filter-btns-bg": "inherit", "table-filter-dropdown-bg": "#fff", "table-expand-icon-bg": "#fff", "table-selection-column-width": "32px", "table-sticky-scroll-bar-bg": "rgba(0, 0, 0, 0.35)", "table-sticky-scroll-bar-radius": "4px", "tag-border-radius": "2px", "tag-default-bg": "#fafafa", "tag-default-color": "rgba(0, 0, 0, 0.85)", "tag-font-size": "12px", "tag-line-height": "20px", "picker-bg": "#fff", "picker-basic-cell-hover-color": "#f5f5f5", "picker-basic-cell-active-with-range-color": "#e6f7ff", "picker-basic-cell-hover-with-range-color": "#cbe6ff", "picker-basic-cell-disabled-bg": "rgba(0, 0, 0, 0.04)", "picker-border-color": "#f0f0f0", "picker-date-hover-range-border-color": "#7ec1ff", "picker-date-hover-range-color": "#cbe6ff", "picker-time-panel-column-width": "56px", "picker-time-panel-column-height": "224px", "picker-time-panel-cell-height": "28px", "picker-panel-cell-height": "24px", "picker-panel-cell-width": "36px", "picker-text-height": "40px", "picker-panel-without-time-cell-height": "66px", "calendar-bg": "#fff", "calendar-input-bg": "#fff", "calendar-border-color": "#fff", "calendar-item-active-bg": "#e6f7ff", "calendar-column-active-bg": "rgba(230, 247, 255, 0.2)", "calendar-full-bg": "#fff", "calendar-full-panel-bg": "#fff", "carousel-dot-width": "16px", "carousel-dot-height": "3px", "carousel-dot-active-width": "24px", "badge-height": "20px", "badge-height-sm": "14px", "badge-dot-size": "6px", "badge-font-size": "12px", "badge-font-size-sm": "12px", "badge-font-weight": "normal", "badge-status-size": "6px", "badge-text-color": "#fff", "badge-color": "#ff4d4f", "rate-star-color": "#fadb14", "rate-star-bg": "#f0f0f0", "rate-star-size": "20px", "rate-star-hover-scale": "scale(1.1)", "card-head-color": "rgba(0, 0, 0, 0.85)", "card-head-background": "transparent", "card-head-font-size": "16px", "card-head-font-size-sm": "14px", "card-head-padding": "16px", "card-head-padding-sm": "8px", "card-head-height": "48px", "card-head-height-sm": "36px", "card-inner-head-padding": "12px", "card-padding-base": "24px", "card-padding-base-sm": "12px", "card-actions-background": "#fff", "card-actions-li-margin": "12px 0", "card-skeleton-bg": "#cfd8dc", "card-background": "#fff", "card-shadow": "0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09)", "card-radius": "2px", "card-head-tabs-margin-bottom": "-17px", "card-head-extra-color": "rgba(0, 0, 0, 0.85)", "comment-bg": "inherit", "comment-padding-base": "16px 0", "comment-nest-indent": "44px", "comment-font-size-base": "14px", "comment-font-size-sm": "12px", "comment-author-name-color": "rgba(0, 0, 0, 0.45)", "comment-author-time-color": "#ccc", "comment-action-color": "rgba(0, 0, 0, 0.45)", "comment-action-hover-color": "#595959", "comment-actions-margin-bottom": "inherit", "comment-actions-margin-top": "12px", "comment-content-detail-p-margin-bottom": "inherit", "tabs-card-head-background": "#fafafa", "tabs-card-height": "40px", "tabs-card-active-color": "#1890ff", "tabs-card-horizontal-padding": "8px 16px", "tabs-card-horizontal-padding-sm": "6px 16px", "tabs-card-horizontal-padding-lg": "7px 16px 6px", "tabs-title-font-size": "14px", "tabs-title-font-size-lg": "16px", "tabs-title-font-size-sm": "14px", "tabs-ink-bar-color": "#1890ff", "tabs-bar-margin": "0 0 16px 0", "tabs-horizontal-gutter": "32px", "tabs-horizontal-margin": "0 0 0 32px", "tabs-horizontal-margin-rtl": "0 0 0 32px", "tabs-horizontal-padding": "12px 0", "tabs-horizontal-padding-lg": "16px 0", "tabs-horizontal-padding-sm": "8px 0", "tabs-vertical-padding": "8px 24px", "tabs-vertical-margin": "16px 0 0 0", "tabs-scrolling-size": "32px", "tabs-highlight-color": "#1890ff", "tabs-hover-color": "#40a9ff", "tabs-active-color": "#096dd9", "tabs-card-gutter": "2px", "tabs-card-tab-active-border-top": "2px solid transparent", "back-top-color": "#fff", "back-top-bg": "rgba(0, 0, 0, 0.45)", "back-top-hover-bg": "rgba(0, 0, 0, 0.85)", "avatar-size-base": "32px", "avatar-size-lg": "40px", "avatar-size-sm": "24px", "avatar-font-size-base": "18px", "avatar-font-size-lg": "24px", "avatar-font-size-sm": "14px", "avatar-bg": "#ccc", "avatar-color": "#fff", "avatar-border-radius": "2px", "avatar-group-overlapping": "-8px", "avatar-group-space": "3px", "avatar-group-border-color": "#fff", "switch-height": "22px", "switch-sm-height": "16px", "switch-min-width": "44px", "switch-sm-min-width": "28px", "switch-disabled-opacity": "0.4", "switch-color": "#1890ff", "switch-bg": "#fff", "switch-shadow-color": "rgba(0, 35, 11, 0.2)", "switch-padding": "2px", "switch-inner-margin-min": "7px", "switch-inner-margin-max": "25px", "switch-sm-inner-margin-min": "5px", "switch-sm-inner-margin-max": "18px", "pagination-item-bg": "#fff", "pagination-item-size": "32px", "pagination-item-size-sm": "24px", "pagination-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "pagination-font-weight-active": "500", "pagination-item-bg-active": "#fff", "pagination-item-link-bg": "#fff", "pagination-item-disabled-color-active": "rgba(0, 0, 0, 0.25)", "pagination-item-disabled-bg-active": "#e6e6e6", "pagination-item-input-bg": "#fff", "pagination-mini-options-size-changer-top": "0px", "page-header-padding": "24px", "page-header-padding-vertical": "16px", "page-header-padding-breadcrumb": "12px", "page-header-content-padding-vertical": "12px", "page-header-back-color": "#000", "page-header-ghost-bg": "inherit", "page-header-heading-title": "20px", "page-header-heading-sub-title": "14px", "page-header-tabs-tab-font-size": "16px", "breadcrumb-base-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-last-item-color": "rgba(0, 0, 0, 0.85)", "breadcrumb-font-size": "14px", "breadcrumb-icon-font-size": "14px", "breadcrumb-link-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-link-color-hover": "rgba(0, 0, 0, 0.85)", "breadcrumb-separator-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-separator-margin": "0 8px", "slider-margin": "10px 6px 10px", "slider-rail-background-color": "#f5f5f5", "slider-rail-background-color-hover": "#e1e1e1", "slider-track-background-color": "#91d5ff", "slider-track-background-color-hover": "#69c0ff", "slider-handle-border-width": "2px", "slider-handle-background-color": "#fff", "slider-handle-color": "#91d5ff", "slider-handle-color-hover": "#69c0ff", "slider-handle-color-focus": "#46a6ff", "slider-handle-color-focus-shadow": "rgba(24, 144, 255, 0.12)", "slider-handle-color-tooltip-open": "#1890ff", "slider-handle-size": "14px", "slider-handle-margin-top": "-5px", "slider-handle-shadow": "0", "slider-dot-border-color": "#f0f0f0", "slider-dot-border-color-active": "#8cc8ff", "slider-disabled-color": "rgba(0, 0, 0, 0.25)", "slider-disabled-background-color": "#fff", "tree-bg": "#fff", "tree-title-height": "24px", "tree-child-padding": "18px", "tree-directory-selected-color": "#fff", "tree-directory-selected-bg": "#1890ff", "tree-node-hover-bg": "#f5f5f5", "tree-node-selected-bg": "#bae7ff", "collapse-header-padding": "12px 16px", "collapse-header-padding-extra": "40px", "collapse-header-bg": "#fafafa", "collapse-content-padding": "16px", "collapse-content-bg": "#fff", "collapse-header-arrow-left": "16px", "skeleton-color": "rgba(190, 190, 190, 0.2)", "skeleton-to-color": "rgba(129, 129, 129, 0.24)", "skeleton-paragraph-margin-top": "28px", "skeleton-paragraph-li-margin-top": "16px", "skeleton-paragraph-li-height": "16px", "skeleton-title-height": "16px", "skeleton-title-paragraph-margin-top": "24px", "transfer-header-height": "40px", "transfer-item-height": "32px", "transfer-disabled-bg": "#f5f5f5", "transfer-list-height": "200px", "transfer-item-hover-bg": "#f5f5f5", "transfer-item-selected-hover-bg": "#dcf4ff", "transfer-item-padding-vertical": "6px", "transfer-list-search-icon-top": "12px", "message-notice-content-padding": "10px 16px", "message-notice-content-bg": "#fff", "wave-animation-width": "6px", "alert-success-border-color": "#b7eb8f", "alert-success-bg-color": "#f6ffed", "alert-success-icon-color": "#52c41a", "alert-info-border-color": "#91d5ff", "alert-info-bg-color": "#e6f7ff", "alert-info-icon-color": "#1890ff", "alert-warning-border-color": "#ffe58f", "alert-warning-bg-color": "#fffbe6", "alert-warning-icon-color": "#faad14", "alert-error-border-color": "#ffccc7", "alert-error-bg-color": "#fff2f0", "alert-error-icon-color": "#ff4d4f", "alert-message-color": "rgba(0, 0, 0, 0.85)", "alert-text-color": "rgba(0, 0, 0, 0.85)", "alert-close-color": "rgba(0, 0, 0, 0.45)", "alert-close-hover-color": "rgba(0, 0, 0, 0.75)", "alert-no-icon-padding-vertical": "8px", "alert-with-description-no-icon-padding-vertical": "15px", "alert-with-description-padding-vertical": "15px", "alert-with-description-padding": "15px 15px 15px 24px", "alert-icon-top": "12.0005px", "alert-with-description-icon-size": "24px", "list-header-background": "transparent", "list-footer-background": "transparent", "list-empty-text-padding": "16px", "list-item-padding": "12px 0", "list-item-padding-sm": "8px 16px", "list-item-padding-lg": "16px 24px", "list-item-meta-margin-bottom": "16px", "list-item-meta-avatar-margin-right": "16px", "list-item-meta-title-margin-bottom": "12px", "list-customize-card-bg": "#fff", "list-item-meta-description-font-size": "14px", "statistic-title-font-size": "14px", "statistic-content-font-size": "24px", "statistic-unit-font-size": "24px", "statistic-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "drawer-header-padding": "16px 24px", "drawer-bg": "#fff", "drawer-footer-padding-vertical": "10px", "drawer-footer-padding-horizontal": "16px", "drawer-header-close-size": "56px", "drawer-title-font-size": "16px", "drawer-title-line-height": "22px", "timeline-width": "2px", "timeline-color": "#f0f0f0", "timeline-dot-border-width": "2px", "timeline-dot-color": "#1890ff", "timeline-dot-bg": "#fff", "timeline-item-padding-bottom": "20px", "typography-title-font-weight": "600", "typography-title-margin-top": "1.2em", "typography-title-margin-bottom": "0.5em", "upload-actions-color": "rgba(0, 0, 0, 0.45)", "process-tail-color": "#f0f0f0", "steps-nav-arrow-color": "rgba(0, 0, 0, 0.25)", "steps-background": "#fff", "steps-icon-size": "32px", "steps-icon-custom-size": "32px", "steps-icon-custom-top": "0px", "steps-icon-custom-font-size": "24px", "steps-icon-top": "-0.5px", "steps-icon-font-size": "16px", "steps-icon-margin": "0 8px 0 0", "steps-title-line-height": "32px", "steps-small-icon-size": "24px", "steps-small-icon-margin": "0 8px 0 0", "steps-dot-size": "8px", "steps-dot-top": "2px", "steps-current-dot-size": "10px", "steps-description-max-width": "140px", "steps-nav-content-max-width": "auto", "steps-vertical-icon-width": "16px", "steps-vertical-tail-width": "16px", "steps-vertical-tail-width-sm": "12px", "notification-bg": "#fff", "notification-padding-vertical": "16px", "notification-padding-horizontal": "24px", "result-title-font-size": "24px", "result-subtitle-font-size": "14px", "result-icon-font-size": "72px", "result-extra-margin": "24px 0 0 0", "image-size-base": "48px", "image-font-size-base": "24px", "image-bg": "#f5f5f5", "image-color": "#fff", "image-mask-font-size": "16px", "image-preview-operation-size": "18px", "image-preview-operation-color": "rgba(255, 255, 255, 0.85)", "image-preview-operation-disabled-color": "rgba(255, 255, 255, 0.25)", "segmented-bg": "rgba(0, 0, 0, 0.04)", "segmented-hover-bg": "rgba(0, 0, 0, 0.06)", "segmented-selected-bg": "#fff", "segmented-label-color": "rgba(0, 0, 0, 0.65)", "segmented-label-hover-color": "#262626", "root-entry-name": "variable"}, "proxy": {"/dev/": {"target": "http://localhost:8081", "changeOrigin": true, "pathRewrite": {"^/dev/": ""}}}, "fastRefresh": true, "model": {}, "initialState": {}, "title": "Daidaida", "layout": {"locale": true, "navTheme": "light", "colorPrimary": "#722ED1", "layout": "top", "contentWidth": "Fluid", "fixedHeader": true, "fixSiderbar": true, "pwa": true, "title": "代代达跑腿工作室", "logo": "https://cdn.daidaida.xyz/daidaida888/2025/05/26/ff99290effe84530a5a7920aff2eb8a8jpg", "token": {}, "splitMenus": false, "siderMenuType": "sub"}, "moment2dayjs": {"preset": "antd", "plugins": ["duration"]}, "locale": {"default": "zh-CN", "antd": true, "baseNavigator": true}, "antd": {"configProvider": {"componentSize": "middle", "strict": false}}, "request": {}, "access": {}, "headScripts": [{"src": "/scripts/loading.js", "async": true}], "presets": ["umi-presets-pro"], "openAPI": [{"requestLibPath": "import { request } from 'umi'", "schemaPath": "http://localhost:8081/v3/api-docs", "projectName": "test-swagger"}], "esbuildMinifyIIFE": true, "targets": {"chrome": 80}, "define": {"ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION": "", "REACT_APP_ENV": false}}, "routes": {"1": {"path": "/user", "layout": false, "id": "1", "absPath": "/user"}, "2": {"name": "login", "path": "/user/login", "file": "@/pages/User/Login/index.tsx", "parentId": "1", "id": "2", "absPath": "/user/login", "__content": "import { Footer } from '@/components';\nimport { login, emailLogin } from '@/services/test-swagger/loginController';\nimport { getFakeCaptcha } from '@/services/ant-design-pro/login';\nimport { sendEmailCode } from '@/services/test-swagger/profileController';\nimport {\n  AlipayCircleOutlined,\n  LockOutlined,\n  MobileOutlined,\n  TaobaoCircleOutlined,\n  UserOutlined,\n  WeiboCircleOutlined,\n} from '@ant-design/icons';\nimport {\n  LoginForm,\n  ProFormCaptcha,\n  ProFormCheckbox,\n  ProFormText,\n} from '@ant-design/pro-components';\nimport { FormattedMessage, history, SelectLang, useIntl, useModel, Helmet } from '@umijs/max';\nimport { Alert, message, Tabs, Form } from 'antd';\nimport Settings from '../../../../config/defaultSettings';\nimport React, { useState } from 'react';\nimport { flushSync } from 'react-dom';\nimport { createStyles } from 'antd-style';\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    action: {\n      marginLeft: '8px',\n      color: 'rgba(0, 0, 0, 0.2)',\n      fontSize: '24px',\n      verticalAlign: 'middle',\n      cursor: 'pointer',\n      transition: 'color 0.3s',\n      '&:hover': {\n        color: token.colorPrimaryActive,\n      },\n    },\n    lang: {\n      width: 42,\n      height: 42,\n      lineHeight: '42px',\n      position: 'fixed',\n      right: 16,\n      borderRadius: token.borderRadius,\n      ':hover': {\n        backgroundColor: token.colorBgTextHover,\n      },\n    },\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n  };\n});\n\nconst ActionIcons = () => {\n  const { styles } = useStyles();\n\n  return (\n    <>\n      <AlipayCircleOutlined key=\"AlipayCircleOutlined\" className={styles.action} />\n      <TaobaoCircleOutlined key=\"TaobaoCircleOutlined\" className={styles.action} />\n      <WeiboCircleOutlined key=\"WeiboCircleOutlined\" className={styles.action} />\n    </>\n  );\n};\n\nconst Lang = () => {\n  const { styles } = useStyles();\n\n  return (\n    <div className={styles.lang} data-lang>\n      {SelectLang && <SelectLang />}\n    </div>\n  );\n};\n\nconst LoginMessage: React.FC<{\n  content: string;\n}> = ({ content }) => {\n  return (\n    <Alert\n      style={{\n        marginBottom: 24,\n      }}\n      message={content}\n      type=\"error\"\n      showIcon\n    />\n  );\n};\n\nconst Login: React.FC = () => {\n  const [userLoginState, setUserLoginState] = useState<API.LoginResult>({});\n  const [type, setType] = useState<string>('account');\n  const { initialState, setInitialState } = useModel('@@initialState');\n  const { styles } = useStyles();\n  const intl = useIntl();\n  const [form] = Form.useForm();\n\n  const fetchUserInfo = async () => {\n    const userInfo = await initialState?.fetchUserInfo?.();\n    if (userInfo) {\n      flushSync(() => {\n        setInitialState((s) => ({\n          ...s,\n          currentUser: userInfo,\n        }));\n      });\n    }\n  };\n\n  const handleSubmit = async (values: API.LoginParams) => {\n    try {\n      // 登录\n      const response = await login({ \n        username: values.username,\n        password: values.password\n      });\n      \n      if (response.code === 200) {  // 假设后端返回code 200表示成功\n        const defaultLoginSuccessMessage = intl.formatMessage({\n          id: 'pages.login.success',\n          defaultMessage: '登录成功！',\n        });\n        message.success(defaultLoginSuccessMessage);\n        \n        // 如果后端返回了token，可以在这里保存\n        if (response.data?.token) {\n          localStorage.setItem('token', response.data.token);\n        }\n        \n        await fetchUserInfo();\n        const urlParams = new URL(window.location.href).searchParams;\n        history.push(urlParams.get('redirect') || '/');\n        return;\n      }\n      \n      // 登录失败\n      message.error(response.msg || '登录失败');\n      setUserLoginState({ status: 'error', type: 'account' });\n      \n    } catch (error) {\n      const defaultLoginFailureMessage = intl.formatMessage({\n        id: 'pages.login.failure',\n        defaultMessage: '登录失败，请重试！',\n      });\n      console.log(error);\n      message.error(defaultLoginFailureMessage);\n    }\n  };\n\n  // 处理邮箱登录\n  const handleEmailLogin = async (values: API.EmailLoginBody) => {\n    try {\n      const response = await emailLogin(values);\n      \n      if (response.code === 200) {\n        const defaultLoginSuccessMessage = intl.formatMessage({\n          id: 'pages.login.success',\n          defaultMessage: '登录成功！',\n        });\n        message.success(defaultLoginSuccessMessage);\n        \n        // 保存token\n        if (response.data?.token) {\n          localStorage.setItem('token', response.data.token);\n        }\n        \n        await fetchUserInfo();\n        const urlParams = new URL(window.location.href).searchParams;\n        history.push(urlParams.get('redirect') || '/');\n        return;\n      }\n      \n      // 登录失败\n      message.error(response.msg || '登录失败');\n      setUserLoginState({ status: 'error', type: 'email' });\n      \n    } catch (error) {\n      const defaultLoginFailureMessage = intl.formatMessage({\n        id: 'pages.login.failure',\n        defaultMessage: '登录失败，请重试！',\n      });\n      message.error(defaultLoginFailureMessage);\n    }\n  };\n\n  // 修改处理发送验证码的函数\n  const handleSendCode = async (email: string) => {\n    try {\n      const response = await sendEmailCode({\n        email: email\n      });\n      \n      if (response.code === 200) {\n        message.success('验证码发送成功，请查收邮件');\n        return true;\n      } else {\n        message.error(response.msg || '验证码发送失败');\n        return false;\n      }\n    } catch (error) {\n      message.error('验证码发送失败，请重试');\n      return false;\n    }\n  };\n\n  const { status, type: loginType } = userLoginState;\n\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          {intl.formatMessage({\n            id: 'menu.login',\n            defaultMessage: '登录页',\n          })}\n          - {Settings.title}\n        </title>\n      </Helmet>\n      <Lang />\n      <div\n        style={{\n          flex: '1',\n          padding: '32px 0',\n        }}\n      >\n        <LoginForm\n          contentStyle={{\n            minWidth: 280,\n            maxWidth: '75vw',\n          }}\n          logo={<img alt=\"logo\" src=\"/logo.png\" />}\n          title={Settings.title || \"Daidaida Run\"}\n          subTitle={intl.formatMessage({ id: 'pages.layouts.userLayout.title' })}\n          initialValues={{\n            autoLogin: true,\n          }}\n          onFinish={async (values) => {\n            if (type === 'account') {\n              await handleSubmit(values as API.LoginParams);\n            } else {\n              await handleEmailLogin(values as API.EmailLoginBody);\n            }\n          }}\n          form={form}\n        >\n          <Tabs\n            activeKey={type}\n            onChange={setType}\n            centered\n            items={[\n              {\n                key: 'account',\n                label: intl.formatMessage({\n                  id: 'pages.login.accountLogin.tab',\n                  defaultMessage: '账户密码登录',\n                }),\n              },\n              {\n                key: 'email',\n                label: intl.formatMessage({\n                  id: 'pages.login.emailLogin.tab',\n                  defaultMessage: '邮箱登录',\n                }),\n              },\n            ]}\n          />\n\n          {status === 'error' && loginType === 'account' && (\n            <LoginMessage\n              content={intl.formatMessage({\n                id: 'pages.login.accountLogin.errorMessage',\n                defaultMessage: '账户或密码错误(admin/ant.design)',\n              })}\n            />\n          )}\n          {type === 'account' && (\n            <>\n              <ProFormText\n                name=\"username\"\n                fieldProps={{\n                  size: 'large',\n                  prefix: <UserOutlined />,\n                }}\n                placeholder={intl.formatMessage({\n                  id: 'pages.login.username.placeholder',\n                  defaultMessage: '用户名: admin or user',\n                })}\n                rules={[\n                  {\n                    required: true,\n                    message: (\n                      <FormattedMessage\n                        id=\"pages.login.username.required\"\n                        defaultMessage=\"请输入用户名!\"\n                      />\n                    ),\n                  },\n                ]}\n              />\n              <ProFormText.Password\n                name=\"password\"\n                fieldProps={{\n                  size: 'large',\n                  prefix: <LockOutlined />,\n                }}\n                placeholder={intl.formatMessage({\n                  id: 'pages.login.password.placeholder',\n                  defaultMessage: '密码: ant.design',\n                })}\n                rules={[\n                  {\n                    required: true,\n                    message: (\n                      <FormattedMessage\n                        id=\"pages.login.password.required\"\n                        defaultMessage=\"请输入密码！\"\n                      />\n                    ),\n                  },\n                ]}\n              />\n            </>\n          )}\n\n          {status === 'error' && loginType === 'email' && <LoginMessage content=\"验证码错误\" />}\n          {type === 'email' && (\n            <>\n              <ProFormText\n                fieldProps={{\n                  size: 'large',\n                  prefix: <UserOutlined />,\n                }}\n                name=\"email\"\n                placeholder={intl.formatMessage({\n                  id: 'pages.login.email.placeholder',\n                  defaultMessage: '请输入邮箱',\n                })}\n                rules={[\n                  {\n                    required: true,\n                    message: (\n                      <FormattedMessage\n                        id=\"pages.login.email.required\"\n                        defaultMessage=\"请输入邮箱！\"\n                      />\n                    ),\n                  },\n                  {\n                    type: 'email',\n                    message: (\n                      <FormattedMessage\n                        id=\"pages.login.email.invalid\"\n                        defaultMessage=\"邮箱格式错误！\"\n                      />\n                    ),\n                  },\n                ]}\n              />\n              <ProFormCaptcha\n                fieldProps={{\n                  size: 'large',\n                  prefix: <LockOutlined />,\n                }}\n                captchaProps={{\n                  size: 'large',\n                }}\n                placeholder={intl.formatMessage({\n                  id: 'pages.login.captcha.placeholder',\n                  defaultMessage: '请输入验证码',\n                })}\n                name=\"code\"\n                rules={[\n                  {\n                    required: true,\n                    message: (\n                      <FormattedMessage\n                        id=\"pages.login.captcha.required\"\n                        defaultMessage=\"请输入验证码！\"\n                      />\n                    ),\n                  },\n                ]}\n                onGetCaptcha={async () => {\n                  const email = form.getFieldValue('email');\n                  if (!email) {\n                    message.error('请先输入邮箱');\n                    return Promise.reject('请先输入邮箱');\n                  }\n                  \n                  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n                  if (!emailRegex.test(email)) {\n                    message.error('请输入正确的邮箱格式');\n                    return Promise.reject('邮箱格式错误');\n                  }\n\n                  const success = await handleSendCode(email);\n                  if (!success) {\n                    throw new Error('获取验证码失败');\n                  }\n                }}\n                captchaTextRender={(timing, count) => {\n                  if (timing) {\n                    return `${count} 秒后重新获取`;\n                  }\n                  return '获取验证码';\n                }}\n              />\n            </>\n          )}\n          <div\n            style={{\n              marginBottom: 24,\n            }}\n          >\n            \n          </div>\n        </LoginForm>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default Login;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/User/Login/index.tsx"}, "3": {"path": "/welcome", "name": "welcome", "icon": "smile", "file": "@/pages/Welcome.tsx", "parentId": "ant-design-pro-layout", "id": "3", "absPath": "/welcome", "__content": "import { PageContainer } from '@ant-design/pro-components';\nimport { useModel } from '@umijs/max';\nimport { Card, Typography, theme } from 'antd';\nimport React from 'react';\n\nconst { Title, Paragraph } = Typography;\n\nconst Welcome: React.FC = () => {\n  const { token } = theme.useToken();\n  const { initialState } = useModel('@@initialState');\n  const currentUser = initialState?.currentUser;\n\n  return (\n    <PageContainer>\n      <Card\n        style={{\n          borderRadius: 8,\n          height: '70vh',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          background: token.colorBgContainer,\n        }}\n        bodyStyle={{\n          padding: '48px',\n          textAlign: 'center',\n          maxWidth: '800px',\n          width: '100%',\n        }}\n        bordered={false}\n      >\n        <div\n          style={{\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            gap: '24px',\n          }}\n        >\n          <Title level={1} style={{ marginBottom: 0, fontSize: '36px' }}>\n            欢迎使用校园跑腿管理系统\n          </Title>\n          \n          {currentUser?.name && (\n            <Title \n              level={3} \n              style={{ \n                marginTop: 0,\n                fontWeight: 'normal',\n                color: token.colorTextSecondary \n              }}\n            >\n              {`您好，${currentUser.name}`}\n            </Title>\n          )}\n\n          <Paragraph\n            style={{\n              fontSize: '16px',\n              color: token.colorTextSecondary,\n              marginBottom: '32px',\n              lineHeight: '1.8',\n            }}\n          >\n            这是一个专业的校园跑腿服务管理平台，帮助您高效管理订单、用户和业务数据。\n            <br />\n            让我们开始使用系统的强大功能，为校园服务提供更好的体验。\n          </Paragraph>\n        </div>\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default Welcome;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/Welcome.tsx"}, "4": {"path": "/admin", "name": "admin", "icon": "crown", "access": "canAdmin", "parentId": "ant-design-pro-layout", "id": "4", "absPath": "/admin"}, "5": {"path": "/admin", "redirect": "/admin/sub-page", "parentId": "4", "id": "5", "absPath": "/admin"}, "6": {"path": "/admin/sub-page", "name": "sub-page", "file": "@/pages/Admin.tsx", "parentId": "4", "id": "6", "absPath": "/admin/sub-page", "__content": "import { HeartTwoTone, SmileTwoTone } from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { useIntl } from '@umijs/max';\nimport { Alert, Card, Typography } from 'antd';\nimport React from 'react';\n\nconst Admin: React.FC = () => {\n  const intl = useIntl();\n  return (\n    <PageContainer\n      content={intl.formatMessage({\n        id: 'pages.admin.subPage.title',\n        defaultMessage: 'This page can only be viewed by admin',\n      })}\n    >\n      <Card>\n        <Alert\n          message={intl.formatMessage({\n            id: 'pages.welcome.alertMessage',\n            defaultMessage: 'Faster and stronger heavy-duty components have been released.',\n          })}\n          type=\"success\"\n          showIcon\n          banner\n          style={{\n            margin: -12,\n            marginBottom: 48,\n          }}\n        />\n        <Typography.Title level={2} style={{ textAlign: 'center' }}>\n          <SmileTwoTone /> Ant Design Pro <HeartTwoTone twoToneColor=\"#eb2f96\" /> You\n        </Typography.Title>\n      </Card>\n      <p style={{ textAlign: 'center', marginTop: 24 }}>\n        Want to add more pages? Please refer to{' '}\n        <a href=\"https://pro.ant.design/docs/block-cn\" target=\"_blank\" rel=\"noopener noreferrer\">\n          use block\n        </a>\n        。\n      </p>\n    </PageContainer>\n  );\n};\n\nexport default Admin;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/Admin.tsx"}, "7": {"name": "user-manage", "icon": "user", "path": "/userManage", "parentId": "ant-design-pro-layout", "id": "7", "absPath": "/userManage"}, "8": {"path": "/userManage/pc", "name": "pc-user", "perms": "system:userpc:view", "access": "routeFilter", "file": "@/pages/userManage/pc/index.tsx", "parentId": "7", "id": "8", "absPath": "/userManage/pc", "__content": "// @ts-nocheck\nimport { PlusOutlined } from '@ant-design/icons';\nimport { Button, message, Modal, Descriptions, Image, Divider, Dropdown } from 'antd';\nimport React, { useState, useRef } from 'react';\nimport { PageContainer } from '@ant-design/pro-layout';\nimport type { ProColumns, ActionType } from '@ant-design/pro-table';\nimport ProTable from '@ant-design/pro-table';\nimport { Access, useAccess } from '@umijs/max';\nimport { addUser, edit, listPc, remove1 as remove, resetPwd } from '@/services/test-swagger/userPcController';\nimport type { ProFormInstance } from '@ant-design/pro-form';\nimport {\n  ModalForm,\n  ProFormText,\n  ProFormSelect,\n  ProFormUploadButton,\n  ProFormRadio,\n} from '@ant-design/pro-form';\nimport { upload } from '@/services/test-swagger/ossController';\nimport type { MenuProps } from 'antd';\nimport { EllipsisOutlined } from '@ant-design/icons';\n\n// 扩展用户类型，以匹配实际使用\ntype ExtendedUser = API.User & {\n  userPc?: API.UserPc;\n  userType?: number;\n  uid?: number;\n  createTime?: string;\n  updateTime?: string;\n  loginTime?: string;\n  loginIp?: string;\n  loginRegion?: string;\n  createId?: number;\n};\n\n// 扩展查询类型\ntype ExtendedUserPcQuery = API.UserPcQuery & {\n  [key: string]: any;\n};\n\nconst UserPcList: React.FC = () => {\n  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\n  const [detailModalVisible, handleDetailModalVisible] = useState<boolean>(false);\n  const [currentRow, setCurrentRow] = useState<ExtendedUser>();\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [selectedRows, setSelectedRows] = useState<ExtendedUser[]>([]);\n  \n  const actionRef = useRef<ActionType>();\n  const formRef = useRef<ProFormInstance>();\n  const access = useAccess();\n\n  const columns: ProColumns<ExtendedUser>[] = [\n    {\n      title: 'UID',\n      dataIndex: 'uid',\n      key: 'uid',\n      hideInTable: true,\n    },\n    {\n      title: '用户名',\n      dataIndex: ['userPc', 'username'],\n      key: 'username',\n      formItemProps: {\n        rules: [{ required: true, message: '请输入用户名' }],\n      },\n    },\n    {\n      title: '手机号',\n      dataIndex: ['userPc', 'phone'],\n      key: 'phone',\n    },\n    {\n      title: '真实姓名',\n      dataIndex: ['userPc', 'name'],\n      key: 'name',\n    },\n    {\n      title: '头像',\n      dataIndex: ['userPc', 'avatar'],\n      hideInSearch: true,\n      render: (_, record) => (\n        <Image\n          src={(record as any).userPc?.avatar}\n          width={32}\n          height={32}\n          style={{ borderRadius: '50%' }}\n          preview={{\n            src: (record as any).userPc?.avatar,\n          }}\n        />\n      ),\n    },\n    {\n      title: '性别',\n      dataIndex: ['userPc', 'sex'],\n      key: 'sex',\n      valueEnum: {\n        0: { text: '女', status: 'Default' },\n        1: { text: '男', status: 'Success' },\n      },\n      valueType: 'select',\n    },\n    {\n      title: '状态',\n      dataIndex: ['userPc', 'status'],\n      key: 'status',\n      valueEnum: {\n        0: { text: '禁用', status: 'Error' },\n        1: { text: '启用', status: 'Success' },\n      },\n      valueType: 'select',\n    },\n    {\n      title: '用户类型',\n      dataIndex: 'userType',\n      key: 'userType',\n      valueEnum: {\n        0: { text: '超级管理员', status: 'Success' },\n        1: { text: '管理员', status: 'Processing' },\n        2: { text: '普通管理员', status: 'Default' },\n      },\n      valueType: 'select',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      valueType: 'dateTimeRange',\n      hideInTable: true,\n      search: {\n        transform: (value) => {\n          return {\n            createTimeBegin: value[0],\n            createTimeEnd: value[1],\n          };\n        },\n      },\n    },\n    {\n      title: '最后登录时间',\n      dataIndex: 'loginTime',\n      valueType: 'dateTimeRange',\n      hideInTable: true,\n      search: {\n        transform: (value) => {\n          return {\n            loginTimeBegin: value[0],\n            loginTimeEnd: value[1],\n          };\n        },\n      },\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updateTime',\n      valueType: 'dateTimeRange',\n      hideInTable: true,\n      search: {\n        transform: (value) => {\n          return {\n            updateTimeBegin: value[0],\n            updateTimeEnd: value[1],\n          };\n        },\n      },\n    },\n    {\n      title: '操作',\n      valueType: 'option',\n      render: (_, record) => [\n        <Access key=\"edit\" accessible={access.hasPerms('system:userpc:edit')}>\n          <a onClick={() => {\n            setCurrentRow(record);\n            handleUpdateModalVisible(true);\n          }}>\n            编辑\n          </a>\n        </Access>,\n        <a key=\"detail\" onClick={() => {\n          setCurrentRow(record);\n          handleDetailModalVisible(true);\n        }}>\n          详情\n        </a>,\n        <Dropdown\n          key=\"more\"\n          menu={{\n            items: [\n              {\n                key: 'resetPwd',\n                label: (\n                  <Access accessible={access.hasPerms('system:userpc:resetPwd')}>\n                    <a onClick={() => {\n                      Modal.confirm({\n                        title: '确认重置密码',\n                        content: '确定要重置该用户的密码吗？',\n                        onOk: () => handleResetPwd(record.userPc?.uid),\n                      });\n                    }}>\n                      重置密码\n                    </a>\n                  </Access>\n                ),\n              },\n              {\n                key: 'delete',\n                label: (\n                  <Access accessible={access.hasPerms('system:userpc:delete')}>\n                    <a onClick={() => {\n                      Modal.confirm({\n                        title: '确认删除',\n                        content: '确定要删除该用户吗？',\n                        onOk: () => handleDelete([record.userPc?.uid || 0]),\n                      });\n                    }}>\n                      删除\n                    </a>\n                  </Access>\n                ),\n              },\n            ],\n          }}\n        >\n          <a>\n            <EllipsisOutlined />\n          </a>\n        </Dropdown>,\n      ],\n    },\n  ];\n\n  // 处理函数\n  const handleAdd = async (fields: API.UserPc) => {\n    const hide = message.loading('正在添加...');\n    console.log(fields);\n    try {\n      const res = await addUser({\n        ...fields,\n        id: 0,\n      });\n      if (res.code === 200) {\n        message.success('添加成功');\n        handleCreateModalVisible(false);\n        actionRef.current?.reload();\n        return true;\n      }\n      message.error(res.msg || '添加失败');\n      return false;\n    } finally {\n      hide();\n    }\n  };\n\n  const handleUpdate = async (fields: API.UserPc) => {\n    const hide = message.loading('正在更新...');\n    try {\n      const res = await edit({\n        ...fields,\n        id: currentRow?.userPc?.id,\n        uid: currentRow?.userPc?.uid,\n      });\n      if (res.code === 200) {\n        message.success('更新成功');\n        handleUpdateModalVisible(false);\n        actionRef.current?.reload();\n        return true;\n      }\n      message.error(res.msg || '更新失败');\n      return false;\n    } finally {\n      hide();\n    }\n  };\n\n  const handleDelete = async (uIds: number[]) => {\n    const hide = message.loading('正在删除...');\n    try {\n      const res = await remove({ uIds: uIds.join(',') });\n      if (res.code === 200) {\n        message.success('删除成功');\n        actionRef.current?.reload();\n        setSelectedRows([]);\n        setSelectedRowKeys([]);\n        return true;\n      }\n      message.error(res.msg || '删除失败');\n      return false;\n    } finally {\n      hide();\n    }\n  };\n\n  const handleResetPwd = async (uId: number) => {\n    const hide = message.loading('正在重置密码...');\n    try {\n      const res = await resetPwd({ uId });\n      if (res.code === 200) {\n        message.success('密码重置成功');\n        return true;\n      }\n      message.error(res.msg || '密码重置失败');\n      return false;\n    } finally {\n      hide();\n    }\n  };\n\n  return (\n    <PageContainer>\n      <ProTable<ExtendedUser>\n        actionRef={actionRef}\n        rowKey=\"uid\"\n        search={{\n          labelWidth: 120,\n          defaultCollapsed: false,\n        }}\n        toolBarRender={() => [\n          <Access accessible={access.hasPerms('system:userpc:add')}>\n            <Button\n              type=\"primary\"\n              onClick={() => handleCreateModalVisible(true)}\n            >\n              <PlusOutlined /> 新建\n            </Button>\n          </Access>,\n          selectedRowKeys.length > 0 && (\n            <Access accessible={access.hasPerms('system:userpc:delete')}>\n              <Button\n                danger\n                onClick={() => {\n                  if (!selectedRowKeys.length) {\n                    message.warning('请选择要删除的记录');\n                    return;\n                  }\n                  const uIds = selectedRows\n                    .map(row => row.userPc?.uid)\n                    .filter((uid): uid is number => uid !== undefined && uid !== null);\n                  Modal.confirm({\n                    title: '确认删除',\n                    content: `确定要删除这${selectedRowKeys.length}条记录吗？`,\n                    onOk: () => handleDelete(uIds),\n                  });\n                }}\n              >\n                批量删除\n              </Button>\n            </Access>\n          ),\n        ]}\n        request={async (params, sorter, filter) => {\n          try {\n            const queryParams: ExtendedUserPcQuery = {\n              userPcQuery: {\n                uid: params.uid ? Number(params.uid) : undefined,\n                username: params.username,\n                phone: params.phone,\n                name: params.name,\n                sex: params.sex ? Number(params.sex) : undefined,\n                status: params.status ? Number(params.status) : undefined,\n                userType: params.userType ? Number(params.userType) : undefined,\n                loginRegion: params.loginRegion,\n                createId: params.createId,\n              },\n              pageQuery: {\n                pageSize: params.pageSize,\n                pageNum: params.current,\n              },\n            };\n\n            // 添加时间范围参数\n            if (params.createTimeBegin) {\n              queryParams.userPcQuery[`params['createTimeBegin']`] = params.createTimeBegin;\n              queryParams.userPcQuery[`params['createTimeEnd']`] = params.createTimeEnd;\n            }\n            if (params.loginTimeBegin) {\n              queryParams.userPcQuery[`params['loginTimeBegin']`] = params.loginTimeBegin;\n              queryParams.userPcQuery[`params['loginTimeEnd']`] = params.loginTimeEnd;\n            }\n            if (params.updateTimeBegin) {\n              queryParams.userPcQuery[`params['updateTimeBegin']`] = params.updateTimeBegin;\n              queryParams.userPcQuery[`params['updateTimeEnd']`] = params.updateTimeEnd;\n            }\n\n            // 移除所有空值\n            const cleanParams = JSON.parse(JSON.stringify(queryParams));\n\n            // 移除 userPcQuery 中的空值\n            Object.keys(cleanParams.userPcQuery).forEach(key => {\n              if (cleanParams.userPcQuery[key] === undefined) {\n                delete cleanParams.userPcQuery[key];\n              }\n            });\n\n            console.log('查询参数:', cleanParams);\n\n            const res = await listPc(cleanParams);\n            return {\n              data: res.rows || [],\n              success: res.code === 200,\n              total: res.total || 0,\n            };\n          } catch (error) {\n            message.error('获取数据失败');\n            return {\n              data: [],\n              success: false,\n              total: 0,\n            };\n          }\n        }}\n        columns={columns}\n        rowSelection={{\n          selectedRowKeys,\n          onChange: (keys, rows) => {\n            setSelectedRowKeys(keys);\n            setSelectedRows(rows);\n          },\n        }}\n      />\n\n      <ModalForm\n        title=\"新建用户\"\n        width=\"500px\"\n        visible={createModalVisible}\n        onVisibleChange={handleCreateModalVisible}\n        onFinish={handleAdd}\n        formRef={formRef}\n      >\n        <ProFormSelect\n          name=\"userType\"\n          label=\"用户类型\"\n          options={[\n            { label: '超级管理员', value: 0 },\n            { label: '管理员', value: 1 },\n            { label: '普通管理员', value: 2 },\n          ]}\n          rules={[{ required: true, message: '请选择用户类型' }]}\n        />\n        <ProFormText\n          name=\"username\"\n          label=\"用户名\"\n          rules={[{ required: true, message: '请输入用户名' }, { min: 6, max: 20, message: '账户长度必须在6到20个字符之间' }]}\n        />\n        <ProFormText\n          name=\"password\"\n          label=\"密码\"\n          rules={[{ required: true, message: '密码不能为空' }, { min: 6, max: 20, message: '密码长度必须在6到20个字符之间' }]}\n          type=\"password\"\n        />\n        <ProFormText\n          name=\"phone\"\n          label=\"手机号\"\n          rules={[{ required: true, message: '请输入手机号' }, { pattern: /^1[3-9]\\d{9}$/, message: '手机号码格式不正确' }]}\n        />\n        <ProFormText\n          name=\"name\"\n          label=\"真实姓名\"\n          rules={[{ required: true, message: '请输入真实姓名' }]}\n        />\n        <ProFormUploadButton\n          name=\"studentCardUrl\"\n          label=\"学生证\"\n          max={1}\n          fieldProps={{\n            name: 'file',\n            listType: 'picture-card',\n            maxCount: 1,\n            customRequest: async ({ file, onSuccess, onError }) => {\n              try {\n                const res = await upload(\n                  {}, // params\n                  { type: 5, name: '' }, // body, type为学生证\n                  file as File\n                );\n                if (res.code === 200 && res.data) {\n                  onSuccess?.(res);\n                } else {\n                  onError?.(new Error(res.msg || '上传失败'));\n                }\n              } catch (error) {\n                onError?.(error);\n              }\n            },\n          }}\n          transform={(value) => {\n            if (value && value.length > 0 && value[0].response) {\n              return value[0].response.data.url;\n            }\n            return undefined;\n          }}\n          rules={[{ required: true, message: '缺少学生证' }]}\n        />\n        <ProFormUploadButton\n          name=\"idCardUrl\"\n          label=\"身份证\"\n          max={1}\n          fieldProps={{\n            name: 'file',\n            listType: 'picture-card',\n            maxCount: 1,\n            customRequest: async ({ file, onSuccess, onError }) => {\n              try {\n                const res = await upload(\n                  {}, // params\n                  { type: 8, name: '' }, // body, type为身份证\n                  file as File\n                );\n                if (res.code === 200 && res.data) {\n                  onSuccess?.(res);\n                } else {\n                  onError?.(new Error(res.msg || '上传失败'));\n                }\n              } catch (error) {\n                onError?.(error);\n              }\n            },\n          }}\n          transform={(value) => {\n            if (value && value.length > 0 && value[0].response) {\n              return value[0].response.data.url;\n            }\n            return undefined;\n          }}\n          rules={[{ required: true, message: '缺少身份证' }]}\n        />\n        <ProFormRadio.Group\n          name=\"sex\"\n          label=\"性别\"\n          options={[\n            { label: '男', value: 1 },\n            { label: '女', value: 0 },\n          ]}\n          rules={[{ required: true, message: '请先设置性别' }]}\n        />\n        <ProFormRadio.Group\n          name=\"status\"\n          label=\"状态\"\n          options={[\n            { label: '启用', value: 1 },\n            { label: '禁用', value: 0 },\n          ]}\n          rules={[{ required: true, message: '请先设置用户状态' }]}\n        />\n        <ProFormUploadButton\n          name=\"avatar\"\n          label=\"头像\"\n          max={1}\n          fieldProps={{\n            name: 'file',\n            listType: 'picture-card',\n            maxCount: 1,\n            customRequest: async ({ file, onSuccess, onError }) => {\n              try {\n                const res = await upload(\n                  {}, // params\n                  { type: 4, name: '' }, // body, type为用户头像\n                  file as File\n                );\n                if (res.code === 200 && res.data) {\n                  onSuccess?.(res);\n                } else {\n                  onError?.(new Error(res.msg || '上传失败'));\n                }\n              } catch (error) {\n                onError?.(error);\n              }\n            },\n          }}\n          transform={(value) => {\n            if (value && value.length > 0 && value[0].response) {\n              return value[0].response.data.url;\n            }\n            return undefined;\n          }}\n          rules={[{ required: true, message: '请先设置用户头像' }]}\n        />\n        <ProFormRadio.Group\n          name=\"emailEnable\"\n          label=\"邮件启用状态\"\n          options={[\n            { label: '启用', value: 1 },\n            { label: '禁用', value: 0 },\n          ]}\n          rules={[{ required: true, message: '请先设置邮件启用状态' }]}\n        />\n        <ProFormText\n          name=\"email\"\n          label=\"邮箱\"\n          rules={[{ required: true, type: 'email', message: '请先设置邮件' }]}\n        />\n      </ModalForm>\n\n      <ModalForm\n        title=\"编辑用户\"\n        width=\"500px\"\n        visible={updateModalVisible}\n        onVisibleChange={handleUpdateModalVisible}\n        onFinish={handleUpdate}\n        initialValues={{\n          ...currentRow?.userPc,\n          id: currentRow?.userPc?.id,\n          userType: currentRow?.userType,\n        }}\n        modalProps={{\n          destroyOnClose: true,\n          onCancel: () => {\n            handleUpdateModalVisible(false);\n            setCurrentRow(undefined);\n          }\n        }}\n        request={async () => {\n          return {\n            ...currentRow?.userPc,\n            id: currentRow?.userPc?.id,\n            userType: currentRow?.userType,\n          };\n        }}\n      >\n        <ProFormText\n          name=\"id\"\n          hidden\n        />\n        <ProFormSelect\n          name=\"userType\"\n          label=\"用户类型\"\n          options={[\n            { label: '超级管理员', value: 0 },\n            { label: '管理员', value: 1 },\n            { label: '普通管理员', value: 2 },\n          ]}\n          rules={[{ required: true, message: '请选择用户类型' }]}\n        />\n        <ProFormText\n          name=\"username\"\n          label=\"用户名\"\n          rules={[{ required: true, message: '请输入用户名' }]}\n        />\n        <ProFormText\n          name=\"phone\"\n          label=\"手机号\"\n          rules={[{ required: true, message: '请输入手机号' }]}\n        />\n        <ProFormText\n          name=\"name\"\n          label=\"真实姓名\"\n          rules={[{ required: true, message: '请输入真实姓名' }]}\n        />\n        <ProFormRadio.Group\n          name=\"sex\"\n          label=\"性别\"\n          options={[\n            { label: '男', value: 1 },\n            { label: '女', value: 0 },\n          ]}\n          rules={[{ required: true, message: '请先设置性别' }]}\n        />\n        <ProFormRadio.Group\n          name=\"status\"\n          label=\"状态\"\n          options={[\n            { label: '启用', value: 1 },\n            { label: '禁用', value: 0 },\n          ]}\n          rules={[{ required: true, message: '请先设置用户状态' }]}\n        />\n        <ProFormRadio.Group\n          name=\"emailEnable\"\n          label=\"邮件启用状态\"\n          options={[\n            { label: '启用', value: 1 },\n            { label: '禁用', value: 0 },\n          ]}\n          rules={[{ required: true, message: '请先设置邮件启用状态' }]}\n        />\n        <ProFormText\n          name=\"email\"\n          label=\"邮箱\"\n          rules={[{ required: true, type: 'email', message: '请先设置邮件' }]}\n        />\n      </ModalForm>\n\n      <Modal\n        title=\"用户详情\"\n        width=\"800px\"\n        open={detailModalVisible}\n        onCancel={() => handleDetailModalVisible(false)}\n        footer={null}\n      >\n        <Descriptions column={2}>\n          <Descriptions.Item label=\"UID\">{currentRow?.uid}</Descriptions.Item>\n          <Descriptions.Item label=\"用户类型\">\n            {currentRow?.userType === 0 ? '超级管理员' : \n            currentRow?.userType === 1 ? '管理员' :\n             currentRow?.userType === 2 ? '普通管理员' : '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">{currentRow?.createTime}</Descriptions.Item>\n          <Descriptions.Item label=\"最后登录时间\">{currentRow?.loginTime}</Descriptions.Item>\n          <Descriptions.Item label=\"登录IP\">{currentRow?.loginIp}</Descriptions.Item>\n          <Descriptions.Item label=\"登录地点\">{currentRow?.loginRegion}</Descriptions.Item>\n          <Descriptions.Item label=\"创建人ID\">{currentRow?.createId}</Descriptions.Item>\n          <Descriptions.Item label=\"更新时间\">{currentRow?.updateTime}</Descriptions.Item>\n        </Descriptions>\n        \n        <Divider>PC用户信息</Divider>\n        \n        <Descriptions column={2}>\n          <Descriptions.Item label=\"用户名\">{currentRow?.userPc?.username}</Descriptions.Item>\n          <Descriptions.Item label=\"手机号\">{currentRow?.userPc?.phone}</Descriptions.Item>\n          <Descriptions.Item label=\"真实姓名\">{currentRow?.userPc?.name}</Descriptions.Item>\n          <Descriptions.Item label=\"邮箱\">{currentRow?.userPc?.email}</Descriptions.Item>\n          <Descriptions.Item label=\"性别\">\n            {currentRow?.userPc?.sex === 1 ? '男' : '女'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"状态\">\n            {currentRow?.userPc?.status === 1 ? '启用' : '禁用'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"头像\">\n            {currentRow?.userPc?.avatar && (\n            <Image\n              src={currentRow?.userPc?.avatar}\n              width={64}\n              height={64}\n              style={{ borderRadius: '50%' }}\n            />\n            )}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"学生证\">\n            {currentRow?.userPc?.studentCardUrl && (\n              <Image src={currentRow?.userPc?.studentCardUrl} width={100} />\n            )}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"身份证\">\n            {currentRow?.userPc?.idCardUrl && (\n              <Image src={currentRow?.userPc?.idCardUrl} width={100} />\n            )}\n          </Descriptions.Item>\n        </Descriptions>\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default UserPcList;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/userManage/pc/index.tsx"}, "9": {"path": "/userManage/xcx", "name": "mini-program-user", "perms": "system:userwx:view", "access": "routeFilter", "file": "@/pages/userManage/xcx/index.tsx", "parentId": "7", "id": "9", "absPath": "/userManage/xcx", "__content": "import { PlusOutlined, EyeOutlined } from '@ant-design/icons';\nimport { Button, message, Modal, Descriptions, Image, Tag, Space, Select } from 'antd';\nimport React, { useState, useRef, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-layout';\nimport type { ProColumns, ActionType } from '@ant-design/pro-table';\nimport ProTable from '@ant-design/pro-table';\nimport ProForm, {\n  ModalForm,\n  ProFormText,\n  ProFormSelect,\n  ProFormDigit,\n} from '@ant-design/pro-form';\nimport { listWx as list, editAgent } from '@/services/test-swagger/userWxController';\nimport { list5 as listSchools } from '@/services/test-swagger/schoolController';\nimport { list as listXcxUsers } from '@/services/test-swagger/userWxController';\n\nconst TableList: React.FC = () => {\n  const [createModalVisible, handleModalVisible] = useState<boolean>(false);\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\n  const [detailModalVisible, handleDetailModalVisible] = useState<boolean>(false);\n  const [currentRow, setCurrentRow] = useState<API.User>();\n  const actionRef = useRef<ActionType>();\n\n  // 查询条件状态\n  const [selectedSchool, setSelectedSchool] = useState<number>();\n  const [schoolOptions, setSchoolOptions] = useState<{ label: string; value: number; }[]>([]);\n\n  // 获取校区列表\n  const fetchSchoolList = async () => {\n    try {\n      const res = await listSchools({\n        pageQuery: {\n          pageSize: 999,\n          pageNum: 1,\n        },\n        school: {}\n      });\n      if (res.code === 200) {\n        const options = (res.rows || []).map((item) => ({\n          label: item.name,\n          value: item.id,\n        }));\n        setSchoolOptions(options);\n      } else {\n        message.error(res.msg);\n      }\n    } catch (error) {\n      message.error('获取校区列表失败，请重试');\n    }\n  };\n\n  useEffect(() => {\n    fetchSchoolList();\n  }, []);\n\n  // 每当查询条件改变时触发查询\n  useEffect(() => {\n    actionRef.current?.reload();\n  }, [selectedSchool]);\n\n  const columns: ProColumns<API.User>[] = [\n    {\n      title: 'UID',\n      dataIndex: 'uid',\n      key: 'uid',\n      hideInTable: true,\n    },\n    {\n      title: '设备类型',\n      dataIndex: 'deviceType',\n      hideInTable: true,\n      hideInSearch: true,\n    },\n    {\n      title: '用户类型',\n      dataIndex: 'userType',\n      hideInTable: true,\n      hideInSearch: true,\n    },\n    {\n      title: '头像',\n      dataIndex: ['userWx', 'avatar'],\n      hideInSearch: true,\n      render: (avatar) => avatar ? (\n        <Image\n          src={avatar}\n          width={40}\n          height={40}\n          style={{ borderRadius: '50%', objectFit: 'cover' }}\n          preview={{\n            mask: <EyeOutlined />,\n            maskStyle: { borderRadius: '50%' }\n          }}\n        />\n      ) : null,\n    },\n    {\n      title: '昵称',\n      dataIndex: 'nickname',\n      render: (_, record) => record.userWx?.nickname,\n    },\n    {\n      title: '手机号',\n      dataIndex: 'phone',\n      render: (_, record) => record.userWx?.phone,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      valueType: 'dateTimeRange',\n      hideInTable: true,\n      search: {\n        transform: (value) => {\n          return {\n            createTimeBegin: value[0],\n            createTimeEnd: value[1],\n          };\n        },\n      },\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updateTime',\n      valueType: 'dateTimeRange',\n      hideInTable: true,\n      search: {\n        transform: (value) => {\n          return {\n            updateTimeBegin: value[0],\n            updateTimeEnd: value[1],\n          };\n        },\n      },\n    },\n    {\n      title: '最后登录时间',\n      dataIndex: 'loginTime',\n      valueType: 'dateTimeRange',\n      hideInTable: true,\n      search: {\n        transform: (value) => {\n          return {\n            loginTimeBegin: value[0],\n            loginTimeEnd: value[1],\n          };\n        },\n      },\n    },\n    {\n      title: '登录IP',\n      dataIndex: 'loginIp',\n      hideInTable: true,\n      hideInSearch: true,\n    },\n    {\n      title: '登录地区',\n      dataIndex: 'loginRegion',\n      hideInSearch: true,\n    },\n    {\n      title: '创建人ID',\n      dataIndex: 'createId',\n      hideInTable: true,\n      hideInSearch: true,\n    },\n    {\n      title: '更新人ID',\n      dataIndex: 'updateId',\n      hideInTable: true,\n      hideInSearch: true,\n    },\n    {\n      title: 'OpenID',\n      dataIndex: 'openid',\n      hideInTable: true,\n    },\n    {\n      title: '积分',\n      dataIndex: ['userWx', 'points'],\n      hideInTable: true,\n      hideInSearch: true, \n    },\n    {\n      title: '是否跑腿',\n      dataIndex: 'isRunner',\n      valueEnum: {\n        0: { text: '否', status: 'Default' },\n        1: { text: '是', status: 'Success' },\n      },\n      render: (_, record) => record.userWx?.isRunner === 1 ? '是' : '否',\n    },\n    {\n      title: '下单权限',\n      dataIndex: 'canOrder',\n      valueEnum: {\n        0: { text: '禁止', status: 'Error' },\n        1: { text: '允许', status: 'Success' },\n      },\n      render: (_, record) => record.userWx?.canOrder === 1 ? '允许' : '禁止',\n    },\n    {\n      title: '接单权限',\n      dataIndex: 'canTake',\n      valueEnum: {\n        0: { text: '禁止', status: 'Error' },\n        1: { text: '允许', status: 'Success' },\n      },\n      render: (_, record) => record.userWx?.canTake === 1 ? '允许' : '禁止',\n    },\n    {\n      title: '学校ID',\n      dataIndex: ['userWx', 'schoolId'],\n      hideInTable: true,\n    },\n    {\n      title: '真实姓名',\n      dataIndex: 'realname',\n      hideInTable: true,\n      render: (_, record) => record.userWx?.realname,\n    },\n    {\n      title: '性别',\n      dataIndex: 'gender',\n      valueEnum: {\n        0: { text: '女', status: 'Default' },\n        1: { text: '男', status: 'Success' },\n      },\n      hideInTable: true,\n      render: (_, record) => record.userWx?.gender === 1 ? '男' : '女',\n    },\n    {\n      title: '用户类型',\n      dataIndex: 'userType',\n      valueEnum: {\n        0: { text: '超级管理员', status: 'Success' },\n        1: { text: '管理员', status: 'Processing' },\n        2: { text: '普通管理员', status: 'Default' },\n        3: { text: '普通用户', status: 'Default' },\n        4: { text: '跑腿用户', status: 'Processing' },\n      },\n    },\n    {\n      title: '操作',\n      dataIndex: 'option',\n      valueType: 'option',\n      render: (_, record) => [\n        <a\n          key=\"detail\"\n          onClick={() => {\n            setCurrentRow(record);\n            handleDetailModalVisible(true);\n          }}\n        >\n          详情\n        </a>,\n        <a\n          key=\"edit\"\n          onClick={() => {\n            handleUpdateModalVisible(true);\n            setCurrentRow(record);\n          }}\n        >\n          编辑\n        </a>,\n      ],\n    },\n  ];\n\n  const detailColumns: { label: string; key: string }[] = [\n    { label: 'UID', key: 'uid' },\n    { label: '创建时间', key: 'createTime' },\n    { label: '更新时间', key: 'updateTime' },\n    { label: '最后登录时间', key: 'loginTime' },\n    { label: '登录地区', key: 'loginRegion' },\n    { label: '创建人ID', key: 'createId' },\n  ];\n\n  const handleEdit = async (fields: API.UserWx) => {\n    const hide = message.loading('正在更新...');\n    try {\n      const res = await editAgent({\n        id: currentRow?.userWx?.id,\n        uid: currentRow?.userWx?.uid,\n        canOrder: fields.canOrder,\n        canTake: fields.canTake,\n      });\n      if (res.code === 200) {\n        message.success('更新成功');\n        handleUpdateModalVisible(false);\n        actionRef.current?.reload();\n        return true;\n      }\n      message.error(res.msg || '更新失败');\n      return false;\n    } catch (error) {\n      message.error('更新失败，请重试');\n      return false;\n    } finally {\n      hide();\n    }\n  };\n\n  const handleRemove = async (selectedRows: API.UserWx[]) => {\n    message.warning('功能暂未实现');\n    return false;\n  };\n\n  return (\n    <PageContainer>\n      <div style={{ marginBottom: 16, display: 'flex', gap: 16, flexWrap: 'wrap', alignItems: 'center' }}>\n        <Select\n          allowClear\n          showSearch\n          style={{ width: 200 }}\n          placeholder=\"请选择校区\"\n          optionFilterProp=\"label\"\n          options={schoolOptions}\n          value={selectedSchool}\n          onChange={(value) => setSelectedSchool(value)}\n          filterOption={(input, option) =>\n            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())\n          }\n        />\n        <Button onClick={() => {\n          setSelectedSchool(undefined);\n          actionRef.current?.reload();\n        }}>\n          重置\n        </Button>\n      </div>\n\n      <ProTable<API.User>\n        headerTitle=\"小程序用户列表\"\n        actionRef={actionRef}\n        rowKey=\"id\"\n        search={{\n          labelWidth: 120,\n        }}\n        toolBarRender={() => [\n          \n        ]}\n        request={async (params, sorter, filter) => {\n          try {\n            const queryParams: API.listWxParams = {\n              userWxQuery: {\n                // 基本查询条件\n                uid: params.uid ? Number(params.uid) : undefined,\n                openid: params.openid,\n                nickname: params.nickname,\n                phone: params.phone,\n                isRunner: params.isRunner ? Number(params.isRunner) : undefined,\n                canOrder: params.canOrder ? Number(params.canOrder) : undefined,\n                canTake: params.canTake ? Number(params.canTake) : undefined,\n                schoolId: selectedSchool, // 保持校区查询不变\n                realname: params.realname,\n                gender: params.gender ? Number(params.gender) : undefined,\n                loginRegion: params.loginRegion,\n                userType: params.userType ? Number(params.userType) : undefined,\n                createId: params.createId ? Number(params.createId) : undefined,\n              },\n              pageQuery: {\n                pageSize: params.pageSize,\n                pageNum: params.current,\n              },\n            };\n\n            // 添加时间范围参数\n            if (params.createTimeBegin) {\n              queryParams.userWxQuery[`params['createTimeBegin']`] = params.createTimeBegin;\n              queryParams.userWxQuery[`params['createTimeEnd']`] = params.createTimeEnd;\n            }\n            if (params.loginTimeBegin) {\n              queryParams.userWxQuery[`params['loginTimeBegin']`] = params.loginTimeBegin;\n              queryParams.userWxQuery[`params['loginTimeEnd']`] = params.loginTimeEnd;\n            }\n            if (params.updateTimeBegin) {\n              queryParams.userWxQuery[`params['updateTimeBegin']`] = params.updateTimeBegin;\n              queryParams.userWxQuery[`params['updateTimeEnd']`] = params.updateTimeEnd;\n            }\n\n            // 移除所有空值\n            const cleanParams = JSON.parse(JSON.stringify(queryParams));\n\n            // 移除 userWxQuery 中的空值\n            Object.keys(cleanParams.userWxQuery).forEach(key => {\n              if (cleanParams.userWxQuery[key] === undefined) {\n                delete cleanParams.userWxQuery[key];\n              }\n            });\n\n            console.log('查询参数:', cleanParams);\n\n            const res = await list(cleanParams);\n            return {\n              data: res.rows || [],\n              success: res.code === 200,\n              total: res.total || 0,\n            };\n          } catch (error) {\n            message.error('获取数据失败');\n            return {\n              data: [],\n              success: false,\n              total: 0,\n            };\n          }\n        }}\n        columns={columns}\n      />\n\n      <ModalForm\n        title=\"编辑用户\"\n        width=\"400px\"\n        visible={updateModalVisible}\n        onVisibleChange={handleUpdateModalVisible}\n        onFinish={handleEdit}\n        initialValues={{\n          canOrder: currentRow?.userWx?.canOrder,\n          canTake: currentRow?.userWx?.canTake,\n        }}\n        modalProps={{\n          destroyOnClose: true,\n          onCancel: () => {\n            handleUpdateModalVisible(false);\n            setCurrentRow(undefined);\n          }\n        }}\n        request={async () => {\n          return {\n            canOrder: currentRow?.userWx?.canOrder,\n            canTake: currentRow?.userWx?.canTake,\n          };\n        }}\n      >\n        <ProFormSelect\n          name=\"canOrder\"\n          label=\"下单权限\"\n          options={[\n            { label: '允许', value: 1 },\n            { label: '禁止', value: 0 },\n          ]}\n          rules={[{ required: true, message: '请选择下单权限' }]}\n        />\n        <ProFormSelect\n          name=\"canTake\"\n          label=\"接单权限\"\n          options={[\n            { label: '允许', value: 1 },\n            { label: '禁止', value: 0 },\n          ]}\n          rules={[{ required: true, message: '请选择接单权限' }]}\n        />\n      </ModalForm>\n\n      <Modal\n        title=\"用户详情\"\n        width=\"600px\"\n        open={detailModalVisible}\n        onCancel={() => handleDetailModalVisible(false)}\n        footer={null}\n      >\n        <Descriptions column={1}>\n          {detailColumns.map(({ label, key }) => (\n            <Descriptions.Item key={key} label={label}>\n              {currentRow?.[key as keyof API.User]}\n            </Descriptions.Item>\n          ))}\n          <Descriptions.Item label=\"用户信息\">\n            <Descriptions column={1}>\n              <Descriptions.Item label=\"OpenID\">{currentRow?.userWx?.openid}</Descriptions.Item>\n              <Descriptions.Item label=\"昵称\">{currentRow?.userWx?.nickname}</Descriptions.Item>\n              <Descriptions.Item label=\"手机号\">{currentRow?.userWx?.phone}</Descriptions.Item>\n              <Descriptions.Item label=\"积分\">{currentRow?.userWx?.points}</Descriptions.Item>\n              <Descriptions.Item label=\"是否跑腿\">\n                {currentRow?.userWx?.isRunner === 1 ? '是' : '否'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"下单权限\">\n                {currentRow?.userWx?.canOrder === 1 ? '允许' : '禁止'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"接单权限\">\n                {currentRow?.userWx?.canTake === 1 ? '允许' : '禁止'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"真实姓名\">{currentRow?.userWx?.realname}</Descriptions.Item>\n              <Descriptions.Item label=\"性别\">\n                {currentRow?.userWx?.gender === 1 ? '男' : '女'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"绑定学校\">{currentRow?.userWx?.schoolName}</Descriptions.Item>\n              <Descriptions.Item label=\"头像\">\n                {currentRow?.userWx?.avatar && (\n                  <Image\n                    width={100}\n                    src={currentRow.userWx.avatar}\n                    style={{ borderRadius: '50%', objectFit: 'cover' }}\n                  />\n                )}\n              </Descriptions.Item>\n            </Descriptions>\n          </Descriptions.Item>\n        </Descriptions>\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default TableList;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/userManage/xcx/index.tsx"}, "10": {"name": "system", "icon": "setting", "path": "/system", "parentId": "ant-design-pro-layout", "id": "10", "absPath": "/system"}, "11": {"path": "/system/config", "name": "configuration", "perms": "system:system:config:view", "access": "routeFilter", "file": "@/pages/system/config/index.tsx", "parentId": "10", "id": "11", "absPath": "/system/config", "__content": "import { <PERSON><PERSON><PERSON><PERSON>, ProCard } from '@ant-design/pro-components';\nimport { Button, Form, Input, InputNumber, message, Space } from 'antd';\nimport React, { useState, useEffect } from 'react';\nimport { useModel } from '@umijs/max';\nimport {\n  ClockCircleOutlined,\n  ProjectOutlined,\n  CopyrightOutlined,\n  HeartOutlined,\n  PictureOutlined,\n  HomeOutlined,\n} from '@ant-design/icons';\nimport { edit7 as editConfig } from '@/services/test-swagger/systemController';\n\n// 定义配置类型\ninterface DaidaidaConfig {\n  name: string;\n  version: string;\n  copyrightYear: string;\n  payCancelTtl: number;\n  autoCompleteTtl: number;\n  completionImagesLimit: number;\n  creditUpperLimit: number;\n  creditLowerLimit: number;\n  creditDeduction: number;\n  maxAddress: number;\n}\n\nconst ConfigPage: React.FC = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  \n  // 获取全局初始状态\n  const { initialState } = useModel('@@initialState');\n  const config = initialState?.currentUser?.config as DaidaidaConfig;\n\n  // 当配置数据加载完成后，设置表单初始值\n  useEffect(() => {\n    if (config) {\n      form.setFieldsValue(config);\n    }\n  }, [config]);\n\n  const handleSave = async (values: DaidaidaConfig) => {\n    setLoading(true);\n    try {\n      const response = await editConfig(values);\n      if (response.code === 200) {\n        message.success('保存成功');\n      } else {\n        message.error(response.msg || '保存失败');\n      }\n    } catch (error) {\n      message.error('保存失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <PageContainer>\n      <ProCard>\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n        >\n          <ProCard\n            title={\n              <Space>\n                <ProjectOutlined />\n                <span>基本设置</span>\n              </Space>\n            }\n            headerBordered\n            bordered\n            style={{ marginBottom: 16 }}\n          >\n            <Form.Item\n              label=\"项目名称\"\n              name=\"name\"\n              rules={[{ required: true, message: '请输入项目名称' }]}\n            >\n              <Input placeholder=\"请输入项目名称\" />\n            </Form.Item>\n\n            <Form.Item\n              label=\"版本号\"\n              name=\"version\"\n              rules={[{ required: true, message: '请输入版本号' }]}\n            >\n              <Input placeholder=\"请输入版本号\" />\n            </Form.Item>\n\n            <Form.Item\n              label=\"版权年份\"\n              name=\"copyrightYear\"\n              rules={[{ required: true, message: '请输入版权年份' }]}\n            >\n              <Input placeholder=\"请输入版权年份\" prefix={<CopyrightOutlined />} />\n            </Form.Item>\n          </ProCard>\n\n          <ProCard\n            title={\n              <Space>\n                <ClockCircleOutlined />\n                <span>时间设置</span>\n              </Space>\n            }\n            headerBordered\n            bordered\n            style={{ marginBottom: 16 }}\n          >\n            <Form.Item\n              label=\"支付超时时间\"\n              name=\"payCancelTtl\"\n              rules={[{ required: true, message: '请输入支付超时时间' }]}\n              extra=\"超时未支付自动取消订单的时间（分钟）\"\n            >\n              <InputNumber\n                min={1}\n                max={1440}\n                style={{ width: 200 }}\n                addonAfter=\"分钟\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              label=\"自动完成时间\"\n              name=\"autoCompleteTtl\"\n              rules={[{ required: true, message: '请输入自动完成时间' }]}\n              extra=\"超时未完成自动完成订单的时间（小时）\"\n            >\n              <InputNumber\n                min={1}\n                max={720}\n                style={{ width: 200 }}\n                addonAfter=\"小时\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              label=\"完成凭证上限\"\n              name=\"completionImagesLimit\"\n              rules={[{ required: true, message: '请输入完成凭证上限' }]}\n              extra=\"订单完成凭证图片上传数量上限\"\n            >\n              <InputNumber\n                min={1}\n                max={10}\n                style={{ width: 200 }}\n                addonAfter=\"张\"\n                prefix={<PictureOutlined />}\n              />\n            </Form.Item>\n          </ProCard>\n\n          <ProCard\n            title={\n              <Space>\n                <HeartOutlined />\n                <span>信用分设置</span>\n              </Space>\n            }\n            headerBordered\n            bordered\n            style={{ marginBottom: 16 }}\n          >\n            <Form.Item\n              label=\"信用分上限\"\n              name=\"creditUpperLimit\"\n              rules={[{ required: true, message: '请输入信用分上限' }]}\n              extra=\"用户初始信用分值\"\n            >\n              <InputNumber\n                min={0}\n                style={{ width: 200 }}\n                addonAfter=\"分\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              label=\"信用分下限\"\n              name=\"creditLowerLimit\"\n              rules={[{ required: true, message: '请输入信用分下限' }]}\n              extra=\"用户最低信用分值\"\n            >\n              <InputNumber\n                min={0}\n                style={{ width: 200 }}\n                addonAfter=\"分\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              label=\"信用分扣除值\"\n              name=\"creditDeduction\"\n              rules={[{ required: true, message: '请输入信用分扣除值' }]}\n              extra=\"每次违规扣除的信用分值\"\n            >\n              <InputNumber\n                min={0}\n                style={{ width: 200 }}\n                addonAfter=\"分\"\n              />\n            </Form.Item>\n          </ProCard>\n\n          <ProCard\n            title={\n              <Space>\n                <HomeOutlined />\n                <span>用户限制</span>\n              </Space>\n            }\n            headerBordered\n            bordered\n            style={{ marginBottom: 16 }}\n          >\n            <Form.Item\n              label=\"地址数量上限\"\n              name=\"maxAddress\"\n              rules={[{ required: true, message: '请输入用户地址数量上限' }]}\n              extra=\"用户可以保存的最大地址数量\"\n            >\n              <InputNumber\n                min={1}\n                max={20}\n                style={{ width: 200 }}\n                addonAfter=\"个\"\n              />\n            </Form.Item>\n          </ProCard>\n\n          <Form.Item>\n            <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n              保存配置\n            </Button>\n          </Form.Item>\n        </Form>\n      </ProCard>\n    </PageContainer>\n  );\n};\n\nexport default ConfigPage;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/system/config/index.tsx"}, "12": {"path": "/system/permission", "name": "permission", "perms": "system:system:perms:view", "access": "routeFilter", "file": "@/pages/system/permission/index.tsx", "parentId": "10", "id": "12", "absPath": "/system/permission", "__content": "import React, { useEffect, useState } from 'react';\nimport { Card, Tree, Select, Button, message, Modal, Form, Input, InputNumber } from 'antd';\nimport type { DataNode } from 'antd/es/tree';\nimport { listPerms, list, roleperms, roleperms1, rolepermsDel } from '@/services/test-swagger/systemController';\n\nconst PermissionPage: React.FC = () => {\n  const [treeData, setTreeData] = useState<DataNode[]>([]);\n  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);\n  const [roleType, setRoleType] = useState<number>();\n  const [form] = Form.useForm();\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);\n  const [deleteCheckedKeys, setDeleteCheckedKeys] = useState<React.Key[]>([]);\n\n  // 将后端返回的权限数据转换为Tree组件需要的格式\n  const convertToTreeData = (perms: API.Perm[]): DataNode[] => {\n    return perms.map(perm => ({\n      title: `${perm.name}${perm.perms ? ` (${perm.perms})` : ''} @${perm.id}@`,\n      key: perm.id,\n      children: perm.children ? convertToTreeData(perm.children) : undefined,\n    }));\n  };\n\n  // 初始化加载所有权限数据\n  useEffect(() => {\n    const fetchAllPerms = async () => {\n      try {\n        const result = await listPerms();\n        if (result) {\n          const formattedData = convertToTreeData(result);\n          setTreeData(formattedData);\n        }\n      } catch (error) {\n        console.error('获取权限列表失败:', error);\n      }\n    };\n    fetchAllPerms();\n  }, []);\n\n  // 根据角色类型获取已有权限\n  const fetchRolePerms = async (userType: number) => {\n    try {\n      const result = await list({ userType });\n      if (result?.data) {\n        const permIds = result.data.map(perm => perm.id);\n        setCheckedKeys(permIds);\n      }\n    } catch (error) {\n      console.error('获取角色权限失败:', error);\n    }\n  };\n\n  // 处理角色选择变化\n  const handleRoleChange = (value: number) => {\n    setRoleType(value);\n    fetchRolePerms(value);\n  };\n\n  // 处理权限选择变化\n  const onCheck = (checked: any, info: any) => {\n    const checkedKeys = Array.isArray(checked) ? checked : checked.checked;\n    setCheckedKeys(checkedKeys);\n    console.log('选中的权限:', checkedKeys, info);\n  };\n\n  // 处理保存权限\n  const handleSave = async () => {\n    if (!roleType) {\n      message.warning('请先选择角色类型');\n      return;\n    }\n\n    try {\n      // 将 checkedKeys 转换为 number[]\n      const selectedPerms = checkedKeys\n        .map(key => Number(key))\n        .filter(key => !isNaN(key));\n\n      await roleperms(\n        { userType: roleType },\n        selectedPerms\n      );\n      \n      message.success('权限保存成功');\n    } catch (error) {\n      console.error('保存权限失败:', error);\n      message.error('保存权限失败');\n    }\n  };\n\n  // 处理添加权限\n  const handleAdd = () => {\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 处理模态框确认\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      await roleperms1(values);\n      message.success('添加成功');\n      setIsModalVisible(false);\n      // 重新加载权限树\n      const result = await listPerms();\n      if (result) {\n        const formattedData = convertToTreeData(result);\n        setTreeData(formattedData);\n      }\n    } catch (error) {\n      console.error('添加权限失败:', error);\n      message.error('添加失败');\n    }\n  };\n\n  // 处理删除模态框的确认\n  const handleDeleteOk = async () => {\n    if (deleteCheckedKeys.length === 0) {\n      message.warning('请选择要删除的权限');\n      return;\n    }\n\n    try {\n      const deleteIds = deleteCheckedKeys\n        .map(key => Number(key))\n        .filter(key => !isNaN(key));\n\n      await rolepermsDel(deleteIds);\n      message.success('删除成功');\n      setIsDeleteModalVisible(false);\n      setDeleteCheckedKeys([]);\n      \n      // 重新加载权限树\n      const result = await listPerms();\n      if (result) {\n        const formattedData = convertToTreeData(result);\n        setTreeData(formattedData);\n      }\n    } catch (error) {\n      console.error('删除权限失败:', error);\n      message.error('删除失败');\n    }\n  };\n\n  // 处理删除权限的选择变化\n  const onDeleteCheck = (checked: any) => {\n    const checkedKeys = Array.isArray(checked) ? checked : checked.checked;\n    setDeleteCheckedKeys(checkedKeys);\n  };\n\n  // 角色类型选项\n  const roleOptions = [\n    { label: '超级管理员', value: 0 },\n    { label: '校区代理', value: 1 },\n    { label: '普通管理员', value: 2 },\n    { label: '普通用户', value: 3 },\n    { label: '跑腿用户', value: 4 },\n  ];\n\n  return (\n    <Card title=\"权限管理\">\n      <div style={{ marginBottom: 16, display: 'flex', gap: 16 }}>\n        <Select\n          style={{ width: 200 }}\n          placeholder=\"请选择角色类型\"\n          onChange={handleRoleChange}\n          value={roleType}\n          options={roleOptions}\n        />\n        <Button \n          type=\"primary\" \n          onClick={handleSave}\n          disabled={!roleType} // 禁用条件：没有选择角色类型\n        >\n          保存权限\n        </Button>\n        <Button type=\"primary\" onClick={handleAdd}>\n          添加权限\n        </Button>\n        <Button danger onClick={() => setIsDeleteModalVisible(true)}>\n          删除权限\n        </Button>\n      </div>\n      <Tree\n        checkable\n        defaultExpandAll\n        checkedKeys={checkedKeys}\n        onCheck={onCheck}\n        treeData={treeData}\n      />\n\n      <Modal\n        title=\"添加权限\"\n        open={isModalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setIsModalVisible(false)}\n      >\n        <Form form={form} layout=\"vertical\">\n        <Form.Item\n            name=\"id\"\n            label=\"ID\"\n          >\n            <InputNumber style={{ width: '100%' }} />\n          </Form.Item>\n          <Form.Item\n            name=\"parentId\"\n            label=\"父级ID\"\n            initialValue={0}\n          >\n            <InputNumber style={{ width: '100%' }} />\n          </Form.Item>\n          <Form.Item\n            name=\"name\"\n            label=\"权限名称\"\n            rules={[{ required: true, message: '请输入权限名称' }]}\n          >\n            <Input />\n          </Form.Item>\n          <Form.Item\n            name=\"perms\"\n            label=\"权限标识\"\n            // rules={[{ required: true, message: '请输入权限标识' }]}\n          >\n            <Input />\n          </Form.Item>\n          <Form.Item\n            name=\"sort\"\n            label=\"排序\"\n            initialValue={0}\n            rules={[{ required: true, message: '请输入排序号' }]}\n          >\n            <InputNumber style={{ width: '100%' }} />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 删除权限模态框 */}\n      <Modal\n        title=\"删除权限\"\n        open={isDeleteModalVisible}\n        onOk={handleDeleteOk}\n        onCancel={() => {\n          setIsDeleteModalVisible(false);\n          setDeleteCheckedKeys([]);\n        }}\n        width={600}\n      >\n        <p style={{ marginBottom: 16 }}>请选择要删除的权限（选中父节点将同时删除其所有子节点）：</p>\n        <Tree\n          checkable\n          defaultExpandAll\n          checkedKeys={deleteCheckedKeys}\n          onCheck={onDeleteCheck}\n          treeData={treeData}\n        />\n      </Modal>\n    </Card>\n  );\n};\n\nexport default PermissionPage;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/system/permission/index.tsx"}, "13": {"path": "/system/monitor", "name": "monitor", "perms": "system:system:monitor:view", "access": "routeFilter", "file": "@/pages/system/monitor/index.tsx", "parentId": "10", "id": "13", "absPath": "/system/monitor", "__content": "import React, { useEffect, useState } from 'react';\nimport { Card, Row, Col, Progress, Descriptions, Table, Spin } from 'antd';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { getServerMonitor } from '@/services/test-swagger/systemController';\nimport {\n  DashboardOutlined,\n  DesktopOutlined,\n  DatabaseOutlined,\n  HddOutlined,\n  SyncOutlined,\n} from '@ant-design/icons';\nimport styles from './index.less';\n\nconst Monitor: React.FC = () => {\n  const [serverInfo, setServerInfo] = useState<API.Server>();\n  const [loading, setLoading] = useState(true);\n  const [lastUpdate, setLastUpdate] = useState<string>('');\n\n  const fetchServerInfo = async () => {\n    try {\n      const response = await getServerMonitor();\n      if (response.code === 200) {\n        setServerInfo(response.data);\n        setLastUpdate(new Date().toLocaleTimeString());\n      }\n    } catch (error) {\n      console.error('获取服务器信息失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchServerInfo();\n    // 每10秒刷新一次数据\n    const timer = setInterval(fetchServerInfo, 10000);\n    return () => clearInterval(timer);\n  }, []);\n\n  const diskColumns = [\n    { title: '磁盘名称', dataIndex: 'typeName', key: 'typeName' },\n    { title: '文件系统', dataIndex: 'sysTypeName', key: 'sysTypeName' },\n    { title: '总容量', dataIndex: 'total', key: 'total' },\n    { title: '已用容量', dataIndex: 'used', key: 'used' },\n    { title: '剩余容量', dataIndex: 'free', key: 'free' },\n    {\n      title: '使用率',\n      dataIndex: 'usage',\n      key: 'usage',\n      render: (usage: number) => (\n        <Progress \n          percent={Number(usage.toFixed(2))}\n          status={usage > 90 ? 'exception' : usage > 70 ? 'warning' : 'normal'}\n        />\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer\n      extra={[\n        <span key=\"lastUpdate\" style={{ marginRight: 16 }}>\n          最后更新时间：{lastUpdate}\n        </span>,\n        <SyncOutlined \n          key=\"refresh\" \n          onClick={fetchServerInfo}\n          style={{ fontSize: 18, cursor: 'pointer' }} \n        />,\n      ]}\n    >\n      <Spin spinning={loading} delay={500}>\n        <Row gutter={[16, 16]}>\n          {/* CPU信息 */}\n          <Col span={12}>\n            <Card \n              title={<><DashboardOutlined /> CPU信息</>}\n              className={styles.monitorCard}\n            >\n              <Row justify=\"center\" align=\"middle\">\n                <Col span={12} className={styles.progressWrapper}>\n                  <Progress\n                    type=\"dashboard\"\n                    percent={Number((serverInfo?.cpu?.used || 0).toFixed(2))}\n                    status={serverInfo?.cpu?.used > 90 ? 'exception' : 'normal'}\n                  />\n                  <div className={styles.progressLabel}>CPU使用率</div>\n                </Col>\n                <Col span={12}>\n                  <Descriptions column={1} size=\"small\">\n                    <Descriptions.Item label=\"核心数\">{serverInfo?.cpu?.cpuNum}</Descriptions.Item>\n                    <Descriptions.Item label=\"系统使用率\">{serverInfo?.cpu?.sys}%</Descriptions.Item>\n                    <Descriptions.Item label=\"用户使用率\">{serverInfo?.cpu?.used}%</Descriptions.Item>\n                    <Descriptions.Item label=\"当前空闲率\">{serverInfo?.cpu?.free}%</Descriptions.Item>\n                  </Descriptions>\n                </Col>\n              </Row>\n            </Card>\n          </Col>\n\n          {/* 内存信息 */}\n          <Col span={12}>\n            <Card \n              title={<><DatabaseOutlined /> 内存信息</>}\n              className={styles.monitorCard}\n            >\n              <Row justify=\"center\" align=\"middle\">\n                <Col span={12} className={styles.progressWrapper}>\n                  <Progress\n                    type=\"dashboard\"\n                    percent={Number((serverInfo?.mem?.usage || 0).toFixed(2))}\n                    status={serverInfo?.mem?.usage > 90 ? 'exception' : 'normal'}\n                  />\n                  <div className={styles.progressLabel}>内存使用率</div>\n                </Col>\n                <Col span={12}>\n                  <Descriptions column={1} size=\"small\">\n                    <Descriptions.Item label=\"总内存\">{serverInfo?.mem?.total} GB</Descriptions.Item>\n                    <Descriptions.Item label=\"已用内存\">{serverInfo?.mem?.used} GB</Descriptions.Item>\n                    <Descriptions.Item label=\"剩余内存\">{serverInfo?.mem?.free} GB</Descriptions.Item>\n                  </Descriptions>\n                </Col>\n              </Row>\n            </Card>\n          </Col>\n\n          {/* JVM信息 */}\n          <Col span={12}>\n            <Card \n              title={<><DesktopOutlined /> JVM信息</>}\n              className={styles.monitorCard}\n            >\n              <Descriptions column={2} size=\"small\">\n                <Descriptions.Item label=\"JDK版本\">{serverInfo?.jvm?.version}</Descriptions.Item>\n                <Descriptions.Item label=\"启动时间\">{serverInfo?.jvm?.startTime}</Descriptions.Item>\n                <Descriptions.Item label=\"运行时长\">{serverInfo?.jvm?.runTime}</Descriptions.Item>\n                <Descriptions.Item label=\"安装路径\">{serverInfo?.jvm?.home}</Descriptions.Item>\n                <Descriptions.Item label=\"已用内存\">{serverInfo?.jvm?.used} MB</Descriptions.Item>\n                <Descriptions.Item label=\"最大可用\">{serverInfo?.jvm?.max} MB</Descriptions.Item>\n              </Descriptions>\n              <Progress\n                percent={Number((serverInfo?.jvm?.usage || 0).toFixed(2))}\n                status={serverInfo?.jvm?.usage > 90 ? 'exception' : 'normal'}\n                strokeWidth={10}\n                className={styles.jvmProgress}\n              />\n            </Card>\n          </Col>\n\n          {/* 服务器信息 */}\n          <Col span={12}>\n            <Card \n              title={<><HddOutlined /> 服务器信息</>}\n              className={styles.monitorCard}\n            >\n              <Descriptions column={1} size=\"small\">\n                <Descriptions.Item label=\"服务器名称\">{serverInfo?.sys?.computerName}</Descriptions.Item>\n                <Descriptions.Item label=\"操作系统\">{serverInfo?.sys?.osName}</Descriptions.Item>\n                <Descriptions.Item label=\"系统架构\">{serverInfo?.sys?.osArch}</Descriptions.Item>\n                <Descriptions.Item label=\"服务器IP\">{serverInfo?.sys?.computerIp}</Descriptions.Item>\n                <Descriptions.Item label=\"项目路径\">{serverInfo?.sys?.userDir}</Descriptions.Item>\n              </Descriptions>\n            </Card>\n          </Col>\n\n          {/* 磁盘信息 */}\n          <Col span={24}>\n            <Card \n              title={<><HddOutlined /> 磁盘信息</>}\n              className={styles.monitorCard}\n            >\n              <Table\n                dataSource={serverInfo?.sysFiles}\n                columns={diskColumns}\n                pagination={false}\n                rowKey=\"dirName\"\n              />\n            </Card>\n          </Col>\n        </Row>\n      </Spin>\n    </PageContainer>\n  );\n};\n\nexport default Monitor;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/system/monitor/index.tsx"}, "14": {"path": "/system/carousel", "name": "carousel", "perms": "system:system:carousel:view", "access": "routeFilter", "file": "@/pages/system/carousel/index.tsx", "parentId": "10", "id": "14", "absPath": "/system/carousel", "__content": "import { <PERSON><PERSON><PERSON>r } from '@ant-design/pro-layout';\nimport { Card, Row, Col, Upload, message, Image, Spin, Button, Modal } from 'antd';\nimport { PlusOutlined, DeleteOutlined } from '@ant-design/icons';\nimport React, { useState, useEffect } from 'react';\nimport { getCarouselImages, updateCarouselImage, addCarouselImage, deleteCarouselImage } from '@/services/test-swagger/loginController';\nimport type { RcFile } from 'antd/es/upload/interface';\n\nconst CarouselManage: React.FC = () => {\n  const [images, setImages] = useState<string[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [uploadingIndex, setUploadingIndex] = useState<number | null>(null);\n  const [addingNew, setAddingNew] = useState(false);\n  const [deletingIndex, setDeletingIndex] = useState<number | null>(null);\n\n  const fetchImages = async () => {\n    setLoading(true);\n    try {\n      const res = await getCarouselImages();\n      if (res.code === 200 && res.data) {\n        // 数据中已经是base64字符串，需要加上前缀才能显示\n        const formattedImages = res.data.map(base64 => `${base64}`);\n        setImages(formattedImages);\n      } else {\n        message.error(res.msg || '获取轮播图失败');\n      }\n    } catch (error) {\n      message.error('获取轮播图失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchImages();\n  }, []);\n\n  const beforeUpload = (file: RcFile) => {\n    const isImage = file.type.startsWith('image/');\n    if (!isImage) {\n      message.error('只能上传图片文件！');\n      return false;\n    }\n    const isLt2M = file.size / 1024 / 1024 < 5;\n    if (!isLt2M) {\n      message.error('图片大小不能超过 5MB！');\n      return false;\n    }\n    return true;\n  };\n\n  const handleUpload = async (index: number, file: RcFile) => {\n    if (!beforeUpload(file)) {\n      return;\n    }\n\n    setUploadingIndex(index);\n    try {\n      // 将文件转换为base64\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = async () => {\n        const base64Content = (reader.result as string).split(',')[1]; // 移除 \"data:image/jpeg;base64,\" 前缀\n        try {\n          const res = await updateCarouselImage(index, base64Content);\n          if (res.code === 200) {\n            message.success('更新成功');\n            fetchImages(); // 重新获取图片列表\n          } else {\n            message.error(res.msg || '更新失败');\n          }\n        } catch (error) {\n          message.error('更新失败');\n        }\n      };\n    } catch (error) {\n      message.error('文件处理失败');\n    } finally {\n      setUploadingIndex(null);\n    }\n  };\n\n  const handleAddNew = async (file: RcFile) => {\n    if (!beforeUpload(file)) {\n      return;\n    }\n\n    setAddingNew(true);\n    try {\n      // 将文件转换为base64\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = async () => {\n        const base64Content = (reader.result as string).split(',')[1]; // 移除 \"data:image/jpeg;base64,\" 前缀\n        try {\n          const res = await addCarouselImage(base64Content);\n          if (res.code === 200) {\n            message.success('添加成功');\n            fetchImages(); // 重新获取图片列表\n          } else {\n            message.error(res.msg || '添加失败');\n          }\n        } catch (error) {\n          message.error('添加失败');\n        }\n      };\n    } catch (error) {\n      message.error('文件处理失败');\n    } finally {\n      setAddingNew(false);\n    }\n  };\n\n  const handleDelete = async (index: number) => {\n    Modal.confirm({\n      title: '确认删除',\n      content: '确定要删除这张轮播图吗？',\n      okText: '确定',\n      cancelText: '取消',\n      onOk: async () => {\n        setDeletingIndex(index);\n        try {\n          const res = await deleteCarouselImage(index);\n          if (res.code === 200) {\n            message.success('删除成功');\n            // 从本地状态中移除被删除的图片\n            setImages(prevImages => prevImages.filter((_, i) => i !== index));\n            // 重新获取最新的图片列表\n            await fetchImages();\n          } else {\n            message.error(res.msg || '删除失败');\n          }\n        } catch (error) {\n          message.error('删除失败');\n        } finally {\n          setDeletingIndex(null);\n        }\n      },\n    });\n  };\n\n  return (\n    <PageContainer>\n      <Card \n        title=\"轮播图管理\"\n        extra={\n          <Upload\n            accept=\"image/*\"\n            showUploadList={false}\n            beforeUpload={(file) => {\n              handleAddNew(file);\n              return false;\n            }}\n          >\n            <Button \n              type=\"primary\" \n              icon={<PlusOutlined />}\n              loading={addingNew}\n            >\n              添加轮播图\n            </Button>\n          </Upload>\n        }\n      >\n        <Spin spinning={loading}>\n          <Row gutter={[16, 16]}>\n            {images.map((base64Url, index) => (\n              <Col key={index} xs={24} sm={12} md={8} lg={6}>\n                <Card\n                  hoverable\n                  cover={\n                    <div style={{ position: 'relative', height: 200 }}>\n                      <Image\n                        src={base64Url}\n                        alt={`轮播图${index + 1}`}\n                        style={{ width: '100%', height: '100%', objectFit: 'cover' }}\n                      />\n                      <div style={{ position: 'absolute', top: 8, right: 8, zIndex: 1 }}>\n                        <Button\n                          type=\"primary\"\n                          danger\n                          icon={<DeleteOutlined />}\n                          loading={deletingIndex === index}\n                          onClick={() => handleDelete(index)}\n                        />\n                      </div>\n                      <Upload\n                        accept=\"image/*\"\n                        showUploadList={false}\n                        beforeUpload={(file) => {\n                          handleUpload(index, file);\n                          return false;\n                        }}\n                      >\n                        <div\n                          style={{\n                            position: 'absolute',\n                            bottom: 0,\n                            left: 0,\n                            right: 0,\n                            background: 'rgba(0,0,0,0.6)',\n                            color: '#fff',\n                            textAlign: 'center',\n                            padding: '8px 0',\n                            cursor: 'pointer',\n                            transition: 'opacity 0.3s',\n                            opacity: uploadingIndex === index ? 0.5 : 1,\n                          }}\n                        >\n                          {uploadingIndex === index ? (\n                            <Spin size=\"small\" />\n                          ) : (\n                            <>\n                              <PlusOutlined /> 更换图片\n                            </>\n                          )}\n                        </div>\n                      </Upload>\n                    </div>\n                  }\n                >\n                  <Card.Meta\n                    title={`轮播图 ${index + 1}`}\n                    description={\n                      <div style={{ fontSize: 12, color: '#999' }}>\n                        点击下方更换图片\n                      </div>\n                    }\n                  />\n                </Card>\n              </Col>\n            ))}\n          </Row>\n        </Spin>\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default CarouselManage;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/system/carousel/index.tsx"}, "15": {"name": "order", "icon": "fileText", "path": "/order", "parentId": "ant-design-pro-layout", "id": "15", "absPath": "/order"}, "16": {"path": "/order", "perms": "order:order:view", "access": "routeFilter", "file": "@/pages/order/index.tsx", "parentId": "15", "id": "16", "absPath": "/order", "__content": "import { Page<PERSON>ontainer } from '@ant-design/pro-layout';\nimport { useRef, useState, useEffect } from 'react';\nimport type { ActionType, ProColumns } from '@ant-design/pro-table';\nimport type { ProListMetas } from '@ant-design/pro-list';\nimport ProList from '@ant-design/pro-list';\nimport { Button, Modal, Tag, Space, message, Segmented, Input, Select, DatePicker, Progress } from 'antd';\nimport { list4 as listOrders, detail, complete, add3 as cancelOrder } from '@/services/test-swagger/orderController';\nimport { list5 } from '@/services/test-swagger/schoolController';\nimport { SearchOutlined, EllipsisOutlined } from '@ant-design/icons';\nimport type { RangePickerProps } from 'antd/es/date-picker';\nimport dayjs from 'dayjs';\nimport ProTable from '@ant-design/pro-table';\nimport { history } from 'umi';\nimport { useModel } from 'umi';\n\nconst { RangePicker } = DatePicker;\n\nconst OrderList: React.FC = () => {\n  const actionRef = useRef<ActionType>();\n  const [orderStatus, setOrderStatus] = useState<string>('全部');\n  const [searchText, setSearchText] = useState<string>('');\n  const [selectedSchool, setSelectedSchool] = useState<number>();\n  const [schoolOptions, setSchoolOptions] = useState<{ label: string; value: number; }[]>([]);\n  const [serviceType, setServiceType] = useState<number>();\n  const [orderUid, setOrderUid] = useState<string>();\n  const [takerUid, setTakerUid] = useState<string>();\n  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>();\n\n  // 获取当前用户信息\n  const { initialState } = useModel('@@initialState');\n  const currentUser = initialState?.currentUser;\n\n  // 获取校区列表\n  const fetchSchoolList = async () => {\n    const res = await list5({\n      pageQuery: {\n        pageSize: 999,\n        pageNum: 1,\n      },\n      school: {}\n    });\n    const options = (res.rows || []).map((item) => ({\n      label: item.name,\n      value: item.id,\n    }));\n    setSchoolOptions(options);\n  };\n\n  useEffect(() => {\n    fetchSchoolList();\n  }, []);\n\n  // 查看详情\n  const handleViewDetail = (orderId: string) => {\n    history.push(`/order/detail/${orderId}`);\n  };\n\n  // 完成订单\n  const handleComplete = async (orderId: number) => {\n    Modal.confirm({\n      title: '确认完成',\n      content: '确定要完成这个订单吗？',\n      onOk: async () => {\n        try {\n          await complete({ orderId });\n          message.success('订单已完成');\n          actionRef.current?.reload();\n        } catch (error) {\n          message.error('操作失败，请重试');\n        }\n      },\n    });\n  };\n\n  // 取消订单\n  const handleCancel = async (orderId: number) => {\n    Modal.confirm({\n      title: '确认取消',\n      content: '确定要取消这个订单吗？',\n      onOk: async () => {\n        try {\n          await cancelOrder({ orderId });\n          message.success('订单已取消');\n          actionRef.current?.reload();\n        } catch (error) {\n          message.error('操作失败，请重试');\n        }\n      },\n    });\n  };\n\n  // 每当查询条件改变时触发查询\n  useEffect(() => {\n    actionRef.current?.reload();\n  }, [orderStatus, searchText, selectedSchool, serviceType, orderUid, takerUid, timeRange]);\n\n  // 添加状态选项的常量\n  const ORDER_STATUS_OPTIONS = [\n    { label: '全部', value: '全部' },\n    { label: '待支付', value: '0' },\n    { label: '待接单', value: '1' },\n    { label: '待配送', value: '2' },\n    { label: '配送中', value: '3' },\n    { label: '已送达', value: '4' },\n    { label: '已取消', value: '5' },\n    { label: '已完成', value: '10' },\n    { label: '已申诉', value: '11' },\n  ];\n\n  // 修改状态查询的处理函数\n  const getStatusValue = (status: string) => {\n    if (status === '全部') return undefined;\n    return Number(status);\n  };\n\n  // 添加重置函数\n  const handleReset = () => {\n    setOrderStatus('全部');\n    setSearchText('');\n    setSelectedSchool(undefined);\n    setServiceType(undefined);\n    setOrderUid(undefined);\n    setTakerUid(undefined);\n    setTimeRange(undefined);\n    actionRef.current?.reload();\n  };\n\n  const columns: ProColumns<API.OrderMain>[] = [\n    {\n      title: '标签',\n      dataIndex: 'tag',\n      width: 100,\n      render: (_, record) => (\n        <div style={{ color: '#1890ff' }}>\n          {record.tag}\n        </div>\n      ),\n    },\n    {\n      title: '服务类型',\n      dataIndex: 'serviceType',\n      width: 120,\n      render: (_, record) => (\n        <Tag color={\n          record.serviceType === 0 ? 'blue' :\n          record.serviceType === 1 ? 'green' :\n          record.serviceType === 2 ? 'purple' : 'default'\n        }>\n          {record.serviceType === 0 ? '帮取送' :\n           record.serviceType === 1 ? '代买' :\n           record.serviceType === 2 ? '万能服务' : '未知服务'}\n        </Tag>\n      ),\n    },\n    {\n      title: '具体要求',\n      dataIndex: 'detail',\n      width: 200,\n      ellipsis: true,\n      render: (text) => (\n        <div style={{ color: '#6c6c6c' }}>{text}</div>\n      ),\n    },\n    {\n      title: '订单状态',\n      dataIndex: 'status',\n      width: 100,\n      valueEnum: {\n        0: { text: '待支付', status: 'warning' },\n        1: { text: '待接单', status: 'warning' },\n        2: { text: '待配送', status: 'processing' },\n        3: { text: '配送中', status: 'processing' },\n        4: { text: '已送达', status: 'success' },\n        5: { text: '已取消', status: 'default' },\n        10: { text: '已完成', status: 'success' },\n        11: { text: '已申诉', status: 'error' },\n      },\n    },\n    {\n      title: '订单金额',\n      dataIndex: 'totalAmount',\n      width: 100,\n      render: (text) => (\n        <div style={{ color: '#a66733' }}>\n          ￥{text}\n        </div>\n      ),\n    },\n    {\n      title: '下单时间',\n      dataIndex: 'createTime',\n      width: 150,\n      render: (text) => (\n        <div style={{ color: '#6c6c6c' }}>{text}</div>\n      ),\n    },\n    {\n      title: '操作',\n      width: 80,\n      valueType: 'option',\n      render: (_, record) => [\n        <a key=\"view\" onClick={() => handleViewDetail(record.id)}>\n          查看详情\n        </a>,\n      ],\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <div style={{ marginBottom: 16, display: 'flex', gap: 16, flexWrap: 'wrap', alignItems: 'center' }}>\n        {/* 只有非校区管理员才显示校区选择 */}\n        {currentUser?.user?.userType !== 1 && (\n          <Select\n            allowClear\n            showSearch\n            style={{ width: 200 }}\n            placeholder=\"请选择校区\"\n            optionFilterProp=\"label\"\n            options={schoolOptions}\n            value={selectedSchool}\n            onChange={(value) => setSelectedSchool(value)}\n            filterOption={(input, option) =>\n              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())\n            }\n          />\n        )}\n        <Select\n          allowClear\n          style={{ width: 150 }}\n          placeholder=\"服务类型\"\n          value={serviceType}\n          onChange={(value) => setServiceType(value)}\n          options={[\n            { label: '帮取送', value: 0 },\n            { label: '代买', value: 1 },\n            { label: '万能服务', value: 2 },\n          ]}\n        />\n        <Input\n          placeholder=\"下单用户ID\"\n          style={{ width: 150 }}\n          value={orderUid}\n          onChange={(e) => setOrderUid(e.target.value)}\n        />\n        <Input\n          placeholder=\"接单用户ID\"\n          style={{ width: 150 }}\n          value={takerUid}\n          onChange={(e) => setTakerUid(e.target.value)}\n        />\n        <RangePicker\n          showTime\n          style={{ width: 380 }}\n          value={timeRange}\n          onChange={(dates) => setTimeRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}\n        />\n        <Button onClick={handleReset}>重置</Button>\n      </div>\n\n      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Segmented\n          options={ORDER_STATUS_OPTIONS}\n          value={orderStatus}\n          onChange={(value) => setOrderStatus(value.toString())}\n        />\n        <Input\n          placeholder=\"搜索订单号\"\n          prefix={<SearchOutlined />}\n          style={{ width: 200 }}\n          value={searchText}\n          onChange={(e) => setSearchText(e.target.value)}\n        />\n      </div>\n\n      <ProTable<API.OrderMain>\n        rowKey=\"id\"\n        actionRef={actionRef}\n        columns={columns}\n        search={false}\n        options={{\n          density: true,\n          fullScreen: true,\n          reload: true,\n          setting: true,\n        }}\n        \n        request={async (params) => {\n          const response = await listOrders({\n            pageQuery: {\n              pageSize: params.pageSize,\n              pageNum: params.current,\n            },\n            orderQuery: {\n              status: getStatusValue(orderStatus),\n              id: searchText ? searchText : undefined,\n              schoolId: selectedSchool,\n              serviceType,\n              orderUid: orderUid ? Number(orderUid) : undefined,\n              takerUid: takerUid ? Number(takerUid) : undefined,\n              beginTime: timeRange?.[0]?.format('YYYY-MM-DD HH:mm:ss'),\n              endTime: timeRange?.[1]?.format('YYYY-MM-DD HH:mm:ss'),\n            },\n          });\n          return {\n            data: response.rows || [],\n            success: response.code === 200,\n            total: response.total || 0,\n          };\n        }}\n        pagination={{\n          defaultPageSize: 10,\n          showSizeChanger: true,\n        }}\n        dateFormatter=\"string\"\n        headerTitle=\"订单列表\"\n      />\n    </PageContainer>\n  );\n};\n\nexport default OrderList;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/order/index.tsx"}, "17": {"path": "/order/detail/:id", "perms": "order:detail:view", "access": "routeFilter", "file": "@/pages/order/detail/index.tsx", "parentId": "15", "id": "17", "absPath": "/order/detail/:id", "__content": "import { PageContainer } from '@ant-design/pro-layout';\nimport { useParams } from 'umi';\nimport { useEffect, useState } from 'react';\nimport { Card, Descriptions, Tag, Divider, message, Avatar, Image, Space, Button, Dropdown, Steps, Modal, Collapse, Form, Input, Select } from 'antd';\nimport { detail, cancel, accept } from '@/services/test-swagger/orderController';\nimport { EllipsisOutlined, ReloadOutlined, FileOutlined, FileExcelOutlined, FileImageOutlined, FilePdfOutlined, FileWordOutlined, FilePptOutlined } from '@ant-design/icons';\nimport { getAppeal } from '@/services/test-swagger/orderAppealController';\nimport { listWx } from '@/services/test-swagger/userWxController';\nimport ChatDrawer from '@/components/ChatDrawer';\nimport { useModel } from '@umijs/max';\n\nconst OrderDetail: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const [orderDetail, setOrderDetail] = useState<API.OrderDetailVO>();\n  const [loading, setLoading] = useState<boolean>(true);\n  const [appealModalVisible, setAppealModalVisible] = useState(false);\n  const [appealDetail, setAppealDetail] = useState<API.OrderAppealVO[]>([]);\n  const [chatVisible, setChatVisible] = useState(false);\n  const [cancelModalVisible, setCancelModalVisible] = useState(false);\n  const [cancelForm] = Form.useForm();\n  \n  // 新增派单相关状态\n  const [assignOrderModalVisible, setAssignOrderModalVisible] = useState(false);\n  const [runnerUsers, setRunnerUsers] = useState<{label: string, value: number}[]>([]);\n  const [selectedRunner, setSelectedRunner] = useState<number>();\n  const [assignLoading, setAssignLoading] = useState(false);\n\n  // 获取当前用户信息\n  const { initialState } = useModel('@@initialState');\n  const currentUser = initialState?.currentUser;\n  const isSuperAdmin = currentUser?.user?.userType === 0;\n\n  const fetchOrderDetail = async () => {\n    try {\n      const response = await detail({ orderId: id });\n      if (response.code === 200 && response.data) {\n        setOrderDetail(response.data);\n      } else {\n        message.error('获取订单详情失败');\n      }\n    } catch (error) {\n      message.error('获取订单详情失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchOrderDetail();\n  }, [id]);\n\n  const getStatusTag = (status?: number) => {\n    const statusMap = {\n      0: { text: '待支付', color: 'warning' },\n      1: { text: '待接单', color: 'warning' },\n      2: { text: '待配送', color: 'processing' },\n      3: { text: '配送中', color: 'processing' },\n      4: { text: '已送达', color: 'success' },\n      5: { text: '已取消', color: 'default' },\n      10: { text: '已完成', color: 'success' },\n      11: { text: '已申诉', color: 'error' },\n    };\n    const status_info = statusMap[status as keyof typeof statusMap] || { text: '未知状态', color: 'default' };\n    return <Tag color={status_info.color}>{status_info.text}</Tag>;\n  };\n\n  const handleRefresh = () => {\n    setLoading(true);\n    fetchOrderDetail();\n  };\n\n  // 获取跑腿用户列表\n  const fetchRunnerUsers = async () => {\n    try {\n      const res = await listWx({\n        pageQuery: {\n          pageSize: 999,\n          pageNum: 1,\n        },\n        userWxQuery: {\n          userType: 4, // 跑腿用户类型为4\n        }\n      });\n\n      if (res.rows && res.rows.length > 0) {\n        const options = res.rows.map((user) => ({\n          label: `${user.userWx?.nickname || '未命名'} (ID: ${user.uid || ''})`,\n          value: user.uid || 0,\n        }));\n        setRunnerUsers(options);\n      }\n    } catch (error) {\n      console.error('获取跑腿用户列表失败', error);\n      message.error('获取跑腿用户列表失败');\n    }\n  };\n\n  // 打开派单对话框\n  const handleAssignOrder = () => {\n    fetchRunnerUsers();\n    setAssignOrderModalVisible(true);\n  };\n\n  // 确认派单\n  const confirmAssignOrder = async () => {\n    if (!selectedRunner) {\n      message.warning('请选择跑腿用户');\n      return;\n    }\n\n    try {\n      setAssignLoading(true);\n      // 使用接单接口，传入跑腿员ID\n      const res = await accept({\n        orderId: Number(id),\n        runnerId: selectedRunner\n      });\n      \n      if (res.code === 200) {\n        message.success('派单成功');\n        setAssignOrderModalVisible(false);\n        handleRefresh();\n        setSelectedRunner(undefined);\n      } else {\n        message.error(res.msg || '派单失败');\n      }\n    } catch (error) {\n      console.error('派单失败', error);\n      message.error('派单失败，请重试');\n    } finally {\n      setAssignLoading(false);\n    }\n  };\n\n  const getOrderSteps = (orderDetail: API.OrderDetailVO) => {\n    const steps: { title: string; description: string | React.ReactNode; time: string }[] = [];\n    \n    // 添加下单时间\n    if (orderDetail.orderMain?.createTime) {\n      steps.push({\n        title: '下单',\n        description: '用户提交订单',\n        time: orderDetail.orderMain.createTime,\n      });\n    }\n\n    // 添加支付时间\n    if (orderDetail.orderPayment?.paymentTime) {\n      steps.push({\n        title: '支付',\n        description: '用户完成支付',\n        time: orderDetail.orderPayment.paymentTime,\n      });\n    }\n\n    // 添加接单时间\n    if (orderDetail.progress?.acceptedTime) {\n      steps.push({\n        title: '接单',\n        description: `跑腿员 ${orderDetail.nicknameRunner} 接单`,\n        time: orderDetail.progress.acceptedTime,\n      });\n    }\n\n    // 添加配送时间\n    if (orderDetail.progress?.deliveringTime) {\n      steps.push({\n        title: '配送中',\n        description: '跑腿员开始配送',\n        time: orderDetail.progress.deliveringTime,\n      });\n    }\n\n    // 添加送达时间\n    if (orderDetail.progress?.deliveredTime) {\n      steps.push({\n        title: '已送达',\n        description: '订单已送达',\n        time: orderDetail.progress.deliveredTime,\n      });\n    }\n\n    // 添加完成时间\n    if (orderDetail.progress?.completedTime) {\n      steps.push({\n        title: '已完成',\n        description: '订单已完成',\n        time: orderDetail.progress.completedTime,\n      });\n    }\n\n    // 修改取消时间的步骤显示\n    if (orderDetail.progress?.cancelTime) {\n      steps.push({\n        title: '已取消',\n        description: (\n          <div>\n            <div>{orderDetail.progress.cancelReason || '订单已取消'}</div>\n            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>\n              取消人：{getUserTypeTag(orderDetail.progress.cancelUserType)}\n              <span style={{ marginLeft: '8px' }}>ID: {orderDetail.progress.cancelUserId}</span>\n            </div>\n          </div>\n        ),\n        time: orderDetail.progress.cancelTime,\n      });\n    }\n\n    // 如果是申诉状态，添加申诉步骤\n    if (orderDetail.orderMain?.status === 11 && orderDetail.orderMain?.updateTime) {\n      steps.push({\n        title: '已申诉',\n        description: (\n          <div>\n            <div>订单已申诉</div>\n            <Button type=\"link\" onClick={handleViewAppeal} style={{ padding: 0 }}>\n              查看申诉详情\n            </Button>\n          </div>\n        ),\n        time: orderDetail.orderMain.updateTime, // 使用更新时间作为申诉时间\n      });\n    }\n\n    // 按时间排序\n    return steps.sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime());\n  };\n\n  const getCurrentStep = (orderDetail: API.OrderDetailVO) => {\n    const steps = getOrderSteps(orderDetail);\n    // 返回最后一个步骤的索引\n    return steps.length - 1;\n  };\n\n  const getStepStatus = (orderDetail: API.OrderDetailVO): \"wait\" | \"process\" | \"finish\" | \"error\" => {\n    const status = orderDetail.orderMain?.status;\n    if (status === 11) return 'error';  // 申诉状态显示为 error\n    if (status === 30) return 'error';  // 取消状态显示为 error\n    if (status === 10) return 'finish'; // 完成状态显示为 finish\n    return 'process';                   // 其他状态显示为 process\n  };\n\n  // 查看申诉详情\n  const handleViewAppeal = async () => {\n    try {\n      const response = await getAppeal({ orderId: id });\n      if (response.code === 200 && response.data) {\n        setAppealDetail(response.data); // 保存整个列表\n        setAppealModalVisible(true);\n      } else {\n        message.error(response.msg);\n      }\n    } catch (error) {\n      message.error('获取申诉详情失败');\n    }\n  };\n\n  const getUserTypeTag = (type?: number) => {\n    const typeMap = {\n      0: { text: '超级管理员', color: 'red' },\n      1: { text: '校区代理', color: 'orange' },\n      2: { text: '普通管理员', color: 'blue' },\n      3: { text: '普通用户', color: 'green' },\n      4: { text: '跑腿用户', color: 'purple' },\n      5: { text: '系统', color: 'default' },\n    };\n    const typeInfo = typeMap[type as keyof typeof typeMap] || { text: '未知', color: 'default' };\n    return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;\n  };\n\n  // 渲染申诉记录的内容\n  const renderAppealContent = (appeal: API.OrderAppealVO) => (\n    <Descriptions column={1} bordered>\n      <Descriptions.Item label=\"申诉原因\">\n        {appeal.orderAppeal?.appealReason}\n      </Descriptions.Item>\n      <Descriptions.Item label=\"申诉时间\">\n        {appeal.orderAppeal?.appealTime}\n      </Descriptions.Item>\n      <Descriptions.Item label=\"申诉状态\">\n        <Tag color={appeal.orderAppeal?.appealStatus === 0 ? 'warning' : \n                    appeal.orderAppeal?.appealStatus === 1 ? 'success' : 'error'}>\n          {appeal.orderAppeal?.appealStatus === 0 ? '待处理' : \n           appeal.orderAppeal?.appealStatus === 1 ? '已通过' : '已驳回'}\n        </Tag>\n      </Descriptions.Item>\n      {appeal.orderAppeal?.remarks && (\n        <Descriptions.Item label=\"备注\">\n          {appeal.orderAppeal.remarks}\n        </Descriptions.Item>\n      )}\n      <Descriptions.Item label=\"更新时间\">\n        {appeal.orderAppeal?.updateTime}\n      </Descriptions.Item>\n      <Descriptions.Item label=\"更新人\">\n        <Space>\n          <span>ID: {appeal.orderAppeal?.updateId}</span>\n          {getUserTypeTag(appeal.orderAppeal?.updateType)}\n        </Space>\n      </Descriptions.Item>\n      {appeal.imageUrls?.length > 0 && (\n        <Descriptions.Item label=\"申诉图片\">\n          {appeal.imageUrls.map((image, index) => (\n            <Image\n              key={index}\n              src={image}\n              width={100}\n              style={{ marginRight: 8 }}\n            />\n          ))}\n        </Descriptions.Item>\n      )}\n    </Descriptions>\n  );\n\n  // 添加 getFileIcon 函数\n  const getFileIcon = (fileSuffix?: string) => {\n    if (!fileSuffix) return <FileOutlined />;\n    \n    switch (fileSuffix) {\n      case 'xlsx':\n      case 'xls':\n        return <FileExcelOutlined style={{ color: '#52c41a' }} />;\n      case 'jpg':\n      case 'jpeg':\n      case 'png':\n      case 'gif':\n      case 'bmp':\n      case 'webp':\n        return <FileImageOutlined style={{ color: '#1890ff' }} />;\n      case 'pdf':\n        return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;\n      case 'doc':\n      case 'docx':\n        return <FileWordOutlined style={{ color: '#1890ff' }} />;\n      case 'ppt':\n      case 'pptx':\n        return <FilePptOutlined style={{ color: '#ff7a45' }} />;\n      default:\n        return <FileOutlined />;\n    }\n  };\n\n  // 处理取消订单\n  const handleCancelOrder = () => {\n    setCancelModalVisible(true);\n  };\n\n  // 确认取消订单\n  const confirmCancelOrder = async (values: { cancelReason: string }) => {\n    try {\n      const hide = message.loading('正在取消订单...');\n      const response = await cancel({\n        orderId: id as string, // 直接使用字符串类型的 id\n        cancelReason: values.cancelReason,\n      });\n      hide();\n      \n      if (response.code === 200) {\n        message.success('订单取消成功');\n        setCancelModalVisible(false);\n        cancelForm.resetFields();\n        handleRefresh(); // 刷新订单详情\n      } else {\n        message.error(response.msg || '取消订单失败');\n      }\n    } catch (error) {\n      message.error('取消订单失败，请重试');\n    }\n  };\n\n  // 判断订单是否可以取消\n  const canCancelOrder = (status?: number) => {\n    return status !== 5 && status !== 11; // 已取消或已申诉状态不能取消\n  };\n\n  // 添加支付状态显示函数\n  const getPaymentStatusTag = (status?: number) => {\n    const statusMap = {\n      0: { text: '未支付', color: 'warning' },\n      1: { text: '已支付', color: 'success' },\n      2: { text: '退款中', color: 'processing' },\n      3: { text: '已退款', color: 'default' },\n    };\n    const status_info = statusMap[status as keyof typeof statusMap] || { text: '未知状态', color: 'default' };\n    return <Tag color={status_info.color}>{status_info.text}</Tag>;\n  };\n\n  return (\n    <PageContainer\n      ghost\n      header={{\n        title: orderDetail ? `订单号：${orderDetail.orderMain?.id}` : '加载中...',\n        breadcrumb: {},\n        extra: (\n          <Space>\n            <Button.Group>    \n              <Button onClick={() => setChatVisible(true)}>进入聊天</Button>\n              {isSuperAdmin && (\n                <>\n                  <Button \n                    onClick={() => handleCancelOrder()}\n                    disabled={!canCancelOrder(orderDetail?.orderMain?.status)}\n                  >\n                    取消订单\n                  </Button>\n                  {/* 只有订单状态为\"待接单\"时才显示派单按钮 */}\n                  {orderDetail?.orderMain?.status === 1 && (\n                    <Button \n                      onClick={() => handleAssignOrder()}\n                      type=\"primary\"\n                    >\n                      派单\n                    </Button>\n                  )}\n                </>\n              )}\n            </Button.Group>\n            <Button type=\"primary\" onClick={handleRefresh} icon={<ReloadOutlined />}>\n              刷新\n            </Button>\n          </Space>\n        ),\n      }}\n      content={\n        loading ? (\n          <Descriptions>\n            <Descriptions.Item>加载中...</Descriptions.Item>\n          </Descriptions>\n        ) : orderDetail && (\n          <Descriptions column={3}>\n            <Descriptions.Item label=\"订单状态\" span={1}>{getStatusTag(orderDetail.orderMain?.status)}</Descriptions.Item>\n            <Descriptions.Item label=\"服务类型\" span={1}>\n              <Tag color={\n                orderDetail.orderMain?.serviceType === 0 ? 'blue' :\n                orderDetail.orderMain?.serviceType === 1 ? 'green' : 'purple'\n              }>\n                {orderDetail.orderMain?.serviceType === 0 ? '帮取送' :\n                 orderDetail.orderMain?.serviceType === 1 ? '代买' : '万能服务'}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"订单金额\" span={1}>\n              <span style={{ color: '#a66733' }}>￥{orderDetail.orderMain?.totalAmount}</span>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"标签\" span={1}>\n              {orderDetail.orderMain?.tag && <Tag color=\"blue\">{orderDetail.orderMain.tag}</Tag>}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"重量\" span={2}>\n              {orderDetail.orderMain?.weight || '-'}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"具体要求\" span={3}>\n              {orderDetail.orderMain?.detail || '-'}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"支付状态\">\n              {getPaymentStatusTag(orderDetail.orderPayment?.paymentStatus)}\n            </Descriptions.Item>\n          </Descriptions>\n        )\n      }\n    >\n      {loading ? (\n        <Card loading bordered={false} />\n      ) : orderDetail && (\n        <>\n          <Card bordered={false} style={{ marginTop: 24 }}>\n            <Descriptions title=\"支付信息\" bordered>\n              <Descriptions.Item label=\"实付金额\" span={1}>\n                <span style={{ color: '#f50', fontWeight: 'bold' }}>\n                  ￥{orderDetail.orderPayment?.actualPayment}\n                </span>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"额外费用\" span={1}>\n                ￥{orderDetail.orderPayment?.additionalAmount || '0.00'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"支付状态\" span={1}>\n                 {getPaymentStatusTag(orderDetail.orderPayment?.paymentStatus)}\n              </Descriptions.Item>\n              {orderDetail.orderPayment?.paymentTime && (\n                <Descriptions.Item label=\"支付时间\" span={3}>\n                  {orderDetail.orderPayment.paymentTime}\n                </Descriptions.Item>\n              )}\n              {orderDetail.orderPayment?.refundTime && (\n                <Descriptions.Item label=\"退款时间\" span={3}>\n                  {orderDetail.orderPayment.refundTime}\n                </Descriptions.Item>\n              )}\n              {orderDetail.orderPayment?.refundPendingTime && (\n                <Descriptions.Item label=\"退款申请时间\" span={3}>\n                  {orderDetail.orderPayment.refundPendingTime}\n                </Descriptions.Item>\n              )}\n              {orderDetail.orderPayment?.isCouponed === 1 && (\n                <>\n                  <Descriptions.Item label=\"优惠券ID\" span={1}>\n                    {orderDetail.orderPayment.couponId}\n                  </Descriptions.Item>\n                  <Descriptions.Item label=\"优惠金额\" span={2}>\n                    ￥{orderDetail.orderPayment.discountAmount || '0.00'}\n                  </Descriptions.Item>\n                </>\n              )}\n            </Descriptions>\n          </Card>\n\n          <Card bordered={false} style={{ marginTop: 24 }}>\n            <Descriptions title=\"用户信息\" bordered>\n              <Descriptions.Item label=\"下单用户\" span={3}>\n                <Avatar src={orderDetail.avatarUser} style={{ marginRight: 8 }} />\n                {orderDetail.nicknameUser}\n              </Descriptions.Item>\n              {orderDetail.avatarRunner && (\n                <Descriptions.Item label=\"接单用户\" span={3}>\n                  <Avatar src={orderDetail.avatarRunner} style={{ marginRight: 8 }} />\n                  {orderDetail.nicknameRunner}\n                </Descriptions.Item>\n              )}\n            </Descriptions>\n          </Card>\n\n          <Card bordered={false} style={{ marginTop: 24 }}>\n            <Descriptions title=\"地址信息\" bordered>\n              {orderDetail.orderMain?.startAddress && (\n                <Descriptions.Item label=\"取货地址\" span={3}>\n                  <div>\n                    <div>{orderDetail.orderMain.startAddress.title}</div>\n                    <div style={{ color: '#666', fontSize: '13px', marginTop: '4px' }}>\n                      {orderDetail.orderMain.startAddress.detail}\n                    </div>\n                    <div style={{ color: '#666', fontSize: '13px' }}>\n                      联系人：{orderDetail.orderMain.startAddress.name} {orderDetail.orderMain.startAddress.phone}\n                    </div>\n                  </div>\n                </Descriptions.Item>\n              )}\n              {orderDetail.orderMain?.endAddress && (\n                <Descriptions.Item label=\"送货地址\" span={3}>\n                  <div>\n                    <div>{orderDetail.orderMain.endAddress.title}</div>\n                    <div style={{ color: '#666', fontSize: '13px', marginTop: '4px' }}>\n                      {orderDetail.orderMain.endAddress.detail}\n                    </div>\n                    <div style={{ color: '#666', fontSize: '13px' }}>\n                      联系人：{orderDetail.orderMain.endAddress.name} {orderDetail.orderMain.endAddress.phone}\n                    </div>\n                  </div>\n                </Descriptions.Item>\n              )}\n            </Descriptions>\n          </Card>\n\n          <Card bordered={false} style={{ marginTop: 24 }}>\n            <Descriptions title=\"订单进度\" >\n              <Descriptions.Item span={3}>\n                <div style={{ \n                  width: '100%', \n                  overflowX: 'auto',\n                  padding: ''\n                }}>\n                  <div style={{ \n                    minWidth: '800px',  // 确保有足够的最小宽度\n                  }}>\n                    <Steps\n                      current={getCurrentStep(orderDetail)}\n                      status={getStepStatus(orderDetail)}\n                      direction=\"horizontal\"\n                      items={getOrderSteps(orderDetail).map(step => ({\n                        title: step.title,\n                        description: (\n                          <div style={{ minWidth: '120px' }}>  {/* 确保每个步骤有合适的最小宽度 */}\n                            <div>{step.description}</div>\n                            <div style={{ color: '#999', fontSize: '12px' }}>{step.time}</div>\n                          </div>\n                        ),\n                      }))}\n                    />\n                  </div>\n                </div>\n              </Descriptions.Item>\n            </Descriptions>\n          </Card>\n\n          {(orderDetail.attachImages?.length > 0 || orderDetail.attachFiles?.length > 0 || orderDetail.completionImages?.length > 0) && (\n            <Card bordered={false} style={{ marginTop: 24 }}>\n              <Descriptions title=\"相关附件\" bordered>\n                {orderDetail.attachImages?.length > 0 && (\n                  <Descriptions.Item label=\"订单图片\" span={3}>\n                    {orderDetail.attachImages.map((image, index) => (\n                      <Image\n                        key={index}\n                        src={image.fileUrl}\n                        width={100}\n                        style={{ marginRight: 8 }}\n                      />\n                    ))}\n                  </Descriptions.Item>\n                )}\n                {orderDetail.attachFiles?.length > 0 && (\n                  <Descriptions.Item label=\"订单文件\" span={3}>\n                    {orderDetail.attachFiles.map((file, index) => (\n                      <Button\n                        key={index}\n                        type=\"link\"\n                        icon={getFileIcon(file.fileType)}\n                        onClick={() => window.open(file.fileUrl)}\n                        style={{ marginRight: 8 }}\n                      >\n                        {file.fileName}\n                      </Button>\n                    ))}\n                  </Descriptions.Item>\n                )}\n                {orderDetail.completionImages?.length > 0 && (\n                  <Descriptions.Item label=\"完成凭证\" span={3}>\n                    {orderDetail.completionImages.map((image, index) => (\n                      <Image\n                        key={index}\n                        src={image.fileUrl}\n                        width={100}\n                        style={{ marginRight: 8 }}\n                      />\n                    ))}\n                  </Descriptions.Item>\n                )}\n              </Descriptions>\n            </Card>\n          )}\n        </>\n      )}\n\n      {/* 修改申诉详情弹窗 */}\n      <Modal\n        title=\"申诉记录\"\n        open={appealModalVisible}\n        onCancel={() => setAppealModalVisible(false)}\n        footer={null}\n        width={800}\n      >\n        {appealDetail && appealDetail.length > 0 && (\n          <Collapse\n            defaultActiveKey={['0']}  // 默认展开第一条记录\n            style={{ background: '#fff' }}\n          >\n            {appealDetail.map((appeal, index) => (\n              <Collapse.Panel\n                key={index}\n                header={\n                  <Space>\n                    <span>申诉记录 #{index + 1}</span>\n                    <Tag color={appeal.orderAppeal?.appealStatus === 0 ? 'warning' : \n                              appeal.orderAppeal?.appealStatus === 1 ? 'success' : 'error'}>\n                      {appeal.orderAppeal?.appealStatus === 0 ? '待处理' : \n                       appeal.orderAppeal?.appealStatus === 1 ? '已通过' : '已驳回'}\n                    </Tag>\n                    <span style={{ color: '#999' }}>{appeal.orderAppeal?.appealTime}</span>\n                  </Space>\n                }\n              >\n                {renderAppealContent(appeal)}\n              </Collapse.Panel>\n            ))}\n          </Collapse>\n        )}\n      </Modal>\n\n      {/* 添加取消订单的弹窗 */}\n      <Modal\n        title=\"取消订单\"\n        open={cancelModalVisible}\n        onCancel={() => {\n          setCancelModalVisible(false);\n          cancelForm.resetFields();\n        }}\n        footer={null}\n      >\n        <Form\n          form={cancelForm}\n          onFinish={confirmCancelOrder}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"cancelReason\"\n            label=\"取消原因\"\n            rules={[\n              { required: true, message: '请输入取消原因' },\n              { max: 200, message: '取消原因不能超过200个字符' }\n            ]}\n          >\n            <Input.TextArea\n              rows={4}\n              placeholder=\"请输入取消订单的原因\"\n              maxLength={200}\n              showCount\n            />\n          </Form.Item>\n          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n            <Space>\n              <Button\n                onClick={() => {\n                  setCancelModalVisible(false);\n                  cancelForm.resetFields();\n                }}\n              >\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                确认取消订单\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 添加派单弹窗 */}\n      <Modal\n        title=\"派单给跑腿用户\"\n        open={assignOrderModalVisible}\n        onCancel={() => {\n          setAssignOrderModalVisible(false);\n          setSelectedRunner(undefined);\n        }}\n        onOk={confirmAssignOrder}\n        confirmLoading={assignLoading}\n      >\n        <Form layout=\"vertical\">\n          <Form.Item label=\"选择跑腿用户\" required>\n            <Select\n              showSearch\n              placeholder=\"请选择跑腿用户\"\n              options={runnerUsers}\n              value={selectedRunner}\n              onChange={(value) => setSelectedRunner(value)}\n              optionFilterProp=\"label\"\n              filterOption={(input, option) =>\n                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())\n              }\n              style={{ width: '100%' }}\n            />\n          </Form.Item>\n          <div style={{ color: '#999', fontSize: '14px' }}>\n            派单后，所选跑腿用户将立即接单并开始处理订单。\n          </div>\n        </Form>\n      </Modal>\n\n      <ChatDrawer\n        visible={chatVisible}\n        onClose={() => setChatVisible(false)}\n        orderId={id}\n      />\n    </PageContainer>\n  );\n};\n\nexport default OrderDetail;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/order/detail/index.tsx"}, "18": {"name": "tag", "icon": "book", "path": "/tag", "perms": "order:tag:view", "access": "routeFilter", "file": "@/pages/tag/index.tsx", "parentId": "ant-design-pro-layout", "id": "18", "absPath": "/tag", "__content": "import { PageContainer } from '@ant-design/pro-layout';\nimport { useRef, useState, useEffect } from 'react';\nimport type { ActionType, ProColumns } from '@ant-design/pro-table';\nimport ProTable from '@ant-design/pro-table';\nimport { Button, Modal, Form, Input, message, Popconfirm, Space, Select, DatePicker } from 'antd';\nimport { PlusOutlined, SearchOutlined } from '@ant-design/icons';\nimport { list3 as listTags, add2 as addTag, edit2 as updateTag, remove4 as deleteTag } from '@/services/test-swagger/tagController';\nimport { list5 as listSchools } from '@/services/test-swagger/schoolController';\nimport { useModel } from '@umijs/max';\nimport dayjs from 'dayjs';\n\n/**\n * 标签金额功能说明\n * 1. 前端已添加金额字段(amount)到标签管理界面\n * 2. 后端需要做以下修改：\n *    - 在tag表中添加amount字段(decimal类型)\n *    - 在Tags实体类中添加amount属性\n *    - 更新相关的Service/Mapper接口\n *    - 修改订单创建和支付逻辑，使用标签对应的金额\n */\n\nconst { RangePicker } = DatePicker;\n\nconst TagList: React.FC = () => {\n  const actionRef = useRef<ActionType>();\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingTag, setEditingTag] = useState<API.Tags>();\n  const [selectedRows, setSelectedRows] = useState<API.Tags[]>([]);\n  const [form] = Form.useForm();\n\n  // 查询条件状态\n  const [searchText, setSearchText] = useState<string>('');\n  const [selectedSchool, setSelectedSchool] = useState<number>();\n  const [schoolOptions, setSchoolOptions] = useState<{ label: string; value: number; }[]>([]);\n  const [serviceType, setServiceType] = useState<number>();\n  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>();\n  const [updateTimeRange, setUpdateTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>();\n\n  // 获取当前用户信息\n  const { initialState } = useModel('@@initialState');\n  const currentUser = initialState?.currentUser;\n  // 使用类型断言避免TypeScript错误\n  const isAdmin = (currentUser as any)?.user?.userType === 0 || (currentUser as any)?.user?.userType === 1;\n\n  // 获取校区列表\n  const fetchSchoolList = async () => {\n    try {\n      const result = await listSchools({\n        pageQuery: {\n          pageSize: 999,\n          pageNum: 1,\n        },\n        school: {} as any,\n      });\n      if (result.code === 200) {\n        const options = (result.rows || []).map((item: any) => ({\n          label: item.name || '',\n          value: item.id || 0,\n        }));\n        setSchoolOptions(options);\n      }\n    } catch (error) {\n      message.error('获取校区列表失败');\n    }\n  };\n\n  useEffect(() => {\n      fetchSchoolList();\n  }, []);\n\n  // 打开新增/编辑弹窗\n  const handleModalOpen = (record?: API.Tags) => {\n    setEditingTag(record);\n    form.setFieldsValue(record || {});\n    setModalVisible(true);\n  };\n\n  // 关闭弹窗\n  const handleModalClose = () => {\n    setModalVisible(false);\n    setEditingTag(undefined);\n    form.resetFields();\n  };\n\n  // 提交表单\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingTag) {\n        const response = await updateTag({\n          ...values,\n          id: editingTag.id,\n        });\n        if (response.code === 200) {\n          message.success('更新成功');\n          handleModalClose();\n          actionRef.current?.reload();\n        } else {\n          message.error(response.msg || '更新失败');\n        }\n      } else {\n        const response = await addTag(values);\n        if (response.code === 200) {\n          message.success('添加成功');\n          handleModalClose();\n          actionRef.current?.reload();\n        } else {\n          message.error(response.msg || '添加失败');\n        }\n      }\n    } catch (error) {\n      console.error('表单错误:', error);\n    }\n  };\n\n  // 删除标签\n  const handleDelete = async (id: number | undefined) => {\n    if (!id) return;\n    try {\n      const response = await deleteTag({ id });\n      if (response.code === 200) {\n        message.success('删除成功');\n        actionRef.current?.reload();\n      } else {\n        message.error(response.msg || '删除失败');\n      }\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  // 批量删除\n  const handleBatchDelete = async () => {\n    Modal.confirm({\n      title: '批量删除',\n      content: `确定要删除选中的 ${selectedRows.length} 个标签吗？`,\n      onOk: async () => {\n        try {\n          const results = await Promise.all(\n            selectedRows.map((tag) => tag.id ? deleteTag({ id: tag.id }) : Promise.resolve({code: 0}))\n          );\n          const hasError = results.some((res) => res.code !== 200);\n          if (hasError) {\n            message.warning('部分标签删除失败');\n          } else {\n            message.success('删除成功');\n          }\n          setSelectedRows([]);\n          actionRef.current?.reload();\n          actionRef.current?.clearSelected?.();\n        } catch (error) {\n          message.error('删除失败');\n        }\n      },\n    });\n  };\n\n  // 重置查询条件\n  const handleReset = () => {\n    setSearchText('');\n    setSelectedSchool(undefined);\n    setServiceType(undefined);\n    setTimeRange(undefined);\n    setUpdateTimeRange(undefined);\n    actionRef.current?.reload();\n  };\n\n  // 添加搜索函数\n  const handleSearch = () => {\n    actionRef.current?.reload();\n  };\n\n  const columns: ProColumns<API.Tags>[] = [\n    {\n      title: '标签名称',\n      dataIndex: 'name',\n      ellipsis: true,\n      width: 120,\n    },\n    {\n      title: '所属校区',\n      dataIndex: 'schoolId',\n      hideInTable: true,\n      hideInSearch: false,\n      width: 120,\n      valueType: 'select',\n      ellipsis: true,\n      valueEnum: Object.fromEntries(\n        schoolOptions.map(({ label, value }) => [value, { text: label }])\n      ),\n    },\n    {\n      title: '服务类型',\n      dataIndex: 'serviceType',\n      width: 80,\n      valueType: 'select',\n      valueEnum: {\n        0: { text: '帮取送', status: 'Processing' },\n        1: { text: '代买', status: 'Success' },\n        2: { text: '万能服务', status: 'Warning' },\n      },\n    },\n    {\n      title: '金额(元)',\n      dataIndex: 'amount',\n      width: 80,\n      valueType: 'money',\n    },\n    {\n      title: '备注',\n      dataIndex: 'remark',\n      width: 150,\n      ellipsis: true,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      width: 150,\n      search: false,\n    },\n    {\n      title: '修改时间',\n      dataIndex: 'updateTime',\n      width: 150,\n      search: false,\n    },\n    {\n      title: '操作',\n      width: 180,\n      key: 'option',\n      valueType: 'option',\n      render: (_, record) => [\n        <a key=\"edit\" onClick={() => handleModalOpen(record)}>\n          编辑\n        </a>,\n        <Popconfirm\n          key=\"delete\"\n          title=\"确定要删除这个标签吗？\"\n          onConfirm={() => handleDelete(record.id)}\n        >\n          <a style={{ color: 'red' }}>删除</a>\n        </Popconfirm>,\n      ],\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <div style={{ marginBottom: 16, display: 'flex', gap: 16, flexWrap: 'wrap', alignItems: 'center' }}>\n        {isAdmin && (\n          <Select\n            allowClear\n            showSearch\n            style={{ width: 200 }}\n            placeholder=\"请选择校区\"\n            optionFilterProp=\"label\"\n            options={schoolOptions}\n            value={selectedSchool}\n            onChange={(value) => setSelectedSchool(value)}\n            filterOption={(input, option) =>\n              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())\n            }\n          />\n        )}\n        <Select\n          allowClear\n          style={{ width: 150 }}\n          placeholder=\"服务类型\"\n          value={serviceType}\n          onChange={(value) => setServiceType(value)}\n          options={[\n            { label: '帮取送', value: 0 },\n            { label: '代买', value: 1 },\n            { label: '万能服务', value: 2 },\n          ]}\n        />\n        <Input\n          placeholder=\"搜索标签名称\"\n          prefix={<SearchOutlined />}\n          style={{ width: 200 }}\n          value={searchText}\n          onChange={(e) => setSearchText(e.target.value)}\n        />\n        <RangePicker\n          showTime\n          style={{ width: 380 }}\n          value={timeRange}\n          placeholder={['创建开始时间', '创建结束时间']}\n          onChange={(dates) => setTimeRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}\n        />\n        <RangePicker\n          showTime\n          style={{ width: 380 }}\n          value={updateTimeRange}\n          placeholder={['修改开始时间', '修改结束时间']}\n          onChange={(dates) => setUpdateTimeRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}\n        />\n        <Button type=\"primary\" onClick={handleSearch}>\n          搜索\n        </Button>\n        <Button onClick={handleReset}>重置</Button>\n      </div>\n\n      <ProTable<API.Tags>\n        rowKey=\"id\"\n        actionRef={actionRef}\n        columns={columns}\n        search={false}\n        options={{\n          density: true,\n          fullScreen: true,\n          reload: true,\n          setting: true,\n        }}\n        rowSelection={{\n          onChange: (_, rows) => {\n            setSelectedRows(rows);\n          },\n        }}\n        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => (\n          <Space size={24}>\n            <span>\n              已选 {selectedRowKeys.length} 项\n              <a style={{ marginLeft: 8 }} onClick={onCleanSelected}>\n                取消选择\n              </a>\n            </span>\n          </Space>\n        )}\n        tableAlertOptionRender={() => (\n          <Space size={16}>\n            <Button danger onClick={handleBatchDelete}>\n              批量删除\n            </Button>\n          </Space>\n        )}\n        toolBarRender={() => [\n          <Button\n            key=\"add\"\n            type=\"primary\"\n            onClick={() => handleModalOpen()}\n            icon={<PlusOutlined />}\n          >\n            新增标签\n          </Button>,\n        ]}\n        request={async (params) => {\n          try {\n            const queryParams = {\n              pageQuery: {\n                pageSize: params.pageSize,\n                pageNum: params.current,\n              },\n              tags: {\n                name: searchText,\n                schoolId: selectedSchool,\n                serviceType,\n                remark: params.remark,\n              },\n            };\n\n            // 添加创建时间范围参数\n            if (timeRange?.[0]) {\n              (queryParams.tags as any)[`params['createTimeBegin']`] = timeRange[0].format('YYYY-MM-DD HH:mm:ss');\n              (queryParams.tags as any)[`params['createTimeEnd']`] = timeRange[1].format('YYYY-MM-DD HH:mm:ss');\n            }\n\n            // 添加修改时间范围参数\n            if (updateTimeRange?.[0]) {\n              (queryParams.tags as any)[`params['updateTimeBegin']`] = updateTimeRange[0].format('YYYY-MM-DD HH:mm:ss');\n              (queryParams.tags as any)[`params['updateTimeEnd']`] = updateTimeRange[1].format('YYYY-MM-DD HH:mm:ss');\n            }\n\n            // 移除所有空值\n            const cleanParams = JSON.parse(JSON.stringify(queryParams));\n\n            // 移除 tags 中的空值\n            Object.keys(cleanParams.tags).forEach(key => {\n              if (cleanParams.tags[key] === undefined) {\n                delete cleanParams.tags[key];\n              }\n            });\n\n            const response = await listTags(cleanParams);\n            if (response.code === 200) {\n              return {\n                data: response.rows || [],\n                success: true,\n                total: response.total || 0,\n              };\n            } else {\n              message.error(response.msg || '获取数据失败');\n              return {\n                data: [],\n                success: false,\n                total: 0,\n              };\n            }\n          } catch (error) {\n            message.error('获取数据失败');\n            return {\n              data: [],\n              success: false,\n              total: 0,\n            };\n          }\n        }}\n        pagination={{\n          defaultPageSize: 10,\n          showSizeChanger: true,\n        }}\n      />\n\n      <Modal\n        title={editingTag ? '编辑标签' : '新增标签'}\n        open={modalVisible}\n        onOk={handleSubmit}\n        onCancel={handleModalClose}\n        destroyOnClose\n      >\n        <Form form={form} layout=\"vertical\">\n          {/* 只有非校区管理员才显示校区选择 */}\n          {isAdmin && (\n            <Form.Item\n              name=\"schoolId\"\n              label=\"所属校区\"\n              rules={[{ required: true, message: '请选择校区' }]}\n            >\n              <Select\n                placeholder=\"请选择校区\"\n                options={schoolOptions}\n                showSearch\n                optionFilterProp=\"label\"\n              />\n            </Form.Item>\n          )}\n          <Form.Item\n            name=\"name\"\n            label=\"标签名称\"\n            rules={[{ required: true, message: '请输入标签名称' }]}\n          >\n            <Input placeholder=\"请输入标签名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"serviceType\"\n            label=\"服务类型\"\n            rules={[{ required: true, message: '请选择服务类型' }]}\n          >\n            <Select\n              placeholder=\"请选择服务类型\"\n              options={[\n                { label: '帮取送', value: 0 },\n                { label: '代买', value: 1 },\n                { label: '万能服务', value: 2 },\n              ]}\n            />\n          </Form.Item>\n          <Form.Item\n            name=\"amount\"\n            label=\"金额(元)\"\n            rules={[{ required: true, message: '请输入标签金额' }]}\n          >\n            <Input type=\"number\" min={0} step={0.01} placeholder=\"请输入标签金额\" />\n          </Form.Item>\n          <Form.Item name=\"remark\" label=\"备注\">\n            <Input.TextArea placeholder=\"请输入备注\" rows={4} />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default TagList;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/tag/index.tsx"}, "19": {"name": "workorder", "icon": "exception", "path": "/workorder", "parentId": "ant-design-pro-layout", "id": "19", "absPath": "/workorder"}, "20": {"path": "/workorder/runnerApply", "name": "runner-application", "perms": "runnerApply:view", "access": "routeFilter", "file": "@/pages/workorder/runnerApply/index.tsx", "parentId": "19", "id": "20", "absPath": "/workorder/runnerApply", "__content": "import { <PERSON><PERSON>ontainer } from '@ant-design/pro-layout';\nimport { useRef, useState, useEffect } from 'react';\nimport type { ActionType, ProColumns } from '@ant-design/pro-table';\nimport ProTable from '@ant-design/pro-table';\nimport { Button, Modal, Form, Input, message, Space, Select, Radio, Upload, Image, Descriptions, Tag, DatePicker } from 'antd';\nimport { PlusOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons';\nimport { list, edit, submit } from '@/services/test-swagger/runnerApplyController';\nimport { list5 as listSchools } from '@/services/test-swagger/schoolController';\nimport { useModel } from '@umijs/max';\nimport dayjs from 'dayjs';\n\nconst { RangePicker } = DatePicker;\n\nconst RunnerApplyList: React.FC = () => {\n  const actionRef = useRef<ActionType>();\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentRow, setCurrentRow] = useState<API.RunnerApply>();\n  const [form] = Form.useForm();\n  const { initialState } = useModel('@@initialState');\n\n  // 查询条件状态\n  const [searchText, setSearchText] = useState<string>('');\n  const [selectedSchool, setSelectedSchool] = useState<number>();\n  const [schoolOptions, setSchoolOptions] = useState<{ label: string; value: number; }[]>([]);\n  const [selectedStatus, setSelectedStatus] = useState<number>();\n\n  // 添加驳回状态\n  const [rejectModalVisible, setRejectModalVisible] = useState(false);\n  const [rejectForm] = Form.useForm();\n\n  // 添加时间范围状态\n  const [timeRange, setTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>();\n  const [updateTimeRange, setUpdateTimeRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>();\n\n  // 添加通过状态\n  const [approveModalVisible, setApproveModalVisible] = useState(false);\n  const [approveForm] = Form.useForm();\n\n  // 获取当前用户信息\n  const currentUser = initialState?.currentUser;\n  const isSchoolAgent = currentUser?.user?.userType === 1;\n  const agentSchoolName = currentUser?.user?.userPc?.agentSchool;\n\n  // 获取校区列表\n  const fetchSchoolList = async () => {\n    const res = await listSchools({\n      pageQuery: {\n        pageSize: 999,\n        pageNum: 1,\n      },\n      school: {}\n    });\n    const options = (res.rows || []).map((item) => ({\n      label: item.name,\n      value: item.id,\n    }));\n    setSchoolOptions(options);\n  };\n\n  useEffect(() => {\n    // 只有非校区代理才获取校区列表\n    if (!isSchoolAgent) {\n      fetchSchoolList();\n    }\n  }, [isSchoolAgent]);  // 添加依赖项\n\n  // 添加搜索函数\n  const handleSearch = () => {\n    actionRef.current?.reload();\n  };\n\n  const getStatusTag = (status?: number) => {\n    const statusMap = {\n      0: { color: 'error', text: '驳回' },\n      1: { color: 'success', text: '通过' },\n      2: { color: 'processing', text: '申请中' },\n    };\n    const { color, text } = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知' };\n    return <Tag color={color}>{text}</Tag>;\n  };\n\n  // 处理申请\n  const handleEdit = async (fields: API.RunnerApply) => {\n    const hide = message.loading('正在处理...');\n    try {\n      const res = await edit(fields);\n      if (res.code === 200) {\n        hide();\n        message.success('处理成功');\n        actionRef.current?.reload();\n        return true;\n      }\n      hide();\n      message.error(res.msg);\n      return false;\n    } catch (error) {\n      hide();\n      message.error('请求失败，请重试');\n      return false;\n    }\n  };\n\n  // 重置查询条件\n  const handleReset = () => {\n    setSearchText('');\n    setSelectedSchool(undefined);\n    setSelectedStatus(undefined);\n    setTimeRange(undefined);\n    setUpdateTimeRange(undefined);\n    actionRef.current?.reload();\n  };\n\n  // 处理驳回\n  const handleReject = async () => {\n    try {\n      const values = await rejectForm.validateFields();\n      const success = await handleEdit({\n        ...currentRow,\n        status: 0,\n        remarks: values.remarks,\n        updateId: initialState?.currentUser?.user?.uid,\n      });\n      if (success) {\n        setRejectModalVisible(false);\n        rejectForm.resetFields();\n      }\n    } catch (error) {\n      // 表单验证错误\n    }\n  };\n\n  // 处理通过\n  const handleApprove = async () => {\n    try {\n      const values = await approveForm.validateFields();\n      const success = await handleEdit({\n        ...currentRow,\n        status: 1,\n        remarks: values.remarks,\n        updateId: initialState?.currentUser?.user?.uid,\n      });\n      if (success) {\n        setApproveModalVisible(false);\n        approveForm.resetFields();\n      }\n    } catch (error) {\n      // 表单验证错误\n    }\n  };\n\n  const columns: ProColumns<API.RunnerApply>[] = [\n    {\n      title: '申请人',\n      dataIndex: 'uid',\n      ellipsis: true,\n    },\n    {\n      title: '真实姓名',\n      dataIndex: 'realname',\n      ellipsis: true,\n    },\n    {\n      title: '性别',\n      dataIndex: 'gender',\n      width: 80,\n      valueEnum: {\n        0: { text: '女' },\n        1: { text: '男' },\n      },\n    },\n    {\n      title: '所属校区',\n      dataIndex: 'schoolId',\n      width: 200,\n      valueType: 'select',\n      ellipsis: true,\n      valueEnum: Object.fromEntries(\n        schoolOptions.map(({ label, value }) => [value, { text: label }])\n      ),\n    },\n    {\n      title: '申请状态',\n      dataIndex: 'status',\n      width: 100,\n      render: (_, record) => getStatusTag(record.status),\n    },\n    {\n      title: '申请时间',\n      dataIndex: 'createTime',\n      width: 160,\n      search: false,\n    },\n    {\n      title: '操作',\n      width: 180,\n      key: 'option',\n      valueType: 'option',\n      render: (_, record) => [\n        <a\n          key=\"detail\"\n          onClick={() => {\n            setCurrentRow(record);\n            setDetailModalVisible(true);\n          }}\n        >\n          详情\n        </a>,\n        record.status === 2 && (\n          <Space key=\"actions\">\n            <a\n              onClick={() => {\n                setCurrentRow(record);\n                setApproveModalVisible(true);\n              }}\n            >\n              通过\n            </a>\n            <a\n              style={{ color: '#ff4d4f' }}\n              onClick={() => {\n                setCurrentRow(record);\n                setRejectModalVisible(true);\n              }}\n            >\n              驳回\n            </a>\n          </Space>\n        ),\n      ],\n    },\n  ];\n\n  return (\n    <PageContainer>\n      {isSchoolAgent && agentSchoolName && (\n        <div \n          style={{ \n            marginBottom: 16,\n            padding: '12px 24px',\n            background: '#f5f5f5',\n            borderRadius: '4px',\n            fontSize: '14px'\n          }}\n        >\n          当前校区：<span style={{ fontWeight: 'bold', color: '#1890ff' }}>{agentSchoolName}</span>\n        </div>\n      )}\n      <div style={{ marginBottom: 16, display: 'flex', gap: 16, flexWrap: 'wrap', alignItems: 'center' }}>\n        {/* 只有非校区管理员才显示校区选择 */}\n        {initialState?.currentUser?.user?.userType !== 1 && (\n          <Select\n            allowClear\n            showSearch\n            style={{ width: 200 }}\n            placeholder=\"请选择校区\"\n            optionFilterProp=\"label\"\n            options={schoolOptions}\n            value={selectedSchool}\n            onChange={(value) => setSelectedSchool(value)}\n            filterOption={(input, option) =>\n              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())\n            }\n          />\n        )}\n        <Select\n          allowClear\n          style={{ width: 150 }}\n          placeholder=\"申请状态\"\n          value={selectedStatus}\n          onChange={(value) => setSelectedStatus(value)}\n          options={[\n            { label: '驳回', value: 0 },\n            { label: '通过', value: 1 },\n            { label: '申请中', value: 2 },\n          ]}\n        />\n        <Input\n          placeholder=\"搜索姓名\"\n          prefix={<SearchOutlined />}\n          style={{ width: 200 }}\n          value={searchText}\n          onChange={(e) => setSearchText(e.target.value)}\n        />\n        <RangePicker\n          showTime\n          style={{ width: 380 }}\n          value={timeRange}\n          placeholder={['创建开始时间', '创建结束时间']}\n          onChange={(dates) => setTimeRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}\n        />\n        <RangePicker\n          showTime\n          style={{ width: 380 }}\n          value={updateTimeRange}\n          placeholder={['修改开始时间', '修改结束时间']}\n          onChange={(dates) => setUpdateTimeRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}\n        />\n        <Button type=\"primary\" onClick={handleSearch}>\n          搜索\n        </Button>\n        <Button onClick={handleReset}>重置</Button>\n      </div>\n\n      <ProTable<API.RunnerApply>\n        rowKey=\"id\"\n        actionRef={actionRef}\n        columns={columns}\n        search={false}\n        options={{\n          density: true,\n          fullScreen: true,\n          reload: true,\n          setting: true,\n        }}\n        request={async (params) => {\n          try {\n            const queryParams = {\n              pageQuery: {\n                pageSize: params.pageSize,\n                pageNum: params.current,\n              },\n              runnerApply: {\n                realname: searchText,\n                schoolId: selectedSchool,\n                status: selectedStatus,\n              },\n            };\n\n            // 添加创建时间范围参数\n            if (timeRange?.[0]) {\n              queryParams.runnerApply[`params['createTimeBegin']`] = timeRange[0].format('YYYY-MM-DD HH:mm:ss');\n              queryParams.runnerApply[`params['createTimeEnd']`] = timeRange[1].format('YYYY-MM-DD HH:mm:ss');\n            }\n\n            // 添加修改时间范围参数\n            if (updateTimeRange?.[0]) {\n              queryParams.runnerApply[`params['updateTimeBegin']`] = updateTimeRange[0].format('YYYY-MM-DD HH:mm:ss');\n              queryParams.runnerApply[`params['updateTimeEnd']`] = updateTimeRange[1].format('YYYY-MM-DD HH:mm:ss');\n            }\n\n            // 移除所有空值\n            const cleanParams = JSON.parse(JSON.stringify(queryParams));\n\n            // 移除 runnerApply 中的空值\n            Object.keys(cleanParams.runnerApply).forEach(key => {\n              if (cleanParams.runnerApply[key] === undefined) {\n                delete cleanParams.runnerApply[key];\n              }\n            });\n\n            const response = await list(cleanParams);\n            if (response.code === 200) {\n              return {\n                data: response.rows || [],\n                success: true,\n                total: response.total || 0,\n              };\n            }\n            message.error(response.msg);\n            return {\n              data: [],\n              success: false,\n              total: 0,\n            };\n          } catch (error) {\n            message.error('请求失败，请重试');\n            return {\n              data: [],\n              success: false,\n              total: 0,\n            };\n          }\n        }}\n        pagination={{\n          defaultPageSize: 10,\n          showSizeChanger: true,\n        }}\n      />\n\n      <Modal\n        title=\"驳回申请\"\n        open={rejectModalVisible}\n        onOk={handleReject}\n        onCancel={() => {\n          setRejectModalVisible(false);\n          rejectForm.resetFields();\n        }}\n        destroyOnClose\n      >\n        <Form form={rejectForm}>\n          <Form.Item\n            name=\"remarks\"\n            label=\"驳回原因\"\n            rules={[\n              { required: true, message: '请输入驳回原因' },\n              { max: 100, message: '驳回原因不能超过100字' },\n            ]}\n          >\n            <Input.TextArea\n              rows={4}\n              placeholder=\"请输入驳回原因（不超过100字）\"\n              maxLength={100}\n              showCount\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      <Modal\n        title=\"通过申请\"\n        open={approveModalVisible}\n        onOk={handleApprove}\n        onCancel={() => {\n          setApproveModalVisible(false);\n          approveForm.resetFields();\n        }}\n        destroyOnClose\n      >\n        <Form form={approveForm}>\n          <Form.Item\n            name=\"remarks\"\n            label=\"审核备注\"\n            rules={[\n              { required: true, message: '请输入审核备注' },\n              { max: 100, message: '审核备注不能超过100字' },\n            ]}\n          >\n            <Input.TextArea\n              rows={4}\n              placeholder=\"请输入审核备注（不超过100字）\"\n              maxLength={100}\n              showCount\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      <Modal\n        title=\"申请详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        {currentRow && (\n          <Descriptions bordered column={1}>\n            <Descriptions.Item label=\"申请ID\">{currentRow.id}</Descriptions.Item>\n            <Descriptions.Item label=\"申请人\">{currentRow.realname}</Descriptions.Item>\n            <Descriptions.Item label=\"性别\">\n              {currentRow.gender === 1 ? '男' : '女'}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"学校\">{currentRow.schoolName}</Descriptions.Item>\n            <Descriptions.Item label=\"申请状态\">\n              {getStatusTag(currentRow.status)}\n            </Descriptions.Item>\n            {currentRow.status === 0 && (\n              <Descriptions.Item label=\"驳回原因\" style={{ whiteSpace: 'pre-wrap' }}>\n                {currentRow.remarks || '无'}\n              </Descriptions.Item>\n            )}\n            <Descriptions.Item label=\"学生证\">\n              <Image\n                width={200}\n                src={currentRow.studentCardUrl}\n                alt=\"学生证照片\"\n              />\n            </Descriptions.Item>\n            <Descriptions.Item label=\"申请时间\">{currentRow.createTime}</Descriptions.Item>\n            {currentRow.updateId && (\n              <Descriptions.Item label=\"处理人ID\">{currentRow.updateId}</Descriptions.Item>\n            )}\n            {currentRow.updateTime && (\n              <Descriptions.Item label=\"处理时间\">{currentRow.updateTime}</Descriptions.Item>\n            )}\n          </Descriptions>\n        )}\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default RunnerApplyList;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/workorder/runnerApply/index.tsx"}, "21": {"path": "/workorder/orderAppeal", "name": "order-appeal", "perms": "order:appeal:view", "access": "routeFilter", "file": "@/pages/workorder/orderAppeal/index.tsx", "parentId": "19", "id": "21", "absPath": "/workorder/orderAppeal", "__content": "import { PageContainer } from '@ant-design/pro-layout';\nimport { useRef, useState, useEffect } from 'react';\nimport type { ActionType, ProColumns } from '@ant-design/pro-table';\nimport ProTable from '@ant-design/pro-table';\nimport { Button, Modal, Form, Input, message, Space, Select, Tag, Descriptions, Image, InputNumber } from 'antd';\nimport { SearchOutlined } from '@ant-design/icons';\nimport { edit3 as editAppeal, getAppeal, confirmComplete } from '@/services/test-swagger/orderAppealController';\nimport { useModel, useAccess, Access } from '@umijs/max';\nimport { list5 as listSchools } from '@/services/test-swagger/schoolController';\nimport { list5 } from '@/services/test-swagger/orderAppealController';\n\nconst OrderAppealList: React.FC = () => {\n  const actionRef = useRef<ActionType>();\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentRow, setCurrentRow] = useState<API.OrderAppealVO>();\n  const { initialState } = useModel('@@initialState');\n  const [rejectModalVisible, setRejectModalVisible] = useState(false);\n  const [rejectForm] = Form.useForm();\n  const [approveModalVisible, setApproveModalVisible] = useState(false);\n  const [approveForm] = Form.useForm();\n  const access = useAccess();\n\n  // 查询条件状态\n  const [searchOrderId, setSearchOrderId] = useState<string>('');\n  const [selectedStatus, setSelectedStatus] = useState<number>();\n  const [selectedSchool, setSelectedSchool] = useState<number>();\n  const [selectedAppealTarget, setSelectedAppealTarget] = useState<number>();\n  const [schoolOptions, setSchoolOptions] = useState<{ label: string; value: number; }[]>([]);\n\n  // 获取校区列表\n  const fetchSchoolList = async () => {\n    try {\n      const res = await listSchools({\n        pageQuery: {\n          pageSize: 999,\n          pageNum: 1,\n        },\n        school: {}\n      });\n      if (res.code === 200) {\n        const options = (res.rows || []).map((item) => ({\n          label: item.name,\n          value: item.id,\n        }));\n        setSchoolOptions(options);\n      } else {\n        message.error(res.msg);\n      }\n    } catch (error) {\n      message.error('获取校区列表失败，请重试');\n    }\n  };\n\n  useEffect(() => {\n    fetchSchoolList();\n  }, []);\n\n  // 每当查询条件改变时触发查询\n  useEffect(() => {\n    actionRef.current?.reload();\n  }, [searchOrderId, selectedStatus, selectedSchool, selectedAppealTarget]);\n\n  const getStatusTag = (status?: number) => {\n    const statusMap = {\n      0: { color: 'error', text: '不通过' },\n      1: { color: 'success', text: '通过' },\n      2: { color: 'processing', text: '申诉中' },\n    };\n    const { color, text } = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知' };\n    return <Tag color={color}>{text}</Tag>;\n  };\n\n  const getAppealTargetTag = (target?: number) => {\n    const targetMap = {\n      0: { color: 'blue', text: '用户退款' },\n      1: { color: 'orange', text: '骑手补差价' },\n    };\n    const { color, text } = targetMap[target as keyof typeof targetMap] || { color: 'default', text: '未知' };\n    return <Tag color={color}>{text}</Tag>;\n  };\n\n  // 处理申诉\n  const handleEdit = async (fields: API.OrderAppeal) => {\n    const hide = message.loading('正在处理...');\n    try {\n      const res = await editAppeal({\n        ...fields,\n        updateId: initialState?.currentUser?.user?.uid,\n        updateType: 0,\n      });\n      if (res.code === 200) {\n        hide();\n        message.success('处理成功!');\n        setApproveModalVisible(false);\n        approveForm.resetFields();\n        actionRef.current?.reload();\n        return true;\n      }\n      hide();\n      message.error(res.msg);\n      return false;\n    } catch (error) {\n      hide();\n      message.error('请求失败，请重试!');\n      return false;\n    }\n  };\n\n  // 处理通过\n  const handleApprove = async () => {\n    try {\n      const values = await approveForm.validateFields();\n      const success = await handleEdit({\n        ...currentRow?.orderAppeal,\n        appealStatus: 1,\n        refundAmount: values.refundAmount,\n        updateId: initialState?.currentUser?.user?.uid,\n      });\n\n      if (success) {\n        setApproveModalVisible(false);\n        approveForm.resetFields();\n      }\n    } catch (error) {\n      // 表单验证错误\n    }\n  };\n\n  // 处理驳回\n  const handleReject = async () => {\n    try {\n      const values = await rejectForm.validateFields();\n      const success = await handleEdit({\n        ...currentRow?.orderAppeal,\n        appealStatus: 0,\n        remarks: values.remarks,\n        updateId: initialState?.currentUser?.user?.uid,\n      });\n      if (success) {\n        setRejectModalVisible(false);\n        rejectForm.resetFields();\n      }\n    } catch (error) {\n      // 表单验证错误\n    }\n  };\n\n  // 获取详情数据\n  const handleViewDetail = async (record: API.OrderAppeal) => {\n    if (!record?.orderId) {\n      message.error('订单ID不存在');\n      return;\n    }\n    try {\n      const response = await getAppeal({ \n        orderId: record.orderId.toString()\n      });\n      if (response.code === 200) {\n        if (response.data?.[0]) {\n          setCurrentRow(response.data[0]);\n          setDetailModalVisible(true);\n        } else {\n          message.error('未找到申诉记录');\n        }\n      } else {\n        message.error(response.msg);\n      }\n    } catch (error) {\n      message.error('请求失败，请重试');\n    }\n  };\n\n  // 添加确认完成函数\n  const handleConfirmComplete = async (orderId?: string) => {\n    if (!orderId) {\n      message.error('订单ID不存在');\n      return;\n    }\n    \n    Modal.confirm({\n      title: '确认完成订单',\n      content: '确定要将此订单状态从\"已申诉\"改为\"已完成\"吗？确认后，补差价订单将被标记为完成状态。',\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        try {\n          const res = await confirmComplete({ orderId });\n          if (res.code === 200) {\n            message.success('订单已确认完成');\n            actionRef.current?.reload();\n          } else {\n            message.error(res.msg || '操作失败');\n          }\n        } catch (error) {\n          message.error('操作失败，请重试');\n        }\n      }\n    });\n  };\n\n  const columns: ProColumns<API.OrderAppeal>[] = [\n    {\n      title: '所属校区',\n      dataIndex: 'schoolId',\n      valueType: 'select',\n      valueEnum: Object.fromEntries(\n        schoolOptions.map(({ label, value }) => [value, { text: label }])\n      ),\n    },\n    {\n      title: '订单ID',\n      dataIndex: 'orderId',\n      ellipsis: true,\n    },\n    {\n      title: '申诉理由',\n      dataIndex: 'appealReason',\n      ellipsis: true,\n      width: 200,\n    },\n    {\n      title: '申诉对象',\n      dataIndex: 'appealTarget',\n      width: 120,\n      render: (_, record) => getAppealTargetTag(record?.appealTarget),\n    },\n    {\n      title: '申诉状态',\n      dataIndex: 'appealStatus',\n      width: 100,\n      render: (_, record) => getStatusTag(record?.appealStatus),\n    },\n    {\n      title: '金额',\n      dataIndex: 'refundAmount',\n      width: 100,\n      render: (_, record) => record.refundAmount ? `¥${record.refundAmount}` : '-',\n    },\n    {\n      title: '申诉时间',\n      dataIndex: 'appealTime',\n      width: 150,\n      valueType: 'dateTime',\n    },\n    {\n      title: '操作',\n      width: 220,\n      key: 'option',\n      valueType: 'option',\n      render: (_, record) => [\n        <a\n          key=\"detail\"\n          onClick={() => record?.orderId && handleViewDetail(record)}\n        >\n          详情\n        </a>,\n        record?.appealStatus === 2 && (\n          <Space key=\"actions\">\n            <Access \n              accessible={!!access.hasPerms('system:appeal:edit')}\n            >\n              <a\n                onClick={() => {\n                  setCurrentRow({ orderAppeal: record });\n                  setApproveModalVisible(true);\n                  // 设置默认退款金额\n                  setTimeout(() => {\n                    approveForm.setFieldsValue({ \n                      refundAmount: record.refundAmount || 0\n                    });\n                  }, 100);\n                }}\n              >\n                通过\n              </a>\n            </Access>\n            <Access \n              accessible={!!access.hasPerms('system:appeal:handle')}\n            >\n              <a\n                style={{ color: '#ff4d4f' }}\n                onClick={() => {\n                  setCurrentRow({ orderAppeal: record });\n                  setRejectModalVisible(true);\n                }}\n              >\n                驳回\n              </a>\n            </Access>\n          </Space>\n        ),\n        // 添加确认完成按钮，只对已通过的骑手补差价申诉显示\n        record?.appealStatus === 1 && record?.appealTarget === 1 && (\n          <Access\n            key=\"confirm\"\n            accessible={!!access.hasPerms('system:appeal:confirm')}\n          >\n            <a\n              onClick={() => {\n                record?.orderId && handleConfirmComplete(record.orderId);\n              }}\n            >\n              确认完成\n            </a>\n          </Access>\n        ),\n      ],\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <div style={{ marginBottom: 16, display: 'flex', gap: 16, flexWrap: 'wrap', alignItems: 'center' }}>\n        <Select\n          allowClear\n          showSearch\n          style={{ width: 200 }}\n          placeholder=\"请选择校区\"\n          optionFilterProp=\"label\"\n          options={schoolOptions}\n          value={selectedSchool}\n          onChange={(value) => setSelectedSchool(value)}\n          filterOption={(input, option) =>\n            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())\n          }\n        />\n        <Input\n          placeholder=\"搜索订单ID\"\n          prefix={<SearchOutlined />}\n          style={{ width: 200 }}\n          value={searchOrderId}\n          onChange={(e) => setSearchOrderId(e.target.value)}\n          onPressEnter={() => actionRef.current?.reload()}\n        />\n        <Select\n          allowClear\n          style={{ width: 150 }}\n          placeholder=\"申诉状态\"\n          value={selectedStatus}\n          onChange={(value) => setSelectedStatus(value)}\n          options={[\n            { label: '不通过', value: 0 },\n            { label: '通过', value: 1 },\n            { label: '申诉中', value: 2 },\n          ]}\n        />\n        <Select\n          allowClear\n          style={{ width: 150 }}\n          placeholder=\"申诉对象\"\n          value={selectedAppealTarget}\n          onChange={(value) => setSelectedAppealTarget(value)}\n          options={[\n            { label: '用户退款', value: 0 },\n            { label: '骑手补差价', value: 1 },\n          ]}\n        />\n        <Button onClick={() => {\n          setSearchOrderId('');\n          setSelectedStatus(undefined);\n          setSelectedSchool(undefined);\n          setSelectedAppealTarget(undefined);\n          actionRef.current?.reload();\n        }}>\n          重置\n        </Button>\n      </div>\n\n      <ProTable<API.OrderAppeal>\n        rowKey=\"id\"\n        actionRef={actionRef}\n        columns={columns}\n        search={false}\n        options={{\n          density: true,\n          fullScreen: true,\n          reload: true,\n          setting: true,\n        }}\n        toolBarRender={() => [\n          <Access \n            key=\"add\" \n            accessible={!!access.hasPerms('system:appeal:add')}\n          >\n            <Button>\n              新增\n            </Button>\n          </Access>\n        ]}\n        request={async (params) => {\n          try {\n            const response = await list5({\n              pageQuery: {\n                pageSize: params.pageSize,\n                pageNum: params.current,\n              },\n              orderAppeal: {\n                orderId: searchOrderId ? searchOrderId : undefined,\n                schoolId: selectedSchool,\n                appealStatus: selectedStatus,\n                appealTarget: selectedAppealTarget,\n              },\n            });\n            if (response.code === 200) {\n              return {\n                data: response.rows || [],\n                success: true,\n                total: response.total || 0,\n              };\n            }\n            message.error(response.msg);\n            return {\n              data: [],\n              success: false,\n              total: 0,\n            };\n          } catch (error) {\n            message.error('请求失败，请重试');\n            return {\n              data: [],\n              success: false,\n              total: 0,\n            };\n          }\n        }}\n        pagination={{\n          defaultPageSize: 10,\n          showSizeChanger: true,\n        }}\n      />\n\n      <Modal\n        title=\"驳回申诉\"\n        open={rejectModalVisible}\n        onOk={handleReject}\n        onCancel={() => {\n          setRejectModalVisible(false);\n          rejectForm.resetFields();\n        }}\n        destroyOnClose\n      >\n        <Form form={rejectForm}>\n          <Form.Item\n            name=\"remarks\"\n            label=\"驳回原因\"\n            rules={[\n              { required: true, message: '请输入驳回原因' },\n              { max: 100, message: '驳回原因不能超过100字' },\n            ]}\n          >\n            <Input.TextArea\n              rows={4}\n              placeholder=\"请输入驳回原因（不超过100字）\"\n              maxLength={100}\n              showCount\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      <Modal\n        title=\"通过申诉\"\n        open={approveModalVisible}\n        onOk={handleApprove}\n        onCancel={() => {\n          setApproveModalVisible(false);\n          approveForm.resetFields();\n        }}\n        destroyOnClose\n      >\n        <Form form={approveForm} layout=\"vertical\">\n          <Form.Item\n            name=\"refundAmount\"\n            label={currentRow?.orderAppeal?.appealTarget === 1 ? \"补差价金额\" : \"退款金额\"}\n            rules={[\n              { required: true, message: `请输入${currentRow?.orderAppeal?.appealTarget === 1 ? \"补差价\" : \"退款\"}金额` },\n              { type: 'number', min: 0.01, message: '金额必须大于0' },\n            ]}\n          >\n            <InputNumber\n              placeholder={`请输入${currentRow?.orderAppeal?.appealTarget === 1 ? \"补差价\" : \"退款\"}金额`}\n              min={0.01}\n              precision={2}\n              step={1}\n              addonBefore=\"¥\"\n              style={{ width: '100%' }}\n            />\n          </Form.Item>\n          <p style={{ color: '#999' }}>\n            {currentRow?.orderAppeal?.appealTarget === 1 \n              ? \"补差价金额将会转入骑手余额中\" \n              : \"如果设置的金额等于订单金额，将执行全额退款\"}\n          </p>\n        </Form>\n      </Modal>\n\n      <Modal\n        title=\"申诉详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={null}\n        width={800}\n      >\n        {currentRow?.orderAppeal && (\n          <Descriptions bordered column={1}>\n            <Descriptions.Item label=\"订单ID\">{currentRow.orderAppeal.orderId}</Descriptions.Item>\n            <Descriptions.Item label=\"申诉理由\">{currentRow.orderAppeal.appealReason}</Descriptions.Item>\n            <Descriptions.Item label=\"申诉对象\">\n              {getAppealTargetTag(currentRow.orderAppeal.appealTarget)}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"申诉状态\">\n              {getStatusTag(currentRow.orderAppeal.appealStatus)}\n            </Descriptions.Item>\n            {currentRow.orderAppeal.refundAmount && (\n              <Descriptions.Item label={currentRow.orderAppeal.appealTarget === 1 ? \"补差价金额\" : \"退款金额\"}>\n                ¥{currentRow.orderAppeal.refundAmount}\n              </Descriptions.Item>\n            )}\n            {currentRow.orderAppeal.appealStatus === 0 && (\n              <Descriptions.Item label=\"驳回原因\" style={{ whiteSpace: 'pre-wrap' }}>\n                {currentRow.orderAppeal.remarks || '无'}\n              </Descriptions.Item>\n            )}\n            <Descriptions.Item label=\"申诉时间\">{currentRow.orderAppeal.appealTime}</Descriptions.Item>\n            {currentRow.orderAppeal.updateId && (\n              <Descriptions.Item label=\"处理人ID\">{currentRow.orderAppeal.updateId}</Descriptions.Item>\n            )}\n            {currentRow.orderAppeal.updateTime && (\n              <Descriptions.Item label=\"处理时间\">{currentRow.orderAppeal.updateTime}</Descriptions.Item>\n            )}\n            {currentRow.orderAppeal.remarks && (\n              <Descriptions.Item label=\"备注\" style={{ whiteSpace: 'pre-wrap' }}>\n                {currentRow.orderAppeal.remarks}\n              </Descriptions.Item>\n            )}\n            {currentRow.imageUrls?.length > 0 && (\n              <Descriptions.Item label=\"申诉图片\">\n                <Image.PreviewGroup>\n                  {currentRow.imageUrls.map((url, index) => (\n                    <Image\n                      key={index}\n                      width={200}\n                      src={url}\n                      style={{ marginRight: 8, marginBottom: 8 }}\n                    />\n                  ))}\n                </Image.PreviewGroup>\n              </Descriptions.Item>\n            )}\n            {/* 添加确认完成按钮，只对已通过的骑手补差价申诉显示 */}\n            {currentRow.orderAppeal.appealStatus === 1 && \n             currentRow.orderAppeal.appealTarget === 1 &&\n             currentRow.orderAppeal.remarks?.includes('补差价已支付') && (\n              <Descriptions.Item label=\" \">\n                <Access accessible={!!access.hasPerms('system:appeal:confirm')}>\n                  <Button \n                    type=\"primary\" \n                    onClick={() => {\n                      currentRow.orderAppeal?.orderId && \n                      handleConfirmComplete(currentRow.orderAppeal.orderId);\n                      setDetailModalVisible(false);\n                    }}\n                  >\n                    确认订单完成\n                  </Button>\n                </Access>\n              </Descriptions.Item>\n            )}\n          </Descriptions>\n        )}\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default OrderAppealList;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/workorder/orderAppeal/index.tsx"}, "22": {"name": "capital", "icon": "propertySafety", "path": "/capital", "parentId": "ant-design-pro-layout", "id": "22", "absPath": "/capital"}, "23": {"path": "/capital/wallet", "name": "wallet", "perms": "payment:withdraw:view", "access": "routeFilter", "file": "@/pages/capital/wallet/index.tsx", "parentId": "22", "id": "23", "absPath": "/capital/wallet", "__content": "import { Button, Modal, Tag, Descriptions } from 'antd';\nimport React, { useState, useRef } from 'react';\nimport { PageContainer } from '@ant-design/pro-layout';\nimport type { ProColumns, ActionType } from '@ant-design/pro-table';\nimport ProTable from '@ant-design/pro-table';\nimport { walletPage as listWallets } from '@/services/test-swagger/moneyController';\nimport type { SortOrder } from 'antd/lib/table/interface';\n\nconst WalletList: React.FC = () => {\n  const [currentRow, setCurrentRow] = useState<API.Wallet>();\n  const actionRef = useRef<ActionType>();\n  const [detailVisible, setDetailVisible] = useState<boolean>(false);\n\n  const handleSorter = (sorter: Record<string, SortOrder>) => {\n    const key = Object.keys(sorter)[0];\n    const order = sorter[key];\n    if (key && order) {\n      const column = key.replace(/([A-Z])/g, '_$1').toLowerCase();\n      return {\n        orderByColumn: column,\n        isAsc: order === 'ascend' ? 'asc' : 'desc',\n      };\n    }\n    return {};\n  };\n\n  const columns: ProColumns<API.Wallet>[] = [\n    {\n      title: '用户ID',\n      dataIndex: 'uid',\n      sorter: true,\n      width: 100,\n      align: 'center',\n    },\n    {\n      title: '当前余额',\n      dataIndex: 'balance',\n      hideInSearch: true,\n      render: (val) => (\n        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>\n          ¥{Number(val || 0).toFixed(2)}\n        </span>\n      ),\n      sorter: true,\n      width: 120,\n      align: 'right',\n    },\n    {\n      title: '已提现',\n      dataIndex: 'withdrawn',\n      hideInSearch: true,\n      render: (val) => (\n        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>\n          ¥{Number(val || 0).toFixed(2)}\n        </span>\n      ),\n      sorter: true,\n      width: 120,\n      align: 'right',\n    },\n    {\n      title: '总收入',\n      hideInSearch: true,\n      render: (_, record) => (\n        <span style={{ color: '#f5222d', fontWeight: 'bold' }}>\n          ¥{(Number(record.balance) + Number(record.withdrawn)).toFixed(2)}\n        </span>\n      ),\n      width: 120,\n      align: 'right',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      valueType: 'dateTimeRange',\n      hideInTable: true,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      hideInSearch: true,\n      sorter: true,\n      width: 180,\n      align: 'center',\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updateTime',\n      hideInSearch: true,\n      sorter: true,\n      width: 180,\n      align: 'center',\n    },\n    {\n      title: '操作',\n      dataIndex: 'option',\n      valueType: 'option',\n      width: 100,\n      align: 'center',\n      render: (_, record) => [\n        <Button\n          key=\"detail\"\n          type=\"link\"\n          onClick={() => {\n            setCurrentRow(record);\n            setDetailVisible(true);\n          }}\n        >\n          详情\n        </Button>,\n      ],\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <ProTable<API.Wallet>\n        headerTitle=\"钱包管理\"\n        actionRef={actionRef}\n        rowKey=\"uid\"\n        search={{\n          labelWidth: 120,\n          defaultCollapsed: false,\n        }}\n        request={async (params, sorter) => {\n          const { current, pageSize, createTime, ...rest } = params;\n          \n          let sortParams = {};\n          if (sorter) {\n            const key = Object.keys(sorter)[0];\n            const order = sorter[key];\n            if (key && order) {\n              const column = key.replace(/([A-Z])/g, '_$1').toLowerCase();\n              sortParams = {\n                orderByColumn: column,\n                isAsc: order === 'ascend' ? 'asc' : 'desc'\n              };\n            }\n          }\n\n          const response = await listWallets({\n            wallet: {\n              ...rest,\n              createTime: createTime?.[0],\n              updateTime: createTime?.[1],\n            },\n            pageQuery: {\n              pageSize,\n              pageNum: current,\n              ...sortParams,\n            },\n          });\n          return {\n            data: response.rows || [],\n            success: response.code === 200,\n            total: response.total || 0,\n          };\n        }}\n        columns={columns}\n        toolBarRender={false}\n        pagination={{\n          showQuickJumper: true,\n          showSizeChanger: true,\n          showTotal: (total) => `共 ${total} 条记录`,\n          defaultPageSize: 10,\n          pageSizeOptions: ['10', '20', '50', '100'],\n        }}\n        bordered\n      />\n\n      <Modal\n        title=\"钱包详情\"\n        open={detailVisible}\n        onCancel={() => setDetailVisible(false)}\n        footer={null}\n        width={800}\n      >\n        <Descriptions column={2}>\n          <Descriptions.Item label=\"用户ID\">{currentRow?.uid}</Descriptions.Item>\n          <Descriptions.Item label=\"当前余额\">\n            ¥{currentRow?.balance || 0}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"已提现\">\n            ¥{currentRow?.withdrawn || 0}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"总收入\">\n            ¥{(Number(currentRow?.balance) + Number(currentRow?.withdrawn)) || 0}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {currentRow?.createTime}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"更新时间\">\n            {currentRow?.updateTime}\n          </Descriptions.Item>\n        </Descriptions>\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default WalletList;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/capital/wallet/index.tsx"}, "24": {"path": "/capital/withdrawal", "name": "withdrawal", "perms": "payment:recode:view", "access": "routeFilter", "file": "@/pages/capital/withdrawal/index.tsx", "parentId": "22", "id": "24", "absPath": "/capital/withdrawal", "__content": "import { Button, message, Modal, Tag, Space, Descriptions } from 'antd';\nimport React, { useState, useRef } from 'react';\nimport { PageContainer } from '@ant-design/pro-layout';\nimport type { ProColumns, ActionType } from '@ant-design/pro-table';\nimport ProTable from '@ant-design/pro-table';\nimport {\n  ModalForm,\n  ProFormSelect,\n  ProFormTextArea,\n} from '@ant-design/pro-form';\nimport { withdrawPage as listMoneyRecords, edit2 as audit } from '@/services/test-swagger/moneyController';\n\nconst WithdrawalList: React.FC = () => {\n  const [currentRow, setCurrentRow] = useState<API.MoneyRecode>();\n  const actionRef = useRef<ActionType>();\n  const [detailVisible, setDetailVisible] = useState<boolean>(false);\n\n  // 获取状态标签\n  const getStatusTag = (status?: number) => {\n    switch (status) {\n      case 0:\n        return <Tag color=\"red\">驳回</Tag>;\n      case 1:\n        return <Tag color=\"green\">通过</Tag>;\n      case 2:\n        return <Tag color=\"processing\">审核中</Tag>;\n      default:\n        return <Tag color=\"default\">未知</Tag>;\n    }\n  };\n\n  // 获取用户类型标签\n  const getUserTypeTag = (type?: number) => {\n    switch (type) {\n      case 1:\n        return <Tag color=\"orange\">校区代理</Tag>;\n      case 4:\n        return <Tag color=\"blue\">跑腿用户</Tag>;\n      default:\n        return <Tag color=\"default\">未知</Tag>;\n    }\n  };\n\n  // 审核处理\n  const handleAudit = async (id: number, status: number, feedback: string) => {\n    try {\n      const res = await audit({\n        id,\n        status,\n        feedback\n      });\n      if (res.code === 200) {\n        message.success('审核成功');\n        actionRef.current?.reload();\n      } else {\n        message.error(res.msg);\n      }\n    } catch (error) {\n      message.error('审核失败');\n    }\n  };\n\n  const columns: ProColumns<API.MoneyRecode>[] = [\n    {\n      title: '提现ID',\n      dataIndex: 'id',\n      hideInSearch: true,\n    },\n    {\n      title: '用户ID',\n      dataIndex: 'uid',\n    },\n    {\n      title: '用户类型',\n      dataIndex: 'type',\n      valueType: 'select',\n      valueEnum: {\n        1: { text: '校区代理', status: 'Warning' },\n        4: { text: '跑腿用户', status: 'Processing' },\n      },\n    },\n    {\n      title: '提现金额',\n      dataIndex: 'cash',\n      hideInSearch: true,\n      render: (val) => `¥${val}`,\n    },\n    {\n      title: '提现平台',\n      dataIndex: 'platform',\n      valueType: 'select',\n      valueEnum: {\n        0: { text: '支付宝转账' },\n        1: { text: '银行卡转账' },\n      },\n    },\n    {\n      title: '卡号/账号',\n      dataIndex: 'card',\n      hideInSearch: true,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      valueType: 'select',\n      valueEnum: {\n        0: { text: '驳回', status: 'Error' },\n        1: { text: '通过', status: 'Success' },\n        2: { text: '审核中', status: 'Processing' },\n      },\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      valueType: 'dateTimeRange',\n      hideInTable: true,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      hideInSearch: true,\n    },\n    {\n      title: '操作',\n      dataIndex: 'option',\n      valueType: 'option',\n      render: (_, record) => [\n        <Button\n          key=\"detail\"\n          type=\"link\"\n          onClick={() => {\n            setCurrentRow(record);\n            setDetailVisible(true);\n          }}\n        >\n          详情\n        </Button>,\n        record.status === 2 && (\n          <ModalForm\n            key=\"audit\"\n            title=\"提现审核\"\n            trigger={<Button type=\"link\">审核</Button>}\n            onFinish={async (values) => {\n              await handleAudit(record.id!, values.status, values.feedback);\n              return true;\n            }}\n          >\n            <ProFormSelect\n              name=\"status\"\n              label=\"审核结果\"\n              rules={[{ required: true }]}\n              options={[\n                { label: '通过', value: 1 },\n                { label: '驳回', value: 0 },\n              ]}\n            />\n            <ProFormTextArea\n              name=\"feedback\"\n              label=\"审核反馈\"\n              rules={[{ required: true }]}\n              placeholder=\"请输入审核反馈\"\n            />\n          </ModalForm>\n        ),\n      ],\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <ProTable<API.MoneyRecode>\n        headerTitle=\"提现记录\"\n        actionRef={actionRef}\n        rowKey=\"id\"\n        search={{\n          labelWidth: 120,\n          defaultCollapsed: false,\n        }}\n        request={async (params) => {\n          const { current, pageSize, createTime, ...rest } = params;\n          const response = await listMoneyRecords({\n            pageQuery: {\n              pageSize,\n              pageNum: current,\n            },\n            moneyRecode: {\n              ...rest,\n              createTime: createTime?.[0],\n              updateTime: createTime?.[1],\n            },\n          });\n          return {\n            data: response.rows || [],\n            success: response.code === 200,\n            total: response.total || 0,\n          };\n        }}\n        columns={columns}\n      />\n\n      <Modal\n        title=\"提现详情\"\n        open={detailVisible}\n        onCancel={() => setDetailVisible(false)}\n        footer={null}\n        width={800}\n      >\n        <Descriptions column={2}>\n          <Descriptions.Item label=\"提现ID\">{currentRow?.id}</Descriptions.Item>\n          <Descriptions.Item label=\"用户ID\">{currentRow?.uid}</Descriptions.Item>\n          <Descriptions.Item label=\"用户类型\">\n            {getUserTypeTag(currentRow?.type)}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"提现金额\">\n            ¥{currentRow?.cash}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"提现平台\">\n            {currentRow?.platform}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"卡号/账号\">\n            {currentRow?.card}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"状态\">\n            {getStatusTag(currentRow?.status)}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"用户备注\">\n            {currentRow?.remark || '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {currentRow?.createTime}\n          </Descriptions.Item>\n          {currentRow?.status !== 2 && (\n            <>\n              <Descriptions.Item label=\"审核人\">\n                {currentRow?.updateId || '-'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"审核时间\">\n                {currentRow?.updateTime || '-'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"审核反馈\" span={2}>\n                {currentRow?.feedback || '-'}\n              </Descriptions.Item>\n            </>\n          )}\n        </Descriptions>\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default WithdrawalList;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/capital/withdrawal/index.tsx"}, "25": {"path": "/capital/flow", "name": "capital-flow", "perms": "payment:flow:view", "access": "routeFilter", "file": "@/pages/capital/flow/index.tsx", "parentId": "22", "id": "25", "absPath": "/capital/flow", "__content": "import { Button, Modal, Tag, Descriptions } from 'antd';\nimport React, { useState, useRef } from 'react';\nimport { PageContainer } from '@ant-design/pro-layout';\nimport type { ProColumns, ActionType } from '@ant-design/pro-table';\nimport ProTable from '@ant-design/pro-table';\nimport { listCurr as listCapitalFlow } from '@/services/test-swagger/moneyController';\nimport type { SortOrder } from 'antd/lib/table/interface';\n\nconst CapitalFlowList: React.FC = () => {\n  const [currentRow, setCurrentRow] = useState<API.CapitalFlow>();\n  const actionRef = useRef<ActionType>();\n  const [detailVisible, setDetailVisible] = useState<boolean>(false);\n\n  // 获取类型标签\n  const getTypeTag = (type?: number) => {\n    switch (type) {\n      case 0:\n        return <Tag color=\"blue\">订单收益</Tag>;\n      case 1:\n        return <Tag color=\"green\">跑腿提现</Tag>;\n      case 2:\n        return <Tag color=\"orange\">代理提现</Tag>;\n      default:\n        return <Tag color=\"default\">未知</Tag>;\n    }\n  };\n\n  // 处理排序参数\n  const handleSorter = (sorter: Record<string, SortOrder>) => {\n    const key = Object.keys(sorter)[0];\n    const order = sorter[key];\n    if (key && order) {\n      // 转换字段名为下划线格式\n      const column = key.replace(/([A-Z])/g, '_$1').toLowerCase();\n      return {\n        orderByColumn: column,\n        isAsc: order === 'ascend' ? 'asc' : 'desc',\n      };\n    }\n    return {};\n  };\n\n  const columns: ProColumns<API.CapitalFlow>[] = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      hideInSearch: true,\n    },\n    {\n      title: '订单ID',\n      dataIndex: 'orderId',\n    },\n    {\n      title: '类型',\n      dataIndex: 'type',\n      valueType: 'select',\n      valueEnum: {\n        0: { text: '订单收益', status: 'Processing' },\n        1: { text: '跑腿提现', status: 'Success' },\n        2: { text: '代理提现', status: 'Warning' },\n      },\n    },\n    {\n      title: '代理收益',\n      dataIndex: 'profitAgent',\n      hideInSearch: true,\n      render: (val) => (val ? `¥${val}` : '-'),\n      sorter: true,\n    },\n    {\n      title: '跑腿收益',\n      dataIndex: 'profitRunner',\n      hideInSearch: true,\n      render: (val) => (val ? `¥${val}` : '-'),\n      sorter: true,\n    },\n    {\n      title: '用户收益',\n      dataIndex: 'profitUser',\n      hideInSearch: true,\n      render: (val) => (val ? `¥${val}` : '-'),\n      sorter: true,\n    },\n    {\n      title: '平台收益',\n      dataIndex: 'profitPlat',\n      hideInSearch: true,\n      render: (val) => (val ? `¥${val}` : '-'),\n      sorter: true,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      valueType: 'dateTimeRange',\n      hideInTable: true,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      hideInSearch: true,\n      sorter: true,\n    },\n    {\n      title: '操作',\n      dataIndex: 'option',\n      valueType: 'option',\n      render: (_, record) => [\n        <Button\n          key=\"detail\"\n          type=\"link\"\n          onClick={() => {\n            setCurrentRow(record);\n            setDetailVisible(true);\n          }}\n        >\n          详情\n        </Button>,\n      ],\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <ProTable<API.CapitalFlow>\n        headerTitle=\"资金流动\"\n        actionRef={actionRef}\n        rowKey=\"id\"\n        search={{\n          labelWidth: 120,\n          defaultCollapsed: false,\n        }}\n        request={async (params, sorter) => {\n          const { current, pageSize, createTime, ...rest } = params;\n          // 获取排序参数\n          const sortParams = handleSorter(sorter);\n          \n          const response = await listCapitalFlow({\n            capitalFlow: {\n              ...rest,\n              createTime: createTime?.[0],\n            },\n            pageQuery: {\n              pageSize,\n              pageNum: current,\n              ...sortParams, // 添加排序参数\n            },\n          });\n          return {\n            data: response.rows || [],\n            success: response.code === 200,\n            total: response.total || 0,\n          };\n        }}\n        columns={columns}\n        toolBarRender={false}\n        pagination={{\n          showQuickJumper: true,\n          showSizeChanger: true,\n        }}\n      />\n\n      <Modal\n        title=\"资金流动详情\"\n        open={detailVisible}\n        onCancel={() => setDetailVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Descriptions column={2}>\n          <Descriptions.Item label=\"ID\">{currentRow?.id}</Descriptions.Item>\n          <Descriptions.Item label=\"订单ID\">{currentRow?.orderId}</Descriptions.Item>\n          <Descriptions.Item label=\"类型\">\n            {getTypeTag(currentRow?.type)}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"代理ID\">\n            {currentRow?.agentId || '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"代理收益\">\n            {currentRow?.profitAgent ? `¥${currentRow.profitAgent}` : '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"跑腿ID\">\n            {currentRow?.runnerId || '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"跑腿收益\">\n            {currentRow?.profitRunner ? `¥${currentRow.profitRunner}` : '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"用户ID\">\n            {currentRow?.userId || '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"用户收益\">\n            {currentRow?.profitUser ? `¥${currentRow.profitUser}` : '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"平台收益\">\n            {currentRow?.profitPlat ? `¥${currentRow.profitPlat}` : '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\" span={2}>\n            {currentRow?.createTime}\n          </Descriptions.Item>\n        </Descriptions>\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default CapitalFlowList;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/capital/flow/index.tsx"}, "26": {"path": "/capital/mywallet", "name": "my-wallet", "perms": "payment:mywallet:view", "access": "routeFilter", "file": "@/pages/capital/mywallet/index.tsx", "parentId": "22", "id": "26", "absPath": "/capital/mywallet", "__content": "import { Card, Row, Col, Button, Modal, Form, Input, message, Select, Descriptions, Tag, Result } from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { PageContainer } from '@ant-design/pro-layout';\nimport { wallet, submitRecode, lastRecode } from '@/services/test-swagger/moneyController';\nimport { StatisticCard } from '@ant-design/pro-components';\nimport { useModel } from '@umijs/max';\n\nconst { Statistic } = StatisticCard;\n\nconst MyWallet: React.FC = () => {\n  const [walletData, setWalletData] = useState<API.Wallet>();\n  const [lastRecord, setLastRecord] = useState<API.MoneyRecode>();\n  const [withdrawVisible, setWithdrawVisible] = useState<boolean>(false);\n  const [form] = Form.useForm();\n  const [cardPlaceholder, setCardPlaceholder] = useState('请输入支付宝账号/手机号');\n  const [remarkPlaceholder, setRemarkPlaceholder] = useState('可注明备注');\n\n  // 获取当前用户信息\n  const { initialState } = useModel('@@initialState');\n  const currentUser = initialState?.currentUser;\n  const userType = currentUser?.user?.userType;\n\n  // 如果是普通管理员，显示无权限页面\n  if (userType === 2) {\n    return (\n      <PageContainer>\n        <Result\n          status=\"403\"\n          title=\"无权访问\"\n          subTitle=\"抱歉，您没有权限访问此页面\"\n        />\n      </PageContainer>\n    );\n  }\n\n  const fetchData = async () => {\n    try {\n      const walletRes = await wallet();\n      if (walletRes.code === 200) {\n        setWalletData(walletRes.data);\n      }\n      \n      const lastRes = await lastRecode();\n      if (lastRes.code === 200) {\n        setLastRecord(lastRes.data);\n      }\n    } catch (error) {\n      message.error('获取数据失败');\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const handleWithdraw = async (values: API.MoneyRecode) => {\n    try {\n      const res = await submitRecode(values);\n      if (res.code === 200) {\n        message.success('提现申请提交成功');\n        setWithdrawVisible(false);\n        form.resetFields();\n        fetchData();\n      } else {\n        message.error(res.msg || '提交失败');\n      }\n    } catch (error) {\n      message.error('提交失败');\n    }\n  };\n\n  const getStatusTag = (status?: number) => {\n    const statusMap = {\n      0: <Tag color=\"red\">已驳回</Tag>,\n      1: <Tag color=\"green\">已通过</Tag>,\n      2: <Tag color=\"blue\">审核中</Tag>,\n    };\n    return status !== undefined ? statusMap[status] : '-';\n  };\n\n  const handlePlatformChange = (value: number) => {\n    if (value === 0) {\n      setCardPlaceholder('请输入支付宝账号/手机号');\n      setRemarkPlaceholder('可注明备注');\n    }\n    if (value === 1) {\n      setCardPlaceholder('请输入银行卡号');\n      setRemarkPlaceholder('注明 开户银行 和 持卡人姓名');\n    }\n  };\n\n  return (\n    <PageContainer>\n      <Row gutter={[16, 16]}>\n        <Col span={24}>\n          <Card bordered={false}>\n            <Row gutter={16}>\n              <Col span={8}>\n                <Statistic\n                  title=\"当前余额\"\n                  value={walletData?.balance || 0}\n                  prefix=\"¥\"\n                  precision={2}\n                  style={{ color: '#52c41a' }}\n                />\n              </Col>\n              <Col span={8}>\n                <Statistic\n                  title=\"已提现\"\n                  value={walletData?.withdrawn || 0}\n                  prefix=\"¥\"\n                  precision={2}\n                  style={{ color: '#1890ff' }}\n                />\n              </Col>\n              <Col span={8}>\n                <Statistic\n                  title=\"总收入\"\n                  value={(Number(walletData?.balance) + Number(walletData?.withdrawn)) || 0}\n                  prefix=\"¥\"\n                  precision={2}\n                  style={{ color: '#f5222d' }}\n                />\n              </Col>\n            </Row>\n            <Row style={{ marginTop: 24 }}>\n              <Col span={24} style={{ textAlign: 'right' }}>\n                {/* 只有校区代理(userType === 1)才显示提现按钮 */}\n                {userType === 1 && (\n                  <Button \n                    type=\"primary\" \n                    onClick={() => setWithdrawVisible(true)}\n                    disabled={!walletData?.balance || walletData.balance <= 0}\n                  >\n                    申请提现\n                  </Button>\n                )}\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {lastRecord && (\n          <Col span={24}>\n            <Card \n              title=\"最近一次提现记录\" \n              bordered={false}\n            >\n              <Descriptions column={2}>\n                <Descriptions.Item label=\"提现金额\">¥{lastRecord.cash}</Descriptions.Item>\n                <Descriptions.Item label=\"提现状态\">{getStatusTag(lastRecord.status)}</Descriptions.Item>\n                <Descriptions.Item label=\"提现平台\">{lastRecord.platform}</Descriptions.Item>\n                <Descriptions.Item label=\"卡号/账号\">{lastRecord.card}</Descriptions.Item>\n                <Descriptions.Item label=\"申请时间\">{lastRecord.createTime}</Descriptions.Item>\n                <Descriptions.Item label=\"审核时间\">{lastRecord.updateTime || '-'}</Descriptions.Item>\n                {lastRecord.status !== 2 && (\n                  <Descriptions.Item label=\"审核反馈\" span={2}>\n                    {lastRecord.feedback || '-'}\n                  </Descriptions.Item>\n                )}\n              </Descriptions>\n            </Card>\n          </Col>\n        )}\n      </Row>\n\n      <Modal\n        title=\"申请提现\"\n        open={withdrawVisible}\n        onCancel={() => {\n          setWithdrawVisible(false);\n          form.resetFields();\n          setCardPlaceholder('请输入支付宝账号/手机号');\n          setRemarkPlaceholder('可注明备注');\n        }}\n        onOk={() => form.submit()}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleWithdraw}\n          initialValues={{\n            platform: 0,\n          }}\n        >\n          <Form.Item\n            label=\"提现金额\"\n            name=\"cash\"\n            rules={[\n              { required: true, message: '请输入提现金额' },\n              {\n                validator: (_, value) => {\n                  if (value && value > (walletData?.balance || 0)) {\n                    return Promise.reject(new Error('提现金额不能大于可用余额'));\n                  }\n                  if (value && value <= 0) {\n                    return Promise.reject(new Error('提现金额必须大于0'));\n                  }\n                  return Promise.resolve();\n                },\n              },\n            ]}\n          >\n            <Input prefix=\"¥\" type=\"number\" placeholder=\"请输入提现金额\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"提现平台\"\n            name=\"platform\"\n            rules={[{ required: true, message: '请选择提现平台' }]}\n          >\n            <Select onChange={handlePlatformChange}>\n              <Select.Option value={0}>支付宝转账</Select.Option>\n              <Select.Option value={1}>银行卡转账</Select.Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            label=\"收款账号\"\n            name=\"card\"\n            rules={[{ required: true, message: '请输入收款账号' }]}\n          >\n            <Input placeholder={cardPlaceholder} />\n          </Form.Item>\n\n          <Form.Item\n            label=\"备注\"\n            name=\"remark\"\n            rules={[\n              { \n                required: form.getFieldValue('platform') === 1, \n                message: '请填写开户银行和持卡人姓名' \n              }\n            ]}\n          >\n            <Input.TextArea placeholder={remarkPlaceholder} />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default MyWallet;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/capital/mywallet/index.tsx"}, "27": {"path": "/capital/myflow", "name": "my-flow", "perms": "payment:myflow:view", "access": "routeFilter", "file": "@/pages/capital/myflow/index.tsx", "parentId": "22", "id": "27", "absPath": "/capital/myflow", "__content": "import { PageContainer } from '@ant-design/pro-layout';\nimport type { ProColumns } from '@ant-design/pro-table';\nimport ProTable from '@ant-design/pro-table';\nimport { Tag } from 'antd';\nimport React from 'react';\nimport { listCurr } from '@/services/test-swagger/moneyController';\nimport { ReloadOutlined } from '@ant-design/icons';\nimport { useModel } from '@umijs/max';\n\nconst MyFlow: React.FC = () => {\n  // 获取当前用户信息\n  const { initialState } = useModel('@@initialState');\n  const currentUser = initialState?.currentUser;\n  const userType = currentUser?.user?.userType;\n\n  // 根据用户类型获取流水类型选项\n  const getTypeValueEnum = () => {\n    const baseTypes = {\n      0: { text: '订单收益', status: 'success' },\n      2: { text: '代理提现', status: 'error' },\n    };\n\n    // 如果不是校区代理，添加跑腿提现选项\n    if (userType !== 1) {\n      return {\n        ...baseTypes,\n        1: { text: '跑腿提现', status: 'warning' },\n      };\n    }\n\n    return baseTypes;\n  };\n\n  // 定义流水类型\n  const getFlowType = (type?: number) => {\n    const typeMap = {\n      0: { text: '订单收益', color: 'success' },\n      1: { text: '跑腿提现', color: 'warning' },\n      2: { text: '代理提现', color: 'error' },\n    };\n    const defaultType = { text: '-', color: 'default' };\n    return type !== undefined ? typeMap[type] || defaultType : defaultType;\n  };\n\n  const renderProfit = (val: number | null) => {\n    if (val === null) {\n      return <span>¥0.00</span>;\n    }\n    const amount = Number(val);\n    return (\n      <span style={{ \n        color: amount < 0 ? '#52c41a' : (amount > 0 ? '#f5222d' : '#000000')\n      }}>\n        ¥{amount.toFixed(2)}\n      </span>\n    );\n  };\n\n  const columns: ProColumns<API.CapitalFlow>[] = [\n    {\n      title: 'id',\n      dataIndex: 'id',\n      width: 80,\n      align: 'center',\n      hideInSearch: true,\n    },\n    {\n      title: '订单号',\n      dataIndex: 'orderId',\n      copyable: true,\n      width: 100,\n      align: 'center',\n      hideInSearch: true,\n    },\n    {\n      title: '类型',\n      dataIndex: 'type',\n      width: 100,\n      align: 'center',\n      valueEnum: getTypeValueEnum(),\n      render: (_, record) => {\n        const flowType = getFlowType(record.type);\n        return <Tag color={flowType.color}>{flowType.text}</Tag>;\n      },\n    },\n    {\n      title: '代理收益',\n      dataIndex: 'profitAgent',\n      width: 120,\n      align: 'right',\n      hideInSearch: true,\n      render: (val) => renderProfit(val),\n    },\n    {\n      title: '跑腿收益',\n      dataIndex: 'profitRunner',\n      width: 120,\n      align: 'right',\n      hideInSearch: true,\n      render: (val) => renderProfit(val),\n    },\n    {\n      title: '用户收益',\n      dataIndex: 'profitUser',\n      width: 120,\n      align: 'right',\n      hideInSearch: true,\n      render: (val) => renderProfit(val),\n    },\n    {\n      title: '平台收益',\n      dataIndex: 'profitPlat',\n      width: 120,\n      align: 'right',\n      hideInSearch: true,\n      render: (val) => renderProfit(val),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      width: 180,\n      align: 'center',\n      valueType: 'dateTime',\n      sorter: true,\n      hideInSearch: true,\n    },\n    {\n      title: '时间范围',\n      dataIndex: 'createTime',\n      valueType: 'dateTimeRange',\n      hideInTable: true,\n      search: {\n        transform: (value) => {\n          return {\n            beginTime: value[0],\n            endTime: value[1],\n          };\n        },\n      },\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <ProTable<API.CapitalFlow>\n        headerTitle=\"账户明细\"\n        rowKey=\"id\"\n        search={{\n          labelWidth: 120,\n          defaultCollapsed: false,\n        }}\n        request={async (params) => {\n          const { current, pageSize, beginTime, endTime, ...rest } = params;\n          const response = await listCurr({\n            pageQuery: {\n              pageSize,\n              pageNum: current,\n            },\n            capitalFlow: {\n              ...rest,\n              beginTime,\n              endTime,\n            },\n          });\n          return {\n            data: response.rows || [],\n            success: response.code === 200,\n            total: response.total || 0,\n          };\n        }}\n        columns={columns}\n        pagination={{\n          showQuickJumper: true,\n          showSizeChanger: true,\n          showTotal: (total) => `共 ${total} 条记录`,\n          defaultPageSize: 10,\n          pageSizeOptions: ['10', '20', '50', '100'],\n        }}\n        bordered\n        dateFormatter=\"string\"\n        toolbar={{\n          actions: [\n            <ReloadOutlined\n              key=\"reload\"\n              onClick={() => window.location.reload()}\n              style={{ fontSize: '16px', cursor: 'pointer' }}\n            />,\n          ],\n        }}\n        options={{\n          density: true,\n          fullScreen: true,\n          reload: true,\n          setting: true,\n        }}\n      />\n    </PageContainer>\n  );\n};\n\nexport default MyFlow;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/capital/myflow/index.tsx"}, "28": {"name": "school", "icon": "home", "path": "/school", "parentId": "ant-design-pro-layout", "id": "28", "absPath": "/school"}, "29": {"path": "/school/campus", "name": "campus", "perms": "address:school:view", "access": "routeFilter", "file": "@/pages/school/campus/index.tsx", "parentId": "28", "id": "29", "absPath": "/school/campus", "__content": "import { PlusOutlined } from '@ant-design/icons';\nimport { Button, message, Modal, Form } from 'antd';\nimport React, { useState, useRef } from 'react';\nimport { PageContainer } from '@ant-design/pro-layout';\nimport type { ProColumns, ActionType } from '@ant-design/pro-table';\nimport ProTable from '@ant-design/pro-table';\nimport { upload } from '@/services/test-swagger/ossController';\n\nimport ProForm, {\n  ModalForm,\n  ProFormText,\n  ProFormDigit,\n  ProFormSelect,\n  ProFormUploadButton,\n} from '@ant-design/pro-form';\nimport { add, edit, list5 as list6, get1 } from '@/services/test-swagger/schoolController';\n\nconst TableList: React.FC = () => {\n  const [createModalVisible, handleModalVisible] = useState<boolean>(false);\n  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);\n  const [currentRow, setCurrentRow] = useState<API.School>();\n  const actionRef = useRef<ActionType>();\n  const [form] = Form.useForm();\n\n  const handleUpdateModalClose = () => {\n    handleUpdateModalVisible(false);\n    setCurrentRow(undefined);\n    form.resetFields();\n  };\n\n  const handleEdit = (record: API.School) => {\n    form.resetFields();\n    setCurrentRow(record);\n    form.setFieldsValue({\n      ...record,\n      logo: record.logo ? [\n        {\n          uid: '-1',\n          name: 'image.png',\n          status: 'done',\n          url: record.logo,\n          response: { url: record.logo },\n        }\n      ] : undefined,\n    });\n    handleUpdateModalVisible(true);\n  };\n\n  const columns: ProColumns<API.School>[] = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      hideInForm: true,\n    },\n    {\n      title: '学校名称',\n      dataIndex: 'name',\n    },\n    {\n      title: '学校Logo',\n      dataIndex: 'logo',\n      valueType: 'image',\n      hideInSearch: true,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      valueEnum: {\n        0: { text: '禁用', status: 'Error' },\n        1: { text: '启用', status: 'Success' },\n      },\n    },\n    {\n      title: '平台收益占比',\n      dataIndex: 'profitPlat',\n      renderText: (val: number) => `${val}%`,\n      hideInSearch: true,\n    },\n    {\n      title: '跑腿收益占比',\n      dataIndex: 'profitRunner',\n      renderText: (val: number) => `${val}%`,\n      hideInSearch: true,\n    },\n    {\n      title: '追加金额底价',\n      dataIndex: 'floorPrice',\n      valueType: 'money',\n      hideInSearch: true,\n    },\n    {\n      title: '追加金额订单平台分成',\n      dataIndex: 'additionalProfitRate',\n      render: (_, record) => record.additionalProfitRate ? `${record.additionalProfitRate}%` : '-',\n      hideInSearch: true,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      valueType: 'dateTime',\n      hideInForm: true,\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updateTime',\n      valueType: 'dateTime',\n      hideInForm: true,\n      hideInSearch: true,\n    },\n    {\n      title: '操作',\n      dataIndex: 'option',\n      valueType: 'option',\n      render: (_, record) => [\n        <a\n          key=\"edit\"\n          onClick={() => {\n            handleEdit(record);\n          }}\n        >\n          编辑\n        </a>,\n        <a\n          key=\"delete\"\n          onClick={() => {\n            Modal.confirm({\n              title: '确认删除',\n              content: '确定要删除这条记录吗？',\n              onOk: async () => {\n                try {\n                  await get1({ id: record.id! });\n                  message.success('删除成功');\n                  actionRef.current?.reload();\n                } catch (error) {\n                  message.error('删除失败，请重试');\n                }\n              },\n            });\n          }}\n        >\n          删除\n        </a>,\n      ],\n    },\n  ];\n\n  const handleAdd = async (fields: API.School) => {\n    const hide = message.loading('正在添加');\n    try {\n      await add(fields);\n      hide();\n      message.success('添加成功');\n      handleModalVisible(false);\n      actionRef.current?.reload();\n      return true;\n    } catch (error) {\n      hide();\n      message.error('添加失败，请重试');\n      return false;\n    }\n  };\n\n  const handleUpdate = async (fields: API.School) => {\n    const hide = message.loading('正在更新');\n    try {\n      await edit({\n        ...currentRow,\n        ...fields,\n      });\n      hide();\n      message.success('更新成功');\n      handleUpdateModalVisible(false);\n      actionRef.current?.reload();\n      return true;\n    } catch (error) {\n      hide();\n      message.error('更新失败，请重试');\n      return false;\n    }\n  };\n\n  return (\n    <PageContainer>\n      <ProTable<API.School>\n        headerTitle=\"校区列表\"\n        actionRef={actionRef}\n        rowKey=\"id\"\n        search={{\n          labelWidth: 120,\n        }}\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            onClick={() => handleModalVisible(true)}\n          >\n            <PlusOutlined /> 新建\n          </Button>,\n        ]}\n        request={async (params, sort, filter) => {\n          const { current, pageSize, ...restParams } = params;\n          \n          const response = await list6({\n            pageQuery: {\n              pageSize,\n              pageNum: current,\n            },\n            school: restParams as any,\n          });\n          \n          return {\n            data: response.rows || [],\n            success: response.code === 200,\n            total: response.total || 0,\n          };\n        }}\n        columns={columns}\n      />\n\n      <ModalForm\n        title=\"新建校区\"\n        width=\"400px\"\n        visible={createModalVisible}\n        onVisibleChange={handleModalVisible}\n        onFinish={handleAdd}\n      >\n        <ProFormText\n          rules={[\n            {\n              required: true,\n              message: '校区名称为必填项',\n            },\n          ]}\n          label=\"校区名称\"\n          name=\"name\"\n        />\n        <ProFormUploadButton\n          label=\"学校Logo\"\n          name=\"logo\"\n          max={1}\n          fieldProps={{\n            name: 'file',\n            listType: 'picture-card',\n            showUploadList: true,\n            customRequest: async ({ file, onSuccess, onError }) => {\n              try {\n                const res = await upload(\n                  { type: 7, name: 'school' },\n                  {},\n                  file as File\n                );\n                if (res.code === 200 && res.data) {\n                  onSuccess?.({ url: res.data.url || res.data });\n                } else {\n                  onError?.(new Error('上传失败'));\n                }\n              } catch (error) {\n                onError?.(new Error('上传失败'));\n              }\n            },\n          }}\n          transform={(value) => {\n            if (!value) return { logo: '' };\n            return { logo: value?.[0]?.response?.url || '' };\n          }}\n        />\n        <ProFormSelect\n          name=\"status\"\n          label=\"状态\"\n          options={[\n            { label: '启用', value: 1 },\n            { label: '禁用', value: 0 },\n          ]}\n          rules={[{ required: true }]}\n        />\n        <ProFormDigit\n          label=\"平台收益占比\"\n          name=\"profitPlat\"\n          min={0}\n          max={100}\n          fieldProps={{ addonAfter: '%' }}\n        />\n        <ProFormDigit\n          label=\"跑腿收益占比\"\n          name=\"profitRunner\"\n          min={0}\n          max={100}\n          fieldProps={{ addonAfter: '%' }}\n        />\n        <ProFormDigit\n          label=\"追加金额底价\"\n          name=\"floorPrice\"\n          min={0}\n          fieldProps={{ precision: 2 }}\n        />\n        <ProFormDigit\n          label=\"追加金额订单平台分成比例\"\n          name=\"additionalProfitRate\"\n          min={0}\n          max={100}\n          fieldProps={{ addonAfter: '%' }}\n          tooltip=\"设置追加金额订单的平台分成比例，不设置则使用默认平台分成比例\"\n        />\n        <ProFormText\n          name=\"adcode\"\n          label=\"行政区划代码\"\n          rules={[\n            {\n              required: true,\n              message: '请输入行政区划代码',\n            },\n            {\n              pattern: /^\\d{6}$/,\n              message: '行政区划代码必须是6位数字',\n            },\n          ]}\n          placeholder=\"请输入6位数字的行政区划代码\"\n          fieldProps={{\n            maxLength: 6,\n          }}\n        />\n      </ModalForm>\n\n      <ModalForm\n        title=\"编辑校区\"\n        width=\"400px\"\n        visible={updateModalVisible}\n        onVisibleChange={(visible) => {\n          if (!visible) {\n            handleUpdateModalClose();\n          }\n        }}\n        form={form}\n        onFinish={handleUpdate}\n      >\n        <ProFormText\n          rules={[\n            {\n              required: true,\n              message: '校区名称为必填项',\n            },\n          ]}\n          label=\"校区名称\"\n          name=\"name\"\n        />\n        <ProFormUploadButton\n          label=\"学校Logo\"\n          name=\"logo\"\n          max={1}\n          fieldProps={{\n            name: 'file',\n            listType: 'picture-card',\n            showUploadList: true,\n            customRequest: async ({ file, onSuccess, onError }) => {\n              try {\n                const res = await upload(\n                  { type: 7, name: 'school' },\n                  {},\n                  file as File\n                );\n                if (res.code === 200 && res.data) {\n                  const url = res.data.url || res.data;\n                  onSuccess?.({ url });\n                } else {\n                  onError?.(new Error('上传失败'));\n                }\n              } catch (error) {\n                onError?.(new Error('上传失败'));\n              }\n            },\n          }}\n          transform={(value) => {\n            if (!value || value.length === 0) return { logo: '' };\n            if (value[0].url) return { logo: value[0].url };\n            return { logo: value[0]?.response?.url || '' };\n          }}\n        />\n        <ProFormSelect\n          name=\"status\"\n          label=\"状态\"\n          options={[\n            { label: '启用', value: 1 },\n            { label: '禁用', value: 0 },\n          ]}\n          rules={[{ required: true }]}\n        />\n        <ProFormDigit\n          label=\"平台收益占比\"\n          name=\"profitPlat\"\n          min={0}\n          max={100}\n          fieldProps={{ addonAfter: '%' }}\n        />\n        <ProFormDigit\n          label=\"跑腿收益占比\"\n          name=\"profitRunner\"\n          min={0}\n          max={100}\n          fieldProps={{ addonAfter: '%' }}\n        />\n        <ProFormDigit\n          label=\"追加金额底价\"\n          name=\"floorPrice\"\n          min={0}\n          fieldProps={{ precision: 2 }}\n        />\n        <ProFormDigit\n          label=\"追加金额订单平台分成比例\"\n          name=\"additionalProfitRate\"\n          min={0}\n          max={100}\n          fieldProps={{ addonAfter: '%' }}\n          tooltip=\"设置追加金额订单的平台分成比例，不设置则使用默认平台分成比例\"\n        />\n        <ProFormText\n          name=\"adcode\"\n          label=\"行政区划代码\"\n          rules={[\n            {\n              required: true,\n              message: '请输入行政区划代码',\n            },\n            {\n              pattern: /^\\d{6}$/,\n              message: '行政区划代码必须是6位数字',\n            },\n          ]}\n          placeholder=\"请输入6位数字的行政区划代码\"\n          fieldProps={{\n            maxLength: 6,\n          }}\n        />\n      </ModalForm>\n    </PageContainer>\n  );\n};\n\nexport default TableList; ", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/src/pages/school/campus/index.tsx"}, "30": {"path": "/school/region", "name": "region", "perms": "address:region:view", "access": "routeFilter", "file": "@/pages/school/region/index.tsx", "parentId": "28", "id": "30", "absPath": "/school/region", "__content": "import { PlusOutlined, EnvironmentOutlined, EyeOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';\nimport { Button, message, Modal, Select, Form, Input, Popconfirm, Space, Card, Empty, Descriptions, Divider } from 'antd';\nimport React, { useState, useRef, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-layout';\nimport type { ProColumns, ActionType } from '@ant-design/pro-table';\nimport ProTable from '@ant-design/pro-table';\nimport ProForm, {\n  ModalForm,\n  ProFormText,\n  ProFormSelect,\n  ProFormTextArea,\n  ProFormDigit,\n} from '@ant-design/pro-form';\nimport { add3 as addRegion, edit5 as editRegion, list7 as listRegion, remove5 as removeRegion } from '@/services/test-swagger/regionController';\nimport { list5 as listSchools } from '@/services/test-swagger/schoolController';\nimport { useModel } from '@umijs/max';\n\nconst RegionList: React.FC = () => {\n  const [modalVisible, handleModalVisible] = useState<boolean>(false);\n  const [currentRow, setCurrentRow] = useState<API.SchoolRegion>();\n  const [selectedSchool, setSelectedSchool] = useState<number>();\n  const [schoolOptions, setSchoolOptions] = useState<{ label: string; value: number; }[]>([]);\n  const [regionData, setRegionData] = useState<API.RegionVO[]>([]);\n  const [form] = Form.useForm();\n  const actionRef = useRef<ActionType>();\n  const [detailVisible, setDetailVisible] = useState<boolean>(false);\n  const [detailData, setDetailData] = useState<API.RegionVO | API.SchoolRegion>();\n\n  // 获取当前用户信息\n  const { initialState } = useModel('@@initialState');\n  const currentUser = initialState?.currentUser as API.CurrentUser & {\n    user: {\n      userType: number;\n      userPc: {\n        agentSchool: API.School;\n        agentSchoolId: number;\n      };\n    };\n  };\n  const isSchoolAgent = currentUser?.user?.userType === 1;\n  const agentSchool = currentUser?.user?.userPc?.agentSchool;\n  const agentSchoolId = currentUser?.user?.userPc?.agentSchoolId;\n\n  // 获取校区列表\n  const fetchSchoolList = async () => {\n    try {\n      const res = await listSchools({\n        pageQuery: {\n          pageSize: 999,\n          pageNum: 1,\n        },\n        school: {}\n      });\n      if (res.code === 200) {\n        const options = (res.rows || []).map((item) => ({\n          label: item.name || '',\n          value: item.id || 0,\n        }));\n        setSchoolOptions(options);\n      }\n    } catch (error) {\n      message.error('获取校区列表失败');\n    }\n  };\n\n  // 获取区域数据\n  const fetchRegionData = async (schoolId: number) => {\n    try {\n      const res = await listRegion({ schoolId });\n      if (res.code === 200) {\n        setRegionData(res.data || []);\n      }\n    } catch (error) {\n      message.error('获取区域数据失败');\n    }\n  };\n\n  useEffect(() => {\n    // 只有非校区代理才获取校区列表\n    if (!isSchoolAgent) {\n      fetchSchoolList();\n    } else if (agentSchoolId) {\n      // 校区代理直接设置选中的校区并获取数据\n      setSelectedSchool(agentSchoolId);\n    }\n  }, [isSchoolAgent, agentSchoolId]);\n\n  useEffect(() => {\n    if (selectedSchool) {\n      fetchRegionData(selectedSchool);\n    } else {\n      setRegionData([]);\n    }\n  }, [selectedSchool]);\n\n  const columns: ProColumns<API.RegionVO>[] = [\n    {\n      title: '区域名称',\n      dataIndex: 'name',\n      ellipsis: true,\n      width: 180,\n    },\n    {\n      title: '楼栋数量',\n      width: 100,\n      render: (_, record) => record.childrens?.length || 0,\n    },\n    {\n      title: '备注',\n      dataIndex: 'remark',\n      ellipsis: true,\n      width: 200,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      width: 180,\n    },\n    {\n      title: '操作',\n      width: 120,\n      valueType: 'option',\n      render: (_, record) => [\n        <Button\n          key=\"detail\"\n          type=\"link\"\n          icon={<EyeOutlined />}\n          onClick={() => handleDetailOpen(record)}\n        />,\n        <Button\n          key=\"addBuilding\"\n          type=\"link\"\n          icon={<PlusOutlined />}\n          onClick={() => handleModalOpen(undefined, record.id)}\n        />,\n        <Button\n          key=\"edit\"\n          type=\"link\"\n          icon={<EditOutlined />}\n          onClick={() => handleModalOpen(record)}\n        />,\n        <Popconfirm\n          key=\"delete\"\n          title=\"确定要删除这个区域吗？删除后其下所有楼栋也将被删除！\"\n          onConfirm={() => handleDelete(record.id)}\n        >\n          <Button\n            type=\"link\"\n            danger\n            icon={<DeleteOutlined />}\n          />\n        </Popconfirm>,\n      ],\n    },\n  ];\n\n  const buildingColumns: ProColumns<API.SchoolRegion>[] = [\n    {\n      title: '楼栋名称',\n      dataIndex: 'name',\n      ellipsis: true,\n    },\n    {\n      title: '备注',\n      dataIndex: 'remark',\n      ellipsis: true,\n    },\n    {\n      title: '操作',\n      width: 90,\n      valueType: 'option',\n      render: (_, record) => [\n        <Button\n          key=\"detail\"\n          type=\"link\"\n          icon={<EyeOutlined />}\n          onClick={() => handleDetailOpen(record)}\n        />,\n        <Button\n          key=\"edit\"\n          type=\"link\"\n          icon={<EditOutlined />}\n          onClick={() => handleModalOpen(record)}\n        />,\n        <Popconfirm\n          key=\"delete\"\n          title=\"确定要删除这个楼栋吗？\"\n          onConfirm={() => handleDelete(record.id)}\n        >\n          <Button\n            type=\"link\"\n            danger\n            icon={<DeleteOutlined />}\n          />\n        </Popconfirm>,\n      ],\n    },\n  ];\n\n  // 删除区域\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await removeRegion({ schoolRegionIds: [id] });\n      if (response.code === 200) {\n        message.success('删除成功');\n        // 刷新数据\n        if (selectedSchool) {\n          fetchRegionData(selectedSchool);\n        }\n      } else {\n        message.error(response.msg || '删除失败');\n      }\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  // 提交表单\n  const handleSubmit = async (values: API.SchoolRegion) => {\n    try {\n      if (currentRow) {\n        const response = await editRegion({\n          ...values,\n          id: currentRow.id,\n        });\n        if (response.code === 200) {\n          message.success('更新成功');\n          // 刷新数据\n          if (selectedSchool) {\n            fetchRegionData(selectedSchool);\n          }\n        } else {\n          message.error(response.msg || '更新失败');\n          return false;\n        }\n      } else {\n        const response = await addRegion(values);\n        if (response.code === 200) {\n          message.success('添加成功');\n          // 刷新数据\n          if (selectedSchool) {\n            fetchRegionData(selectedSchool);\n          }\n        } else {\n          message.error(response.msg || '添加失败');\n          return false;\n        }\n      }\n      handleModalVisible(false);\n      return true;\n    } catch (error) {\n      message.error('操作失败');\n      return false;\n    }\n  };\n\n  // 打开新增/编辑弹窗\n  const handleModalOpen = (record?: API.RegionVO | API.SchoolRegion, parentId?: number) => {\n    if (record) {\n      // 编辑模式：设置所有字段\n      setCurrentRow(record);\n      form.setFieldsValue(record);\n    } else {\n      // 新建模式：只设置必要的默认字段\n      setCurrentRow(undefined);\n      form.resetFields(); // 先清空所有字段\n      form.setFieldsValue({\n        schoolId: selectedSchool,\n        type: parentId ? 1 : 0,  // 有 parentId 时为楼栋，否则为区域\n        parentId: parentId || 0,\n      });\n    }\n    handleModalVisible(true);\n  };\n\n  // 打开详情弹窗\n  const handleDetailOpen = (record: API.RegionVO | API.SchoolRegion) => {\n    setDetailData(record);\n    setDetailVisible(true);\n  };\n\n  return (\n    <PageContainer>\n      {isSchoolAgent && agentSchool && (\n        <div \n          style={{ \n            marginBottom: 16,\n            padding: '12px 24px',\n            background: '#f5f5f5',\n            borderRadius: '4px',\n            fontSize: '14px'\n          }}\n        >\n          当前校区：<span style={{ fontWeight: 'bold', color: '#1890ff' }}>{agentSchool}</span>\n        </div>\n      )}\n\n      <div style={{ marginBottom: 16, display: 'flex', gap: 16, flexWrap: 'wrap', alignItems: 'center' }}>\n        {/* 只有非校区代理才显示校区选择 */}\n        {!isSchoolAgent && (\n          <Select\n            allowClear\n            showSearch\n            style={{ width: 200 }}\n            placeholder=\"请选择校区\"\n            optionFilterProp=\"label\"\n            options={schoolOptions}\n            value={selectedSchool}\n            onChange={(value) => setSelectedSchool(value)}\n            filterOption={(input, option) =>\n              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())\n            }\n          />\n        )}\n        {selectedSchool && (\n          <Button\n            type=\"primary\"\n            onClick={() => handleModalOpen()}\n            icon={<PlusOutlined />}\n          >\n            新建区域\n          </Button>\n        )}\n      </div>\n\n      {!selectedSchool ? (\n        <Empty description=\"请先选择校区\" />\n      ) : (\n        <div style={{ \n          display: 'grid', \n          gridTemplateColumns: 'repeat(3, 1fr)', \n          gap: 16,\n          padding: '8px 0'\n        }}>\n          {regionData.map((region) => (\n            <Card\n              key={region.id}\n              title={\n                <Space>\n                  <EnvironmentOutlined />\n                  {region.name}\n                  {region.remark && (\n                    <span style={{ fontSize: '12px', color: '#999' }}>\n                      ({region.remark})\n                    </span>\n                  )}\n                </Space>\n              }\n              style={{ \n                minWidth: 300,\n                height: 400,\n                display: 'flex',\n                flexDirection: 'column'\n              }}\n              bodyStyle={{ \n                flex: 1,\n                padding: '12px',\n                overflow: 'hidden',\n                display: 'flex',\n                flexDirection: 'column'\n              }}\n              extra={\n                <Space>\n                  <Button\n                    type=\"link\"\n                    size=\"small\"\n                    icon={<EyeOutlined />}\n                    onClick={() => handleDetailOpen(region)}\n                  />\n                  <Button\n                    type=\"link\"\n                    size=\"small\"\n                    icon={<PlusOutlined />}\n                    onClick={() => handleModalOpen(undefined, region.id)}\n                  />\n                  <Button\n                    type=\"link\"\n                    size=\"small\"\n                    icon={<EditOutlined />}\n                    onClick={() => handleModalOpen(region)}\n                  />\n                  <Popconfirm\n                    title=\"确定要删除这个区域吗？删除后其下所有楼栋也将被删除！\"\n                    onConfirm={() => handleDelete(region.id)}\n                  >\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      danger\n                      icon={<DeleteOutlined />}\n                    />\n                  </Popconfirm>\n                </Space>\n              }\n            >\n              {region.childrens && region.childrens.length > 0 ? (\n                <div style={{ flex: 1, overflow: 'hidden' }}>\n                  <ProTable<API.SchoolRegion>\n                    columns={buildingColumns}\n                    dataSource={region.childrens}\n                    search={false}\n                    options={false}\n                    pagination={false}\n                    rowKey=\"id\"\n                    scroll={{ y: 280 }}\n                    tableStyle={{ \n                      height: '100%',\n                    }}\n                    cardProps={{ \n                      bodyStyle: { \n                        padding: 0,\n                      },\n                    }}\n                  />\n                </div>\n              ) : (\n                <Empty \n                  description=\"暂无楼栋\" \n                  image={Empty.PRESENTED_IMAGE_SIMPLE}\n                  style={{\n                    margin: 'auto'\n                  }}\n                />\n              )}\n            </Card>\n          ))}\n        </div>\n      )}\n\n      <ModalForm\n        title={currentRow ? '编辑' : (form.getFieldValue('type') === 0 ? '新建区域' : '新建楼栋')}\n        width=\"500px\"\n        visible={modalVisible}\n        onVisibleChange={(visible) => {\n          if (!visible) {\n            form.resetFields(); // 关闭时清空表单\n          }\n          handleModalVisible(visible);\n        }}\n        onFinish={handleSubmit}\n        form={form}\n      >\n        <ProFormSelect\n          name=\"schoolId\"\n          label=\"所属校区\"\n          rules={[{ required: true, message: '请选择校区' }]}\n          disabled\n          options={schoolOptions}\n        />\n        <ProFormSelect\n          name=\"type\"\n          label=\"类型\"\n          disabled\n          options={[\n            { label: '区域', value: 0 },\n            { label: '楼栋', value: 1 },\n          ]}\n          rules={[{ required: true, message: '请选择类型' }]}\n        />\n        {form.getFieldValue('type') === 1 && (\n          <ProFormSelect\n            name=\"parentId\"\n            label=\"所属区域\"\n            disabled\n            options={regionData.map(region => ({\n              label: region.name,\n              value: region.id,\n            }))}\n            rules={[{ required: true, message: '请选择所属区域' }]}\n          />\n        )}\n        <ProFormText\n          name=\"name\"\n          label=\"名称\"\n          rules={[{ required: true, message: '请输入名称' }]}\n          placeholder=\"请输入名称\"\n        />\n        {form.getFieldValue('type') === 1 && (\n          <>\n            <ProFormText\n              name=\"lon\"\n              label=\"经度\"\n              rules={[\n                {\n                  pattern: /^-?((0|1?[0-7]?[0-9]?)(([.][0-9]*)?)|180(([.][0]*)?))$/,\n                  message: '请输入有效的经度',\n                },\n              ]}\n              placeholder=\"请输入经度\"\n            />\n            <ProFormText\n              name=\"lat\"\n              label=\"纬度\"\n              rules={[\n                {\n                  pattern: /^-?([0-8]?[0-9]|90)([.][0-9]*)?$/,\n                  message: '请输入有效的纬度',\n                },\n              ]}\n              placeholder=\"请输入纬度\"\n            />\n          </>\n        )}\n        <ProFormTextArea\n          name=\"remark\"\n          label=\"备注\"\n          placeholder=\"请输入备注信息\"\n        />\n      </ModalForm>\n\n      {/* 添加详情弹窗 */}\n      <Modal\n        title={detailData?.type === 0 ? '区域详情' : '楼栋详情'}\n        open={detailVisible}\n        onCancel={() => setDetailVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Descriptions column={2} bordered>\n          <Descriptions.Item label=\"名称\" span={2}>\n            {detailData?.name}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"类型\">\n            {detailData?.type === 0 ? '区域' : '楼栋'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"所属校区\">\n            {schoolOptions.find(item => item.value === detailData?.schoolId)?.label}\n          </Descriptions.Item>\n          {detailData?.type === 1 && (\n            <>\n              <Descriptions.Item label=\"所属区域\">\n                {regionData.find(item => item.id === detailData?.parentId)?.name}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"经度\">\n                {detailData?.lon || '-'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"纬度\">\n                {detailData?.lat || '-'}\n              </Descriptions.Item>\n            </>\n          )}\n          {detailData?.type === 0 && (detailData as API.RegionVO).childrens?.length > 0 && (\n            <Descriptions.Item label=\"楼栋数量\" span={2}>\n              {(detailData as API.RegionVO).childrens?.length || 0}\n            </Descriptions.Item>\n          )}\n          <Descriptions.Item label=\"备注\" span={2}>\n            {detailData?.remark || '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {detailData?.createTime}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建人\">\n            {detailData?.createId || '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"修改时间\">\n            {detailData?.updateTime || '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"修改人\">\n            {detailData?.updateId || '-'}\n          </Descriptions.Item>\n        </Descriptions>\n        {detailData?.type === 0 && (detailData as API.RegionVO).childrens?.length > 0 && (\n          <>\n            <Divider>楼栋列表</Divider>\n            <ProTable<API.SchoolRegion>\n              columns={buildingColumns.filter(col => col.dataIndex !== 'type')}\n              dataSource={(detailData as API.RegionVO).childrens}\n              search={false}\n              options={false}\n              pagination={false}\n              rowKey=\"id\"\n            />\n          </>\n        )}\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default RegionList;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/src/pages/school/region/index.tsx"}, "31": {"name": "oss", "icon": "star", "path": "/oss", "parentId": "ant-design-pro-layout", "id": "31", "absPath": "/oss"}, "32": {"path": "/oss/manage", "name": "oss-manage", "perms": "oss:oss:view", "access": "routeFilter", "file": "@/pages/oss/manage/index.tsx", "parentId": "31", "id": "32", "absPath": "/oss/manage", "__content": "import { PlusOutlined, FileExcelOutlined, FileImageOutlined, FilePdfOutlined, FileWordOutlined, FilePptOutlined, FileOutlined } from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { ProTable, ModalForm, ProFormText, ProFormUploadButton, ProFormSelect } from '@ant-design/pro-components';\nimport { Button, message, Modal, Tag } from 'antd';\nimport React, { useRef, useState } from 'react';\nimport type { ActionType, ProColumns } from '@ant-design/pro-components';\nimport {\n  remove2,\n  list1,\n  upload,\n  download,\n} from '@/services/test-swagger/ossController';\nimport type { OssVO } from '@/services/test-swagger/typings';\n\ntype OssFormProps = {\n  onFinish: (values: any) => Promise<boolean>;\n  onVisibleChange: (visible: boolean) => void;\n  visible: boolean;\n};\n\nconst OssForm: React.FC<OssFormProps> = (props) => {\n  const { visible, onVisibleChange, onFinish } = props;\n\n  return (\n    <ModalForm\n      title=\"上传OSS文件\"\n      visible={visible}\n      onVisibleChange={onVisibleChange}\n      onFinish={onFinish}\n    >\n      <ProFormSelect\n        name=\"type\"\n        label=\"文件类型\"\n        options={[\n          { label: '附件图片', value: 1 },\n          { label: '附件文件', value: 2 },\n          { label: '完成凭证', value: 3 },\n          { label: '用户头像', value: 4 },\n          { label: '学生证照片', value: 5 },\n          { label: '订单申诉凭证', value: 6 },\n          { label: '学校logo', value: 7 },\n          { label: '身份证图片', value: 8 },\n        ]}\n        rules={[{ required: true, message: '请选择文件类型' }]}\n      />\n      <ProFormUploadButton\n        name=\"file\"\n        label=\"选择文件\"\n        max={1}\n        fieldProps={{\n          name: 'file',\n        }}\n        rules={[{ required: true, message: '请选择文件' }]}\n      />\n    </ModalForm>\n  );\n};\n\nconst OssManage: React.FC = () => {\n  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);\n  const [detailModalVisible, setDetailModalVisible] = useState<boolean>(false);\n  const [currentRow, setCurrentRow] = useState<OssVO>();\n  const actionRef = useRef<ActionType>();\n\n  const handleUpload = async (fields: any) => {\n    const hide = message.loading('正在上传...');\n    try {\n      const response = await upload(\n        { \n          type: fields.type,\n          name: '',\n        },\n        {},\n        fields.file[0].originFileObj\n      );\n      hide();\n      \n      if (response.code === 200) {\n        message.success('上传成功!');\n        actionRef.current?.reload();\n        return true;\n      } else {\n        message.error(response.msg || '上传失败');\n        return false;\n      }\n    } catch (error) {\n      hide();\n      message.error('上传失败，请重试!');\n      return false;\n    }\n  };\n\n  const handleDelete = async (ossIds: string) => {\n    const hide = message.loading('正在删除...');\n    try {\n      const response = await remove2({ ossIds });\n      hide();\n      \n      if (response.code === 200) {\n        message.success('删除成功!');\n        actionRef.current?.reload();\n      } else {\n        message.error(response.msg || '删除失败');\n      }\n    } catch (error) {\n      hide();\n      message.error('删除失败，请重试!');\n    }\n  };\n\n  const handleDownload = async (url: string, fileName: string) => {\n    try {\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      message.success('开始下载');\n    } catch (error) {\n      message.error('下载失败，请重试');\n    }\n  };\n\n  // 根据文件后缀返回对应的图标\n  const getFileIcon = (fileSuffix?: string) => {\n    if (!fileSuffix) return <FileOutlined />;\n    \n    const suffix = fileSuffix.toLowerCase();\n    switch (suffix) {\n      case '.xlsx':\n      case '.xls':\n        return <FileExcelOutlined style={{ color: '#52c41a' }} />;\n      case '.jpg':\n      case '.jpeg':\n      case '.png':\n      case '.gif':\n      case '.bmp':\n      case '.webp':\n\n        return <FileImageOutlined style={{ color: '#1890ff' }} />;\n      case '.pdf':\n        return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;\n      case '.doc':\n      case '.docx':\n\n        return <FileWordOutlined style={{ color: '#1890ff' }} />;\n      case '.ppt':\n      case '.pptx':\n        return <FilePptOutlined style={{ color: '#ff7a45' }} />;\n\n      default:\n        return <FileOutlined />;\n    }\n  };\n\n  // 添加业务类型映射函数\n  const getBusinessType = (type?: number) => {\n    const typeMap: Record<number, { text: string; color: string }> = {\n      1: { text: '附件图片', color: 'blue' },\n      2: { text: '附件文件', color: 'cyan' },\n      3: { text: '完成凭证', color: 'green' },\n      4: { text: '用户头像', color: 'purple' },\n      5: { text: '学生证照片', color: 'magenta' },\n      6: { text: '订单申诉凭证', color: 'red' },\n      7: { text: '学校logo', color: 'orange' },\n      8: { text: '身份证图片', color: 'volcano' },\n    };\n\n    if (!type || !typeMap[type]) {\n      return <Tag>未知类型</Tag>;\n    }\n\n    return (\n      <Tag color={typeMap[type].color} style={{ border: '1px solid' }}>\n        {typeMap[type].text}\n      </Tag>\n    );\n  };\n\n  const columns: ProColumns<OssVO>[] = [\n    {\n      title: 'ID',\n      dataIndex: 'ossId',\n      hideInForm: true,\n      ellipsis: true,\n      search: false,\n    },\n    {\n      title: '文件名',\n      dataIndex: 'fileName',\n      ellipsis: true,\n    },\n\n    {\n      title: '原始文件名',\n      dataIndex: 'originalName',\n      ellipsis: true,\n    },\n\n    {\n      title: '文件后缀',\n      dataIndex: 'fileSuffix',\n      render: (_, record) => (\n        <span>\n          {getFileIcon(record.fileSuffix)}\n          <span style={{ marginLeft: 8 }}>{record.fileSuffix}</span>\n        </span>\n      ),\n    },\n    {\n      title: 'URL',\n      dataIndex: 'url',\n      ellipsis: true,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      valueType: 'dateTime',\n      hideInTable: true,\n      search: false,\n    },\n    {\n      title: '创建人ID',\n      dataIndex: 'createId',\n      hideInTable: true,\n      search: false,\n\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updateTime',\n      valueType: 'dateTime',\n      hideInTable: true,\n      search: false,\n\n    },\n    {\n      title: '更新人ID',\n      dataIndex: 'updateId',\n      hideInTable: true,\n      search: false,\n    },\n    {\n      title: '业务类型',\n      dataIndex: 'type',\n      render: (_, record) => getBusinessType(record.type),\n      valueEnum: {\n        1: { text: '附件图片' },\n        2: { text: '附件文件' },\n        3: { text: '完成凭证' },\n        4: { text: '用户头像' },\n        5: { text: '学生证照片' },\n        6: { text: '订单申诉凭证' },\n        7: { text: '学校logo' },\n        8: { text: '身份证图片' },\n      },\n    },\n    {\n      title: '操作',\n      dataIndex: 'option',\n      valueType: 'option',\n      render: (_, record) => [\n        <a\n          key=\"detail\"\n          onClick={() => {\n            setCurrentRow(record);\n            setDetailModalVisible(true);\n          }}\n        >\n          详情\n        </a>,\n        <a\n          key=\"download\"\n          onClick={() => handleDownload(record.url!, record.fileName!)}\n        >\n          下载\n        </a>,\n        <a\n          key=\"delete\"\n          style={{ color: '#ff4d4f' }}\n          onClick={() => {\n            Modal.confirm({\n              title: '确认删除',\n              content: '确定要删除这个文件吗？',\n              onOk: () => handleDelete(record.ossId!),\n            });\n          }}\n        >\n          删除\n        </a>,\n      ],\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <ProTable<OssVO>\n        headerTitle=\"OSS文件列表\"\n        actionRef={actionRef}\n        rowKey=\"ossId\"\n        search={{\n          labelWidth: 120,\n        }}\n        toolBarRender={() => [\n          <Button\n            type=\"primary\"\n            key=\"primary\"\n            onClick={() => setCreateModalVisible(true)}\n          >\n            <PlusOutlined /> 上传\n          </Button>,\n        ]}\n        request={async (params) => {\n          try {\n            const response = await list1({\n              ...params,\n              pageNum: params.current,\n              pageSize: params.pageSize,\n            });\n            \n            if (response.code === 200) {\n              const rows = response.rows?.map(row => ({\n                ...row,\n                ossId: row.ossId?.toString()\n              })) || [];\n              return {\n                data: rows,\n                success: true,\n                total: response.total || 0,\n              };\n            } else {\n              message.error(response.msg || '获取数据失败');\n              return {\n                data: [],\n                success: false,\n                total: 0,\n              };\n            }\n          } catch (error) {\n            message.error('获取数据失败，请重试');\n            return {\n              data: [],\n              success: false,\n              total: 0,\n            };\n          }\n        }}\n        columns={columns}\n      />\n\n      <OssForm\n        visible={createModalVisible}\n        onVisibleChange={setCreateModalVisible}\n        onFinish={async (value) => {\n          const success = await handleUpload(value);\n          if (success) {\n            setCreateModalVisible(false);\n          }\n        }}\n      />\n\n      <Modal\n        title=\"文件详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        {currentRow && (\n          <div>\n            <p><strong>文件ID：</strong> {currentRow.ossId}</p>\n            <p><strong>文件名：</strong> {currentRow.fileName}</p>\n            <p><strong>原始文件名：</strong> {currentRow.originalName}</p>\n            <p>\n              <strong>文件类型：</strong> \n              {getFileIcon(currentRow.fileSuffix)}\n              <span style={{ marginLeft: 8 }}>{currentRow.fileSuffix}</span>\n            </p>\n            <p><strong>文件大小：</strong> {currentRow.size}</p>\n            <p style={{ wordBreak: 'break-all' }}>\n              <strong>URL：</strong> \n              <a \n                onClick={() => handleDownload(currentRow.url!, currentRow.fileName!)}\n                style={{ cursor: 'pointer' }}\n              >\n                {currentRow.url}\n              </a>\n            </p>\n            <p><strong>创建时间：</strong> {currentRow.createTime}</p>\n            <p><strong>创建人ID：</strong> {currentRow.createId}</p>\n            <p><strong>更新时间：</strong> {currentRow.updateTime}</p>\n            <p><strong>更新人ID：</strong> {currentRow.updateId}</p>\n            <p>\n              <strong>业务类型：</strong> \n              {getBusinessType(currentRow.type)}\n            </p>\n          </div>\n        )}\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default OssManage;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/oss/manage/index.tsx"}, "33": {"path": "/oss/config", "name": "oss-config", "perms": "oss:config:view", "access": "routeFilter", "file": "@/pages/oss/config/index.tsx", "parentId": "31", "id": "33", "absPath": "/oss/config", "__content": "import { PlusOutlined } from '@ant-design/icons';\nimport { <PERSON><PERSON>ontainer, ProTable } from '@ant-design/pro-components';\nimport { Button, message, Modal, Switch, Form, Input, Space } from 'antd';\nimport React, { useRef, useState } from 'react';\nimport type { ActionType, ProColumns } from '@ant-design/pro-components';\nimport { ModalForm, ProFormText, ProFormTextArea } from '@ant-design/pro-components';\nimport {\n  add,\n  edit1,\n  remove3,\n  list2,\n  changeStatus,\n} from '@/services/test-swagger/ossConfigController';\n\ninterface OssConfigVO {\n  ossConfigId: number;\n  configKey: string;\n  accessKey: string;\n  secretKey: string;\n  bucketName: string;\n  prefix: string;\n  endpoint: string;\n  domain: string;\n  isHttps: string;\n  region: string;\n  status: number;\n  ext1: string;\n  remark: string;\n  accessPolicy: string;\n  createTime: string;\n  updateTime: string;\n}\n\nconst OssConfigPage: React.FC = () => {\n  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);\n  const [updateModalVisible, setUpdateModalVisible] = useState<boolean>(false);\n  const [currentRow, setCurrentRow] = useState<OssConfigVO>();\n  const actionRef = useRef<ActionType>();\n  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);\n\n  const handleAdd = async (fields: OssConfigVO) => {\n    const hide = message.loading('正在添加...');\n    try {\n      await add(fields);\n      hide();\n      message.success('添加成功!');\n      actionRef.current?.reload();\n      return true;\n    } catch (error) {\n      hide();\n      message.error('添加失败，请重试!');\n      return false;\n    }\n  };\n\n  const handleUpdate = async (fields: OssConfigVO) => {\n    const hide = message.loading('正在更新...');\n    try {\n      await edit1({\n        ...fields,\n        ossConfigId: currentRow?.ossConfigId,\n      });\n      hide();\n      message.success('更新成功!');\n      actionRef.current?.reload();\n      return true;\n    } catch (error) {\n      hide();\n      message.error('更新失败，请重试!');\n      return false;\n    }\n  };\n\n  const handleDelete = async (ossConfigId: number) => {\n    const hide = message.loading('正在删除...');\n    try {\n      await remove3({ ossConfigIds: [ossConfigId] });\n      hide();\n      message.success('删除成功!');\n      actionRef.current?.reload();\n    } catch (error) {\n      hide();\n      message.error('删除失败，请重试!');\n    }\n  };\n\n  const handleBatchDelete = async () => {\n    if (!selectedRowKeys.length) {\n      message.warning('请选择要删除的配置');\n      return;\n    }\n    Modal.confirm({\n      title: '批量删除',\n      content: `确定要删除选中的 ${selectedRowKeys.length} 个配置吗？`,\n      onOk: async () => {\n        await remove3({ ossConfigIds: selectedRowKeys.map(key => parseInt(key)) });\n        setSelectedRowKeys([]);\n      },\n    });\n  };\n\n  const handleStatusChange = async (record: OssConfigVO) => {\n    const hide = message.loading('正在修改状态...');\n    try {\n      await changeStatus({\n        ...record,\n        status: record.status === 0 ? 1 : 0,\n      });\n      hide();\n      message.success('状态修改成功!');\n      actionRef.current?.reload();\n    } catch (error) {\n      hide();\n      message.error('状态修改失败，请重试!');\n    }\n  };\n\n  const columns: ProColumns<OssConfigVO>[] = [\n    {\n      title: '配置ID',\n      dataIndex: 'ossConfigId',\n      hideInForm: true,\n      search: false,\n      ellipsis: true,\n    },\n    {\n      title: '配置名称',\n      dataIndex: 'configKey',\n      ellipsis: true,\n    },\n    {\n      title: '配置值',\n      dataIndex: 'configValue',\n      ellipsis: true,\n      search: false,\n    },\n    {\n      title: '备注',\n      dataIndex: 'remark',\n      ellipsis: true,\n      search: false,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      valueEnum: {\n        0: { text: '停用', status: 'Error' },\n        1: { text: '正常', status: 'Success' },\n      },\n      render: (_, record) => (\n        <Switch\n          checked={record.status === 1}\n          onChange={() => handleStatusChange(record)}\n          checkedChildren=\"启用\"\n          unCheckedChildren=\"停用\"\n        />\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createTime',\n      valueType: 'dateTime',\n      search: false,\n      ellipsis: true,\n    },\n    {\n      title: '操作',\n      dataIndex: 'option',\n      valueType: 'option',\n      render: (_, record) => [\n        <a\n          key=\"edit\"\n          onClick={() => {\n            setCurrentRow(record);\n            setUpdateModalVisible(true);\n          }}\n        >\n          编辑\n        </a>,\n        <a\n          key=\"delete\"\n          onClick={() => {\n            Modal.confirm({\n              title: '确认删除',\n              content: '确定要删除该配置吗？',\n              onOk: () => handleDelete(record.ossConfigId),\n            });\n          }}\n        >\n          删除\n        </a>,\n      ],\n    },\n  ];\n\n  const ConfigForm: React.FC<{\n    visible: boolean;\n    onVisibleChange: (visible: boolean) => void;\n    onFinish: (values: any) => Promise<boolean>;\n    values?: Partial<OssConfigVO>;\n  }> = (props) => {\n    return (\n      <ModalForm\n        title={props.values ? '编辑OSS配置' : '新建OSS配置'}\n        visible={props.visible}\n        onVisibleChange={props.onVisibleChange}\n        onFinish={props.onFinish}\n        initialValues={props.values}\n        width={600}\n      >\n        <ProFormText\n          name=\"configKey\"\n          label=\"配置名称\"\n          rules={[{ required: true, message: '请输入配置名称' }]}\n        />\n        <ProFormText\n          name=\"accessKey\"\n          label=\"AccessKey\"\n          rules={[{ required: true, message: '请输入AccessKey' }]}\n        />\n        <ProFormText\n          name=\"secretKey\"\n          label=\"SecretKey\"\n          rules={[{ required: true, message: '请输入SecretKey' }]}\n        />\n        <ProFormText\n          name=\"bucketName\"\n          label=\"存储桶名称\"\n          rules={[{ required: true, message: '请输入存储桶名称' }]}\n        />\n        <ProFormText name=\"prefix\" label=\"前缀\" />\n        <ProFormText\n          name=\"endpoint\"\n          label=\"访问站点\"\n          rules={[{ required: true, message: '请输入访问站点' }]}\n        />\n        <ProFormText name=\"domain\" label=\"自定义域名\" />\n        <ProFormText\n          name=\"isHttps\"\n          label=\"是否HTTPS\"\n          rules={[{ required: true, message: '请选择是否启用HTTPS' }]}\n        />\n        <ProFormText name=\"region\" label=\"区域\" />\n        <ProFormText\n          name=\"accessPolicy\"\n          label=\"访问权限\"\n          rules={[{ required: true, message: '请选择访问权限' }]}\n        />\n        <ProFormTextArea\n          name=\"remark\"\n          label=\"备注\"\n          placeholder=\"请输入备注信息\"\n        />\n      </ModalForm>\n    );\n  };\n\n  return (\n    <PageContainer>\n      <ProTable<OssConfigVO>\n        headerTitle=\"OSS配置列表\"\n        actionRef={actionRef}\n        rowKey=\"ossConfigId\"\n        search={{\n          labelWidth: 120,\n        }}\n        rowSelection={{\n          selectedRowKeys,\n          onChange: (keys) => setSelectedRowKeys(keys as string[]),\n        }}\n        toolBarRender={() => [\n          <Button\n            key=\"delete\"\n            danger\n            onClick={handleBatchDelete}\n            disabled={!selectedRowKeys.length}\n          >\n            批量删除\n          </Button>,\n          <Button\n            type=\"primary\"\n            key=\"add\"\n            onClick={() => setCreateModalVisible(true)}\n          >\n            <PlusOutlined /> 新建\n          </Button>,\n        ]}\n        request={async (params) => {\n          const msg = await list2({\n            ...params,\n            current: params.current,\n            pageSize: params.pageSize,\n          });\n          return {\n            data: msg.rows || [],\n            success: true,\n            total: msg.total || 0,\n          };\n        }}\n        columns={columns}\n      />\n\n      <ConfigForm\n        visible={createModalVisible}\n        onVisibleChange={setCreateModalVisible}\n        onFinish={async (value) => {\n          const success = await handleAdd(value as OssConfigVO);\n          if (success) {\n            setCreateModalVisible(false);\n          }\n          return success;\n        }}\n      />\n\n      <ConfigForm\n        visible={updateModalVisible}\n        onVisibleChange={setUpdateModalVisible}\n        values={currentRow}\n        onFinish={async (value) => {\n          const success = await handleUpdate(value as OssConfigVO);\n          if (success) {\n            setUpdateModalVisible(false);\n          }\n          return success;\n        }}\n      />\n    </PageContainer>\n  );\n};\n\nexport default OssConfigPage;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/oss/config/index.tsx"}, "34": {"name": "个人中心", "icon": "star", "path": "/profile", "hideInMenu": true, "file": "@/pages/User/profile/index.tsx", "parentId": "ant-design-pro-layout", "id": "34", "absPath": "/profile", "__content": "import { Card, Descriptions, Avatar, Tabs, Tag, Switch, Button, Input, message, Form, Modal, Upload } from 'antd';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { useModel } from 'umi';\nimport { useState } from 'react';\nimport { updateProfile, updatePwd, sendEmailCode, bindEmail } from '@/services/test-swagger/profileController';\nimport { upload } from '@/services/test-swagger/ossController';\nimport { CameraOutlined } from '@ant-design/icons';\nimport styles from './index.less';\n\nconst Profile: React.FC = () => {\n  const { initialState, setInitialState } = useModel('@@initialState');\n  const userInfo = initialState?.currentUser?.user;\n  const userPc = userInfo?.userPc;\n  const [emailEnable, setEmailEnable] = useState(userPc?.emailEnable || false);\n  const [isPasswordModalVisible, setIsPasswordModalVisible] = useState(false);\n  const [isEmailModalVisible, setIsEmailModalVisible] = useState(false);\n  const [emailForm] = Form.useForm();\n  const [passwordForm] = Form.useForm();\n  const [countdown, setCountdown] = useState(0);\n  const [avatarUrl, setAvatarUrl] = useState(userPc?.avatar);\n\n  // 格式化性别显示\n  const formatSex = (sex: number) => {\n    switch (sex) {\n      case 1:\n        return '男';\n      case 2:\n        return '女';\n      default:\n        return '未知';\n    }\n  };\n\n  // 格式化状态显示\n  const formatStatus = (status: number) => {\n    switch (status) {\n      case 1:\n        return { text: '正常', color: 'green' };\n      case 0:\n        return { text: '禁用', color: 'red' };\n      default:\n        return { text: '未知', color: 'default' };\n    }\n  };\n\n  // 处理邮箱启用状态变化\n  const handleEmailEnableChange = (checked: boolean) => {\n    setEmailEnable(checked);\n  };\n\n  // 统一的错误处理函数\n  const handleResponse = (response: any, successMsg: string) => {\n    if (response?.code === 200) {\n      message.success(successMsg);\n      return true;\n    } else {\n      message.error(response?.msg || '操作失败');\n      return false;\n    }\n  };\n\n  // 处理发送验证码\n  const handleSendCode = async () => {\n    try {\n      const email = emailForm.getFieldValue('email');\n      if (!email) {\n        message.error('请输入邮箱');\n        return;\n      }\n      const response = await sendEmailCode({ email });\n      if (handleResponse(response, '验证码已发送')) {\n        setCountdown(60);\n        const timer = setInterval(() => {\n          setCountdown((prev) => {\n            if (prev <= 1) {\n              clearInterval(timer);\n              return 0;\n            }\n            return prev - 1;\n          });\n        }, 1000);\n      }\n    } catch (error) {\n      message.error('发送验证码失败');\n    }\n  };\n\n  // 重新加载用户信息\n  const reloadUserInfo = async () => {\n    const fetchUserInfo = initialState?.fetchUserInfo;\n    if (fetchUserInfo) {\n      const currentUser = await fetchUserInfo();\n      setInitialState((s) => ({\n        ...s,\n        currentUser,\n      }));\n    }\n  };\n\n  // 处理保存邮箱\n  const handleEmailSave = async () => {\n    try {\n      const values = await emailForm.validateFields();\n      const response = await bindEmail(values.email, values.emailCode);\n      if (handleResponse(response, '邮箱修改成功')) {\n        setIsEmailModalVisible(false);\n        emailForm.resetFields();\n        await reloadUserInfo();  // 重新加载用户信息\n      }\n    } catch (error) {\n      message.error('邮箱修改失败');\n    }\n  };\n\n  // 处理头像上传\n  const handleAvatarUpload = async (file: File) => {\n    try {\n      const response = await upload(\n        { type: 4 },  // 请求参数\n        { name: '' }, // 请求体参数\n        file,\n      );\n      \n      if (response?.code === 200 && response.data) {\n        const url = response.data.url;  // 假设返回的数据中有 url 字段\n        setAvatarUrl(url);\n        // 立即更新头像\n        const profileResponse = await updateProfile({\n          nickname: userPc?.name,\n          avatar: url,\n          emailEnable: emailEnable ? 1 : 0,\n        });\n        if (handleResponse(profileResponse, '头像更新成功')) {\n          await reloadUserInfo();\n        }\n      } else {\n        message.error(response?.msg || '上传失败');\n      }\n    } catch (error) {\n      message.error('上传失败');\n    }\n  };\n\n  // 处理保存基本信息\n  const handleSaveProfile = async () => {\n    try {\n      const response = await updateProfile({\n        nickname: userPc?.name,\n        avatar: avatarUrl || userPc?.avatar,  // 使用最新的头像URL\n        emailEnable: emailEnable ? 1 : 0,\n      });\n      if (handleResponse(response, '保存成功')) {\n        await reloadUserInfo();\n      }\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n\n  // 处理修改密码\n  const handlePasswordChange = async () => {\n    try {\n      const values = await passwordForm.validateFields();\n      const response = await updatePwd(values);\n      if (handleResponse(response, '密码修改成功')) {\n        setIsPasswordModalVisible(false);\n        passwordForm.resetFields();\n      }\n    } catch (error) {\n      message.error('密码修改失败');\n    }\n  };\n\n  const items = [\n    {\n      key: '1',\n      label: '基本信息',\n      children: (\n        <>\n          <Descriptions column={2}>\n            <Descriptions.Item label=\"用户ID\">{userPc?.id}</Descriptions.Item>\n            <Descriptions.Item label=\"用户名\">{userPc?.username}</Descriptions.Item>\n            <Descriptions.Item label=\"姓名\">{userPc?.name}</Descriptions.Item>\n            <Descriptions.Item label=\"手机号\">{userPc?.phone}</Descriptions.Item>\n            <Descriptions.Item label=\"邮箱\">\n              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n                <span>{userPc?.email || '未设置'}</span>\n                <Button \n                  type=\"link\" \n                  onClick={() => {\n                    setIsEmailModalVisible(true);\n                    emailForm.setFieldsValue({\n                      email: userPc?.email || '',\n                    });\n                  }}\n                  style={{ padding: '0 4px', height: 'auto', lineHeight: '1.5' }}\n                >\n                  {userPc?.email ? '更换邮箱' : '绑定邮箱'}\n                </Button>\n              </div>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"性别\">{formatSex(userPc?.sex)}</Descriptions.Item>\n            <Descriptions.Item label=\"状态\">\n              {userPc?.status !== undefined && (\n                <Tag color={formatStatus(userPc.status).color}>\n                  {formatStatus(userPc.status).text}\n                </Tag>\n              )}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"创建时间\">{userInfo?.createTime}</Descriptions.Item>\n          </Descriptions>\n        </>\n      ),\n    },\n    {\n      key: '2',\n      label: '上次登录',\n      children: (\n        <Descriptions column={2}>\n          <Descriptions.Item label=\"登录时间\">{userInfo?.loginTime}</Descriptions.Item>\n          <Descriptions.Item label=\"登录IP\">{userInfo?.loginIp}</Descriptions.Item>\n          <Descriptions.Item label=\"登录地区\">{userInfo?.loginRegion}</Descriptions.Item>\n          <Descriptions.Item label=\"设备类型\">\n            {userInfo?.deviceType === 0 ? 'PC端' : '移动端'}\n          </Descriptions.Item>\n        </Descriptions>\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <Card className={styles.profileCard}>\n        <div className={styles.userInfoHeader}>\n          <div className={styles.leftSection}>\n            <div className={styles.avatarContainer}>\n              <Upload\n                accept=\"image/*\"\n                showUploadList={false}\n                beforeUpload={(file) => {\n                  handleAvatarUpload(file);\n                  return false;  // 阻止自动上传\n                }}\n              >\n                <div className={styles.avatarWrapper}>\n                  <Avatar size={80} src={avatarUrl || userPc?.avatar} />\n                  <div className={styles.avatarMask}>\n                    <CameraOutlined />\n                    <span>更换头像</span>\n                  </div>\n                </div>\n              </Upload>\n            </div>\n            <div className={styles.userMeta}>\n              <h2>{userPc?.name}</h2>\n              <p>{userPc?.username} {userInfo?.admin ? '(管理员)' : ''}</p>\n            </div>\n          </div>\n          <div className={styles.rightSection}>\n            {userPc?.email && (\n              <div >\n                <Switch\n                  checked={emailEnable}\n                  onChange={handleEmailEnableChange}\n                  checkedChildren=\"启用邮箱\"\n                  unCheckedChildren=\"禁用邮箱\"\n                />\n              </div>\n            )}\n            <Button onClick={() => setIsPasswordModalVisible(true)}>\n              修改密码\n            </Button>\n            <Button type=\"primary\" onClick={handleSaveProfile}>\n              保存修改\n            </Button>\n          </div>\n        </div>\n        <Tabs items={items} />\n      </Card>\n\n      {/* 邮箱设置模态框 */}\n      <Modal\n        title={userPc?.email ? '更换邮箱' : '绑定邮箱'}\n        open={isEmailModalVisible}\n        onOk={handleEmailSave}\n        onCancel={() => {\n          setIsEmailModalVisible(false);\n          emailForm.resetFields();\n        }}\n      >\n        <Form form={emailForm} layout=\"vertical\">\n          <Form.Item\n            name=\"email\"\n            label=\"邮箱地址\"\n            rules={[\n              { required: true, message: '请输入邮箱地址' },\n              { type: 'email', message: '请输入正确的邮箱格式' }\n            ]}\n          >\n            <Input />\n          </Form.Item>\n          <Form.Item\n            name=\"emailCode\"\n            label=\"验证码\"\n            rules={[{ required: true, message: '请输入验证码' }]}\n          >\n            <div style={{ display: 'flex', gap: 8 }}>\n              <Input style={{ flex: 1 }} />\n              <Button \n                onClick={handleSendCode}\n                disabled={countdown > 0}\n              >\n                {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}\n              </Button>\n            </div>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 修改密码模态框 */}\n      <Modal\n        title=\"修改密码\"\n        open={isPasswordModalVisible}\n        onOk={handlePasswordChange}\n        onCancel={() => {\n          setIsPasswordModalVisible(false);\n          passwordForm.resetFields();\n        }}\n      >\n        <Form form={passwordForm} layout=\"vertical\">\n          <Form.Item\n            name=\"oldPassword\"\n            label=\"原密码\"\n            rules={[{ required: true, message: '请输入原密码' }]}\n          >\n            <Input.Password />\n          </Form.Item>\n          <Form.Item\n            name=\"newPassword\"\n            label=\"新密码\"\n            rules={[{ required: true, message: '请输入新密码' }]}\n          >\n            <Input.Password />\n          </Form.Item>\n          <Form.Item\n            name=\"confirmPassword\"\n            label=\"确认新密码\"\n            dependencies={['newPassword']}\n            rules={[\n              { required: true, message: '请确认新密码' },\n              ({ getFieldValue }) => ({\n                validator(_, value) {\n                  if (!value || getFieldValue('newPassword') === value) {\n                    return Promise.resolve();\n                  }\n                  return Promise.reject(new Error('两次输入的密码不一致'));\n                },\n              }),\n            ]}\n          >\n            <Input.Password />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </PageContainer>\n  );\n};\n\n// 登录日志组件\nconst LoginLogs: React.FC = () => {\n  const columns = [\n    {\n      title: '登录时间',\n      dataIndex: 'loginTime',\n      key: 'loginTime',\n    },\n    {\n      title: '登录IP',\n      dataIndex: 'ip',\n      key: 'ip',\n    },\n    {\n      title: '登录地点',\n      dataIndex: 'location',\n      key: 'location',\n    },\n    {\n      title: '设备类型',\n      dataIndex: 'deviceType',\n      key: 'deviceType',\n    },\n  ];\n\n  // 这里应该从API获取登录日志数据\n  const data = [\n    {\n      key: '1',\n      loginTime: '2024-03-20 14:30:00',\n      ip: '***********',\n      location: '北京市',\n      deviceType: 'PC',\n    },\n  ];\n\n  return <Table columns={columns} dataSource={data} />;\n};\n\nexport default Profile;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/User/profile/index.tsx"}, "35": {"path": "/", "redirect": "/welcome", "parentId": "ant-design-pro-layout", "id": "35", "absPath": "/"}, "36": {"path": "*", "layout": false, "file": "@/pages/404.tsx", "id": "36", "absPath": "/*", "__content": "import { history, useIntl } from '@umijs/max';\nimport { Button, Result } from 'antd';\nimport React from 'react';\n\nconst NoFoundPage: React.FC = () => (\n  <Result\n    status=\"404\"\n    title=\"404\"\n    subTitle={useIntl().formatMessage({ id: 'pages.404.subTitle' })}\n    extra={\n      <Button type=\"primary\" onClick={() => history.push('/')}>\n        {useIntl().formatMessage({ id: 'pages.404.buttonText' })}\n      </Button>\n    }\n  />\n);\n\nexport default NoFoundPage;\n", "__isJSFile": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daida<PERSON>-run-admin/src/pages/404.tsx"}, "ant-design-pro-layout": {"id": "ant-design-pro-layout", "path": "/", "file": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/src/.umi-production/plugin-layout/Layout.tsx", "absPath": "/", "isLayout": true, "__absFile": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/src/.umi-production/plugin-layout/Layout.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "cnpm", "umi": {"version": "4.4.10", "name": "<PERSON><PERSON>", "importSource": "@umijs/max", "cliName": "max"}, "bundleStatus": {"done": false}, "mfsuBundleStatus": {"done": false}, "react": {"version": "18.3.1", "path": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\react"}, "react-dom": {"version": "18.3.1", "path": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\react-dom"}, "appJS": {"path": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\src\\app.tsx", "exports": ["getInitialState", "layout", "request"]}, "locale": "zh-CN", "globalCSS": ["C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\src\\global.less"], "globalJS": ["C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\src\\global.tsx"], "overridesCSS": [], "bundler": "webpack", "framework": "react", "typescript": {"tsVersion": "5.8.3", "tslibVersion": "2.8.1"}, "faviconFiles": [], "antd": {"pkgPath": "C:\\Users\\<USER>\\Desktop\\fuu-run-master\\daidaida-run-admin\\node_modules\\antd", "version": "5.24.8"}, "pluginLayout": {"pkgPath": "C:/Users/<USER>/Desktop/fuu-run-master/daidaida-run-admin/node_modules/@ant-design/pro-components", "version": "2.8.7"}}
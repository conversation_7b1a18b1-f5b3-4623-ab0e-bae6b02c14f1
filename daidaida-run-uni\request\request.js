import {
	tansParams
} from '@/utils/utils.js';
import {checkLogin,xcxLogin,getInfo,login} from "@/request/apis/login.js";
import {getSchool} from "@/request/apis/school.js";

// 生产环境API地址
const base_url = 'https://api.daidaida.xyz'; // 后端API反代域名
export const upload_url = base_url + '/system/oss/upload'; // 上传路径
export const ws_url = 'wss://api.daidaida.xyz/ws'; // WebSocket服务地址

function uploadFile1(filePath, formData) {
	return new Promise((resolve, reject) => {
		let header = {
			'Content-Type': 'multipart/form-data'
		}
		let token = uni.getStorageSync('token')
		if (token) {
			header['Authorization'] = 'Bearer ' + token
		}
		uni.uploadFile({
			url: base_url + '/system/oss/upload', // 直接使用端口号
			filePath: filePath,
			name: 'file',
			header: header, // 设置请求的 header
			timeout: 60000, // 上传超时设置为60秒
			formData: formData, // HTTP 请求中其他额外的 form data
			success: function(res) {
				if (res.statusCode == 200) {
					var json = res.data ? JSON.parse(res.data) : {};
					if (json.code == 200) {
						uni.hideLoading();
						resolve(json);
					} else {
						uni.showToast({
							title: json.msg || '上传失败',
							icon: 'none',
							duration: 1000
						})
						reject(json);
					}

				} else if (res.statusCode == 401) {
					login.call(this)
					reject(res);
				} else {
					uni.showToast({
						title: res.data?.msg || '上传失败',
						icon: 'none',
						duration: 1000
					})
					reject(res.data);
				}
			},
			fail: function(err) {
				console.error('上传失败:', err);
				uni.showToast({
					title: err.errMsg || '网络错误',
					icon: 'none',
					duration: 1000
				})
				reject(err)
			}
		})
	});
}

// 请求函数
function request1(params) {
	let url = params.url;
	let method = params.method || "get";
	let data = params.data || {};
	let param = params.params
	if (method === "get" && param) {
		url = url + "?" + tansParams(param)
	}

	let header = {
		'Content-Type': 'application/json',
		...params.header,
	};

	// 获取本地token
	if (uni.getStorageSync("token")) {
		header['Authorization'] = 'Bearer ' + uni.getStorageSync("token");
	}

	return new Promise((resolve, reject) => {
		if(params.showLoading == true) {
			uni.showLoading({
				title: "加载中..."
			})
		}
		uni.request({
			url: base_url + url,
			method: method,
			header: header,
			data: data,
			timeout: 30000, // 请求超时设置为30秒
			success(response) {
				if(params.showLoading == true) {
					uni.hideLoading();
				}
				console.log(response);
				const res = response.data
				// 根据返回的状态码做出对应的操作
				if (res.code == 200) {
					console.log(222);
					resolve(res);
				} else {
					switch (res.code) {
						case 401:
							console.log(666);
							login.call(this)
							break;
						case 404:
							uni.showToast({
								title: '请求地址不存在...',
								duration: 2000,
							})
							break;
						case 500:
							console.log(111)
							reject(res.msg);
							break
						default:
							// uni.showToast({
							// 	title: res.msg,
							// 	duration: 2000,
							// 	icon: "none"
							// })
							break;
					}
				}
			},
			fail(err) {
				if(params.showLoading == true) {
					uni.hideLoading();
				}
				console.error('请求失败:', err)
				reject(err);
			}
		});
	});
};


export const request = request1

export const uploadFile = uploadFile1
// module.exports = {
// 	request:request,
// 	uploadFile:uploadFile
// }
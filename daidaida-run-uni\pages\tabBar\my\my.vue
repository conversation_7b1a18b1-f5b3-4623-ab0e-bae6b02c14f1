<template>
  <view class="my-page">
    <!-- 个人信息卡片 -->
    <view @click="toProfile" class="profile-card">
      <view class="profile-info">
      <view class="nickname">
        {{userInfo.userWx.nickname}}
          <nut-icon size="12px" name="rect-right" color="#4F7DF5"></nut-icon>
        </view>
        <view class="user-time">{{userInfo.comesTime}}</view>
    </view>
    <image class="avatar" :src="userInfo.userWx.avatar" />
  </view>

    <!-- 功能菜单列表 -->
    <view class="menu-list">
      <!-- 跑腿中心 -->
      <view @click="toCenter" v-if="userInfo?.userWx?.isRunner === 1" class="menu-item">
        <view class="menu-left">
          <view class="icon-wrapper" style="background-color: rgba(79, 125, 245, 0.1);">
            <image class="menu-icon" src="/static/profile/跑腿中心.png" mode=""></image>
    </view>
          <text class="menu-title">跑腿中心</text>
    </view>
        <nut-icon name="right" size="16" color="#CCCCCC"></nut-icon>
  </view>

      <!-- 申请跑腿 -->
      <view @click="checkSchool" class="menu-item">
        <view class="menu-left">
          <view class="icon-wrapper" style="background-color: rgba(66, 185, 131, 0.1);">
            <image class="menu-icon" src="/static/profile/注册.png" mode=""></image>
    </view>
          <text class="menu-title">申请跑腿</text>
    </view>
        <nut-icon name="right" size="16" color="#CCCCCC"></nut-icon>
  </view>

      <!-- 我的订单 -->
      <view @click="toOrderList" class="menu-item">
        <view class="menu-left">
          <view class="icon-wrapper" style="background-color: rgba(255, 170, 0, 0.1);">
            <image class="menu-icon" src="/static/profile/订单.png" mode=""></image>
    </view>
          <text class="menu-title">我的订单</text>
    </view>
        <nut-icon name="right" size="16" color="#CCCCCC"></nut-icon>
  </view>

      <!-- 我的地址 -->
      <view @click="toAddressList" class="menu-item">
        <view class="menu-left">
          <view class="icon-wrapper" style="background-color: rgba(255, 107, 107, 0.1);">
            <image class="menu-icon" src="/static/profile/地址.png" mode=""></image>
    </view>
          <text class="menu-title">我的地址</text>
    </view>
        <nut-icon name="right" size="16" color="#CCCCCC"></nut-icon>
  </view>

      <!-- 消息订阅设置 -->
      <view @click="openSubscriptionSettings" class="menu-item">
        <view class="menu-left">
          <view class="icon-wrapper" style="background-color: rgba(79, 192, 245, 0.1);">
            <image class="menu-icon" src="/static/profile/消息.png" mode=""></image>
          </view>
          <text class="menu-title">消息通知</text>
        </view>
        <nut-icon name="right" size="16" color="#CCCCCC"></nut-icon>
      </view>

      <!-- 咨询客服 -->
      <button open-type="contact" class="contact-button">
        <view class="menu-item">
          <view class="menu-left">
            <view class="icon-wrapper" style="background-color: rgba(79, 125, 245, 0.1);">
              <image class="menu-icon" src="/static/profile/客服.png" mode=""></image>
		  </view>
            <text class="menu-title">咨询客服</text>
		  </view>
          <nut-icon name="right" size="16" color="#CCCCCC"></nut-icon>
		</view>
	</button>

      <!-- 设置 -->
      <view @click="toSetting" class="menu-item">
        <view class="menu-left">
          <view class="icon-wrapper" style="background-color: rgba(153, 153, 153, 0.1);">
            <image class="menu-icon" src="/static/profile/设置.png" mode=""></image>
    </view>
          <text class="menu-title">设置</text>
    </view>
        <nut-icon name="right" size="16" color="#CCCCCC"></nut-icon>
    </view>
    </view>
  
    <!-- 未选择校区弹窗 -->
  <nut-dialog 
    title="未选择校区" 
    content="是否前往选择校区？" 
    v-model:visible="visible1" 
    @ok="toSelectSchool" 
  />
  
  <!-- 消息订阅设置弹窗 -->
  <nut-dialog
    title="消息通知设置"
    content="允许接收订单状态变更、补差价等重要信息的通知"
    v-model:visible="showSubscribeDialog"
    @cancel="showSubscribeDialog = false"
    @ok="requestSubscription"
    cancel-text="取消"
    ok-text="立即开启"
  />
  </view>
</template>

<script>
	import dayjs from 'dayjs';
	import relativeTime from 'dayjs/plugin/relativeTime'; // 引入 relativeTime 插件
	import 'dayjs/locale/zh-cn';  // 导入中文语言包
	// 扩展 dayjs 插件
	dayjs.extend(relativeTime);
	// 设置为中文
	dayjs.locale('zh-cn');
	export default {
		data() {
			return {
				title: 'Hello',
				userInfo:{
					userWx:{}
				},
				visible1:false, //检查有没有选择校区dialog
				showSubscribeDialog: false,
				// 订阅消息模板ID
				ORDER_STATUS_TEMPLATE_ID: 'j8Z2M2fdVyAAMsGcJXPWAXSfu3YxexvNMLM_1Dtp9-c',
				MESSAGE_TEMPLATE_ID: 'nR0D0mjOJmS74O1eGsI0oncXXRcQg0lSbSSrucYQMNA',
				PRICE_ADJUSTMENT_TEMPLATE_ID: 'p0THQpfjhGnJguDx1XPKlMfZLCqAMRJC3OflL6WLYws',
			}
		},
		onLoad() {

		},
		onShow() {
			this.initData()
		},	
		methods: {
			toSetting(){
				uni.navigateTo({
					url:"/pages/API/user/setting/setting"
				})
			},
			toCenter() {
				uni.navigateTo({
					url:"/pages/API/runner/center/center"
				})
			},
			toOrderList() {
				uni.navigateTo({
					url:"/pages/API/order/list/list"
				})
			},
			toAddressList() {
				uni.navigateTo({
					url:"/pages/API/address/list/list"
				})
			},
			toSelectSchool() {
				uni.navigateTo({
					url:"/pages/API/school/select/select"
				})
			},
			toRunnerIntro() {
				uni.navigateTo({
					url:"/pages/API/runner/introduce/introduce"
				})
			},
			checkSchool() {
				let school = this.$store.state.currSchool;
				if(school == null || school == undefined || school == '') {
					this.visible1 = true
				}
				else {
					this.toRunnerIntro()
				}
			},
			toProfile(){	
				uni.navigateTo({
					url:"/pages/API/user/profile/profile"
				})
			},
			initData(){
				this.userInfo = this.$store.state.userInfo;
				this.userInfo.comesTime = this.getRegisterTimeMessage(this.userInfo.createTime)
				console.log(this.userInfo);
				console.log(this.userInfo);
			},
			getRegisterTimeMessage(createTime) {
			  return dayjs().diff(dayjs(createTime), 'day') < 1 
			    ? '欢迎使用' 
			    : `您已经使用 ${dayjs(createTime).toNow(true)}啦！`;
			},
			openSubscriptionSettings() {
				this.showSubscribeDialog = true;
			},
			requestSubscription() {
				// 检查是否支持订阅消息功能
				if (wx.requestSubscribeMessage) {
					wx.requestSubscribeMessage({
						tmplIds: [
							this.ORDER_STATUS_TEMPLATE_ID, 
							this.MESSAGE_TEMPLATE_ID, 
							this.PRICE_ADJUSTMENT_TEMPLATE_ID
						],
						success: (res) => {
							console.log('订阅结果:', res);
							
							// 统计接受的模板
							let acceptedTemplates = [];
							for (let tmplId in res) {
								if (res[tmplId] === 'accept') {
									acceptedTemplates.push(tmplId);
								}
							}
							
							// 如果有接受的模板，向后端报告
							if (acceptedTemplates.length > 0) {
								this.reportSubscription(acceptedTemplates);
								uni.showToast({
									title: '订阅成功，将为您推送重要通知',
									icon: 'none',
									duration: 2000
								});
							} else {
								uni.showToast({
									title: '您已拒绝接收通知，将无法收到重要信息',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (err) => {
							console.log('订阅请求失败:', err);
							
							// 检查是否是主开关被关闭
							if (err.errCode === 20004) {
								uni.showModal({
									title: '通知权限已关闭',
									content: '请在小程序设置中打开"接收订阅消息"开关',
									showCancel: true,
									confirmText: '去设置',
									success: (res) => {
										if (res.confirm) {
											// 跳转到小程序设置页
											wx.openSetting({
												success: (settingRes) => {
													console.log('设置结果:', settingRes);
												}
											});
										}
									}
								});
							} else {
								uni.showToast({
									title: '订阅失败，请稍后重试',
									icon: 'none',
									duration: 2000
								});
							}
						}
					});
				} else {
					uni.showToast({
						title: '当前微信版本不支持订阅消息，请升级微信',
						icon: 'none',
						duration: 2000
					});
				}
				this.showSubscribeDialog = false;
			},
			
			// 向后端报告订阅状态
			reportSubscription(templateIds) {
				let baseUrl = this.$baseUrl;
				if (!baseUrl) {
					baseUrl = 'https://api.daidaida.xyz';
				}
				
				const token = uni.getStorageSync('token');
				
				templateIds.forEach(templateId => {
					uni.request({
						url: baseUrl + '/wx/subscription/record',
						method: 'POST',
						header: {
							'Authorization': 'Bearer ' + token,
							'Content-Type': 'application/x-www-form-urlencoded'
						},
						data: 'templateId=' + templateId,
						success: function(res) {
							console.log('订阅状态记录成功:', res);
						},
						fail: function(err) {
							console.error('订阅状态记录失败:', err);
						}
					});
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
/* 基础样式设置 */
page {
  background-color: #f7f9ff;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.my-page {
  padding: 32rpx;
  box-sizing: border-box;
}

/* 个人信息卡片 */
.profile-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #4F7DF5, #6C8FF8);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 30rpx rgba(79, 125, 245, 0.2);
  color: #fff;

  .profile-info {
  flex: 1;
    
    .nickname {
  display: flex;
  align-items: center;
      font-size: 40rpx;
      font-weight: 600;
      margin-bottom: 16rpx;
      
      .nut-icon {
        margin-left: 12rpx;
      }
    }
    
    .user-time {
      font-size: 26rpx;
      opacity: 0.8;
}
  }
  
  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    border: 4rpx solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
}

/* 功能菜单列表 */
.menu-list {
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  
  .menu-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1px solid #f0f2f5;
    transition: all 0.3s;
    
    &:last-child {
      border-bottom: none;
}
    
    &:active {
      background-color: #f7f8fa;
}
    
    .menu-left {
      display: flex;
      align-items: center;
      
      .icon-wrapper {
        width: 80rpx;
        height: 80rpx;
        border-radius: 16rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 24rpx;
      }
      
      .menu-icon {
        width: 40rpx;
        height: 40rpx;
      }
      
      .menu-title {
        font-size: 30rpx;
        color: #333;
        font-weight: 500;
}
    }
  }
}

/* 联系按钮样式重置 */
.contact-button {
  padding: 0;
  margin: 0;
  line-height: 1;
  background-color: transparent;
  width: 100%;
  text-align: left;
  border-radius: 0;

  &::after {
    border: none;
  }
}
</style>

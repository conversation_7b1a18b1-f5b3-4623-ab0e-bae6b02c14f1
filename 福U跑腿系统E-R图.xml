<?xml version="1.0" encoding="UTF-8"?>
<!-- 福U跑腿系统E-R图 - 可导入Visio的XML格式 -->
<VisioDocument xmlns="http://schemas.microsoft.com/visio/2003/core" 
               xmlns:vx="http://schemas.microsoft.com/visio/2006/extension">
  <DocumentProperties>
    <Title>福U跑腿系统数据库E-R图</Title>
    <Subject>数据库实体关系图</Subject>
    <Creator>系统生成</Creator>
    <Description>福U跑腿系统完整的数据库实体关系图，包含所有表结构和关系</Description>
  </DocumentProperties>
  
  <!-- 实体定义 -->
  <Entities>
    <!-- 用户相关实体 -->
    <Entity name="USER" type="主实体">
      <Attributes>
        <Attribute name="uid" type="bigint" key="PK" description="全局用户ID"/>
        <Attribute name="device_type" type="tinyint" description="设备类型"/>
        <Attribute name="create_time" type="datetime" description="创建时间"/>
        <Attribute name="login_time" type="datetime" description="登录时间"/>
        <Attribute name="login_ip" type="varchar(20)" description="登录IP"/>
        <Attribute name="login_region" type="varchar(50)" description="登录地区"/>
        <Attribute name="user_type" type="int" description="用户类型"/>
        <Attribute name="create_id" type="bigint" description="创建人"/>
        <Attribute name="update_time" type="datetime" description="更新时间"/>
        <Attribute name="update_id" type="bigint" description="更新人"/>
      </Attributes>
    </Entity>
    
    <Entity name="USER_WX" type="主实体">
      <Attributes>
        <Attribute name="id" type="bigint" key="PK" description="主键"/>
        <Attribute name="uid" type="bigint" key="FK" description="用户ID"/>
        <Attribute name="openid" type="varchar(128)" description="微信OpenID"/>
        <Attribute name="avatar" type="varchar(255)" description="头像"/>
        <Attribute name="nickname" type="varchar(10)" description="昵称"/>
        <Attribute name="phone" type="varchar(11)" description="手机号"/>
        <Attribute name="points" type="int" description="积分"/>
        <Attribute name="is_runner" type="tinyint" description="是否跑腿员"/>
        <Attribute name="can_order" type="tinyint" description="是否可下单"/>
        <Attribute name="can_take" type="tinyint" description="是否可接单"/>
        <Attribute name="school_id" type="bigint" key="FK" description="学校ID"/>
        <Attribute name="realname" type="varchar(20)" description="真实姓名"/>
        <Attribute name="gender" type="tinyint" description="性别"/>
        <Attribute name="credit_score" type="int" description="信用分"/>
      </Attributes>
    </Entity>
    
    <Entity name="USER_PC" type="主实体">
      <Attributes>
        <Attribute name="id" type="bigint" key="PK" description="主键"/>
        <Attribute name="uid" type="bigint" key="FK" description="用户ID"/>
        <Attribute name="username" type="varchar(25)" description="用户名"/>
        <Attribute name="password" type="varchar(100)" description="密码"/>
        <Attribute name="phone" type="varchar(11)" description="手机号"/>
        <Attribute name="name" type="varchar(20)" description="真实姓名"/>
        <Attribute name="student_card_url" type="varchar(255)" description="学生证"/>
        <Attribute name="id_card_url" type="varchar(255)" description="身份证"/>
        <Attribute name="sex" type="tinyint" description="性别"/>
        <Attribute name="status" type="tinyint" description="状态"/>
        <Attribute name="avatar" type="varchar(255)" description="头像"/>
        <Attribute name="email" type="varchar(50)" description="邮箱"/>
        <Attribute name="email_enable" type="tinyint" description="邮箱启用"/>
      </Attributes>
    </Entity>
    
    <!-- 学校实体 -->
    <Entity name="SCHOOL" type="主实体">
      <Attributes>
        <Attribute name="id" type="bigint" key="PK" description="学校ID"/>
        <Attribute name="belong_uid" type="bigint" key="FK" description="管理员ID"/>
        <Attribute name="adcode" type="char(6)" description="城市编码"/>
        <Attribute name="name" type="varchar(100)" description="学校名称"/>
        <Attribute name="logo" type="varchar(255)" description="学校logo"/>
        <Attribute name="create_time" type="datetime" description="创建时间"/>
        <Attribute name="update_time" type="datetime" description="更新时间"/>
        <Attribute name="status" type="tinyint" description="状态"/>
        <Attribute name="profit_plat" type="tinyint" description="平台收益占比"/>
        <Attribute name="profit_agent" type="tinyint" description="代理收益占比"/>
        <Attribute name="profit_runner" type="tinyint" description="跑腿收益占比"/>
        <Attribute name="floor_price" type="decimal(10,2)" description="底价"/>
        <Attribute name="additional_profit_rate" type="tinyint" description="追加金额分成比例"/>
        <Attribute name="emergency_min_amount" type="decimal(10,2)" description="加急最低追加金额"/>
      </Attributes>
    </Entity>
    
    <!-- 订单相关实体 -->
    <Entity name="ORDER_MAIN" type="主实体">
      <Attributes>
        <Attribute name="id" type="bigint" key="PK" description="订单ID"/>
        <Attribute name="school_id" type="bigint" key="FK" description="学校ID"/>
        <Attribute name="service_type" type="int" description="服务类型"/>
        <Attribute name="tag" type="varchar(50)" description="标签"/>
        <Attribute name="weight" type="varchar(20)" description="物品重量"/>
        <Attribute name="start_address" type="json" description="起点地址"/>
        <Attribute name="end_address" type="json" description="终点地址"/>
        <Attribute name="detail" type="varchar(100)" description="具体描述"/>
        <Attribute name="is_timed" type="tinyint" description="是否指定时间"/>
        <Attribute name="specified_time" type="datetime" description="指定时间"/>
        <Attribute name="auto_cancel_ttl" type="int" description="自动取消时间"/>
        <Attribute name="gender" type="tinyint" description="性别要求"/>
        <Attribute name="estimated_price" type="decimal(10,2)" description="预估商品价格"/>
        <Attribute name="total_amount" type="decimal(10,2)" description="订单总金额"/>
        <Attribute name="status" type="tinyint" description="订单状态"/>
        <Attribute name="user_id" type="bigint" key="FK" description="下单用户ID"/>
        <Attribute name="runner_id" type="bigint" key="FK" description="跑腿员ID"/>
        <Attribute name="create_time" type="datetime" description="创建时间"/>
        <Attribute name="update_time" type="datetime" description="更新时间"/>
      </Attributes>
    </Entity>
    
    <Entity name="ORDER_PAYMENT" type="从实体">
      <Attributes>
        <Attribute name="order_id" type="bigint" key="PK,FK" description="订单ID"/>
        <Attribute name="additional_amount" type="decimal(10,2)" description="附加金额"/>
        <Attribute name="actual_payment" type="decimal(10,2)" description="实付金额"/>
        <Attribute name="payment_status" type="tinyint" description="支付状态"/>
        <Attribute name="payment_time" type="datetime" description="付款时间"/>
        <Attribute name="refund_pending_time" type="datetime" description="退款中时间"/>
        <Attribute name="refund_time" type="datetime" description="退款时间"/>
        <Attribute name="is_couponed" type="tinyint" description="是否使用优惠券"/>
        <Attribute name="coupon_id" type="bigint" description="优惠券ID"/>
        <Attribute name="discount_amount" type="decimal(10,2)" description="优惠金额"/>
      </Attributes>
    </Entity>
    
    <Entity name="ORDER_PROGRESS" type="从实体">
      <Attributes>
        <Attribute name="order_id" type="bigint" key="PK,FK" description="订单ID"/>
        <Attribute name="accepted_time" type="datetime" description="接单时间"/>
        <Attribute name="delivering_time" type="datetime" description="开始配送时间"/>
        <Attribute name="delivered_time" type="datetime" description="送达时间"/>
        <Attribute name="completed_time" type="datetime" description="完成时间"/>
        <Attribute name="completed_type" type="tinyint" description="完成类型"/>
        <Attribute name="cancel_time" type="datetime" description="取消时间"/>
        <Attribute name="cancel_reason" type="varchar(100)" description="取消原因"/>
        <Attribute name="cancel_user_type" type="int" description="取消人类型"/>
        <Attribute name="cancel_user_id" type="bigint" description="取消人ID"/>
      </Attributes>
    </Entity>
    
    <Entity name="ORDER_CHAT" type="主实体">
      <Attributes>
        <Attribute name="id" type="bigint" key="PK" description="主键"/>
        <Attribute name="order_id" type="bigint" key="FK" description="订单ID"/>
        <Attribute name="sender_id" type="bigint" description="发送者ID"/>
        <Attribute name="sender_type" type="int" description="发送者类型"/>
        <Attribute name="recipients" type="varchar(255)" description="接收者IDs"/>
        <Attribute name="msg_type" type="int" description="消息类型"/>
        <Attribute name="message" type="varchar(255)" description="消息内容"/>
        <Attribute name="readIds" type="varchar(255)" description="已读IDs"/>
        <Attribute name="create_time" type="datetime" description="创建时间"/>
      </Attributes>
    </Entity>
    
    <Entity name="ORDER_APPEAL" type="主实体">
      <Attributes>
        <Attribute name="id" type="bigint" key="PK" description="主键"/>
        <Attribute name="order_id" type="bigint" key="FK" description="订单ID"/>
        <Attribute name="school_id" type="bigint" key="FK" description="学校ID"/>
        <Attribute name="appeal_time" type="datetime" description="申诉时间"/>
        <Attribute name="appeal_reason" type="varchar(100)" description="申诉理由"/>
        <Attribute name="appeal_status" type="tinyint" description="申诉状态"/>
        <Attribute name="update_time" type="datetime" description="更新时间"/>
        <Attribute name="remarks" type="varchar(100)" description="申诉备注"/>
        <Attribute name="update_id" type="bigint" description="更新人ID"/>
        <Attribute name="update_type" type="int" description="更新人类型"/>
      </Attributes>
    </Entity>
    
    <!-- 财务相关实体 -->
    <Entity name="WALLET" type="从实体">
      <Attributes>
        <Attribute name="uid" type="bigint" key="PK,FK" description="用户ID"/>
        <Attribute name="withdrawn" type="decimal(10,2)" description="当前余额"/>
        <Attribute name="balance" type="decimal(10,2)" description="已提现"/>
        <Attribute name="create_time" type="datetime" description="创建时间"/>
        <Attribute name="update_time" type="datetime" description="更新时间"/>
      </Attributes>
    </Entity>
    
    <Entity name="CAPITAL_FLOW" type="主实体">
      <Attributes>
        <Attribute name="id" type="bigint" key="PK" description="主键"/>
        <Attribute name="order_id" type="bigint" key="FK" description="订单ID"/>
        <Attribute name="agent_id" type="bigint" key="FK" description="代理ID"/>
        <Attribute name="profit_agent" type="decimal(10,2)" description="代理收益"/>
        <Attribute name="runner_id" type="bigint" key="FK" description="跑腿员ID"/>
        <Attribute name="profit_runner" type="decimal(10,2)" description="跑腿收益"/>
        <Attribute name="user_id" type="bigint" key="FK" description="用户ID"/>
        <Attribute name="profit_user" type="decimal(10,2)" description="用户收益"/>
        <Attribute name="profit_plat" type="decimal(10,2)" description="平台收益"/>
        <Attribute name="create_time" type="datetime" description="创建时间"/>
        <Attribute name="type" type="tinyint" description="类型"/>
      </Attributes>
    </Entity>
    
    <!-- 其他实体 -->
    <Entity name="ADDRESS" type="主实体">
      <Attributes>
        <Attribute name="id" type="bigint" key="PK" description="主键"/>
        <Attribute name="uid" type="bigint" key="FK" description="用户ID"/>
        <Attribute name="title" type="varchar(50)" description="地点"/>
        <Attribute name="detail" type="varchar(50)" description="地址详情"/>
        <Attribute name="lon" type="varchar(50)" description="经度"/>
        <Attribute name="lat" type="varchar(50)" description="纬度"/>
        <Attribute name="name" type="varchar(20)" description="姓名"/>
        <Attribute name="phone" type="varchar(11)" description="电话"/>
        <Attribute name="is_default" type="tinyint" description="是否默认"/>
        <Attribute name="create_time" type="datetime" description="创建时间"/>
        <Attribute name="create_id" type="bigint" description="创建人"/>
        <Attribute name="update_time" type="datetime" description="更新时间"/>
        <Attribute name="update_id" type="bigint" description="更新人"/>
      </Attributes>
    </Entity>
  </Entities>
  
  <!-- 关系定义 -->
  <Relationships>
    <Relationship name="用户-微信用户" type="一对多" from="USER" to="USER_WX" foreignKey="uid"/>
    <Relationship name="用户-PC用户" type="一对多" from="USER" to="USER_PC" foreignKey="uid"/>
    <Relationship name="用户-钱包" type="一对一" from="USER" to="WALLET" foreignKey="uid"/>
    <Relationship name="用户-地址" type="一对多" from="USER" to="ADDRESS" foreignKey="uid"/>
    <Relationship name="学校-订单" type="一对多" from="SCHOOL" to="ORDER_MAIN" foreignKey="school_id"/>
    <Relationship name="学校-微信用户" type="一对多" from="SCHOOL" to="USER_WX" foreignKey="school_id"/>
    <Relationship name="订单-支付" type="一对一" from="ORDER_MAIN" to="ORDER_PAYMENT" foreignKey="order_id"/>
    <Relationship name="订单-进度" type="一对一" from="ORDER_MAIN" to="ORDER_PROGRESS" foreignKey="order_id"/>
    <Relationship name="订单-聊天" type="一对多" from="ORDER_MAIN" to="ORDER_CHAT" foreignKey="order_id"/>
    <Relationship name="订单-申诉" type="一对多" from="ORDER_MAIN" to="ORDER_APPEAL" foreignKey="order_id"/>
    <Relationship name="订单-资金流水" type="一对多" from="ORDER_MAIN" to="CAPITAL_FLOW" foreignKey="order_id"/>
    <Relationship name="微信用户-下单" type="一对多" from="USER_WX" to="ORDER_MAIN" foreignKey="user_id"/>
    <Relationship name="微信用户-接单" type="一对多" from="USER_WX" to="ORDER_MAIN" foreignKey="runner_id"/>
  </Relationships>
</VisioDocument>

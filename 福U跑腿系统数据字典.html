<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>福U跑腿系统数据字典</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .mermaid { text-align: center; }
        h1 { text-align: center; color: #333; }
        .export-info { 
            background: #f0f8ff; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 20px 0;
            border-left: 4px solid #007acc;
        }
    </style>
</head>
<body>
    <h1>福U跑腿系统数据字典</h1>
    
    <div class="export-info">
        <h3>📋 导出说明：</h3>
        <ul>
            <li><strong>Word导入</strong>：右键图表 → 复制图像 → 粘贴到Word</li>
            <li><strong>Visio导入</strong>：另存为PNG/SVG → 在Visio中插入图片</li>
            <li><strong>高清导出</strong>：按F12打开开发者工具 → Console → 输入导出命令</li>
        </ul>
    </div>

    <div class="mermaid">
erDiagram
    USER {
        bigint uid PK "全局用户ID"
        tinyint device_type "设备类型(0:PC,1:小程序)"
        datetime create_time "创建时间"
        datetime login_time "最后登录时间"
        varchar login_ip "登录IP"
        varchar login_region "登录地区"
        int user_type "用户类型(0:超管,1:校管,2:普管,3:普通,4:跑腿)"
        bigint create_id "创建人ID"
        datetime update_time "更新时间"
        bigint update_id "更新人ID"
    }

    USER_WX {
        bigint id PK "主键ID"
        bigint uid FK "全局用户ID"
        varchar openid UK "微信OpenID"
        varchar avatar "头像URL"
        varchar nickname "昵称"
        varchar phone "手机号"
        int points "积分"
        tinyint is_runner "是否跑腿员(0:否,1:是)"
        tinyint can_order "是否可下单(0:否,1:是)"
        tinyint can_take "是否可接单(0:否,1:是)"
        bigint school_id FK "绑定学校ID"
        varchar realname "真实姓名"
        tinyint gender "性别(0:女,1:男)"
        int credit_score "信用分"
    }

    USER_PC {
        bigint id PK "主键ID"
        bigint uid FK "全局用户ID"
        varchar username UK "用户名"
        varchar password "密码"
        varchar phone "手机号"
        varchar name "真实姓名"
        varchar student_card_url "学生证URL"
        varchar id_card_url "身份证URL"
        tinyint sex "性别(0:女,1:男)"
        tinyint status "状态(0:禁用,1:启用)"
        varchar avatar "头像URL"
        varchar email "邮箱"
        tinyint email_enable "邮箱是否启用"
    }

    SCHOOL {
        bigint id PK "学校ID"
        bigint belong_uid FK "管理员ID"
        char adcode "城市编码"
        varchar name "学校名称"
        varchar logo "学校logo"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
        tinyint status "状态(0:禁用,1:启用)"
        tinyint profit_plat "平台收益占比"
        tinyint profit_agent "代理收益占比"
        tinyint profit_runner "跑腿收益占比"
        decimal floor_price "底价"
        tinyint additional_profit_rate "追加金额分成比例"
        decimal emergency_min_amount "加急最低追加金额"
    }

    ORDER_MAIN {
        bigint id PK "订单ID"
        bigint school_id FK "学校ID"
        int service_type "服务类型(0:帮取送,1:代买,2:万能)"
        varchar tag "标签"
        varchar weight "物品重量"
        json start_address "起点地址"
        json end_address "终点地址"
        varchar detail "具体描述"
        tinyint is_timed "是否指定时间"
        datetime specified_time "指定时间"
        int auto_cancel_ttl "自动取消时间(秒)"
        tinyint gender "性别要求(0:女,1:男,2:不限)"
        decimal estimated_price "预估商品价格"
        decimal total_amount "订单总金额"
        tinyint status "订单状态"
        bigint user_id FK "下单用户ID"
        bigint runner_id FK "跑腿员ID"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    ORDER_PAYMENT {
        bigint order_id PK,FK "订单ID"
        decimal additional_amount "附加金额"
        decimal actual_payment "实付金额"
        tinyint payment_status "支付状态(0:未付,1:已付,2:退款中,3:已退)"
        datetime payment_time "付款时间"
        datetime refund_pending_time "退款中时间"
        datetime refund_time "退款时间"
        tinyint is_couponed "是否使用优惠券"
        bigint coupon_id "优惠券ID"
        decimal discount_amount "优惠金额"
    }

    ORDER_PROGRESS {
        bigint order_id PK,FK "订单ID"
        datetime accepted_time "接单时间"
        datetime delivering_time "开始配送时间"
        datetime delivered_time "送达时间"
        datetime completed_time "完成时间"
        tinyint completed_type "完成类型"
        datetime cancel_time "取消时间"
        varchar cancel_reason "取消原因"
        int cancel_user_type "取消人类型"
        bigint cancel_user_id "取消人ID"
    }

    ORDER_CHAT {
        bigint id PK "聊天记录ID"
        bigint order_id FK "订单ID"
        bigint sender_id "发送者ID"
        int sender_type "发送者类型"
        varchar recipients "接收者IDs"
        int msg_type "消息类型"
        varchar message "消息内容"
        varchar readIds "已读IDs"
        datetime create_time "创建时间"
    }

    ORDER_APPEAL {
        bigint id PK "申诉ID"
        bigint order_id FK "订单ID"
        bigint school_id FK "学校ID"
        datetime appeal_time "申诉时间"
        varchar appeal_reason "申诉理由"
        tinyint appeal_status "申诉状态(0:不通过,1:通过,2:申诉中)"
        datetime update_time "更新时间"
        varchar remarks "申诉备注"
        bigint update_id "更新人ID"
        int update_type "更新人类型"
    }

    WALLET {
        bigint uid PK,FK "用户ID"
        decimal withdrawn "当前余额"
        decimal balance "已提现"
        datetime create_time "创建时间"
        datetime update_time "更新时间"
    }

    CAPITAL_FLOW {
        bigint id PK "流水ID"
        bigint order_id FK "订单ID"
        bigint agent_id FK "代理ID"
        decimal profit_agent "代理收益"
        bigint runner_id FK "跑腿ID"
        decimal profit_runner "跑腿收益"
        bigint user_id FK "用户ID"
        decimal profit_user "用户收益"
        decimal profit_plat "平台收益"
        datetime create_time "创建时间"
        tinyint type "类型"
    }

    ADDRESS {
        bigint id PK "地址ID"
        bigint uid FK "用户ID"
        varchar title "地点"
        varchar detail "地址详情"
        varchar lon "经度"
        varchar lat "纬度"
        varchar name "姓名"
        varchar phone "电话"
        tinyint is_default "是否默认"
        datetime create_time "创建时间"
        bigint create_id "创建人"
        datetime update_time "更新时间"
        bigint update_id "更新人"
    }

    USER ||--o{ USER_WX : "一对多"
    USER ||--o{ USER_PC : "一对多"
    USER ||--|| WALLET : "一对一"
    USER ||--o{ ADDRESS : "一对多"
    USER ||--o{ SCHOOL : "管理"
    USER ||--o{ CAPITAL_FLOW : "代理收益"
    USER ||--o{ CAPITAL_FLOW : "跑腿收益"
    USER ||--o{ CAPITAL_FLOW : "用户收益"
    
    SCHOOL ||--o{ ORDER_MAIN : "一对多"
    SCHOOL ||--o{ USER_WX : "绑定"
    SCHOOL ||--o{ ORDER_APPEAL : "一对多"
    
    USER_WX ||--o{ ORDER_MAIN : "下单"
    USER_WX ||--o{ ORDER_MAIN : "接单"
    
    ORDER_MAIN ||--|| ORDER_PAYMENT : "一对一"
    ORDER_MAIN ||--|| ORDER_PROGRESS : "一对一"
    ORDER_MAIN ||--o{ ORDER_CHAT : "一对多"
    ORDER_MAIN ||--o{ ORDER_APPEAL : "一对多"
    ORDER_MAIN ||--o{ CAPITAL_FLOW : "一对多"
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            er: {
                fontSize: 12,
                useMaxWidth: true
            }
        });
        
        // 导出功能
        console.log("💡 导出提示：");
        console.log("1. 右键图表 → 复制图像 → 粘贴到Word");
        console.log("2. 截图工具截取整个图表");
        console.log("3. 浏览器打印 → 另存为PDF");
    </script>
</body>
</html>

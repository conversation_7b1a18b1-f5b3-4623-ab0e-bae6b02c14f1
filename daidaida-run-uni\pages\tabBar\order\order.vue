<template>
	<view class="order-page">
		<nut-tabs v-model="tabValue" @change="tabChange" class="custom-tabs" type="smile">
		<!-- 待接单标签页放在左边 -->
		<nut-tab-pane title="待接单">
				<div v-show="skeletonLoading" class="skeleton-container">
					<nut-skeleton v-for="(item, index) in 6" :key="index" width="100%" height="24px" title animated row="3" class="skeleton-item"></nut-skeleton>
				</div>
				<template v-if="!skeletonLoading">
					<div v-if="total > 0" class="order-list">
						<div class="order-card" v-for="(item, index) in rows" :key="index" @click="toOrderDetailRunner(item)">
							<div class="order-header">
								<view class="order-type-tags">
									<nut-tag v-if="item.serviceType == 0" custom-color="#e9f7ff" text-color="#4F7DF5">帮取送</nut-tag>
									<nut-tag v-if="item.serviceType == 1" custom-color="#fff5e8" text-color="#ffaa00">帮买</nut-tag>
									<nut-tag v-if="item.serviceType == 2" custom-color="#f4ebff" text-color="#8850ef">万能帮</nut-tag>
									<text class="order-tag">{{item.tag}}</text>
								</view>
								<view class="order-status status-1">
									<text>待接单</text>
								</view>
							</div>
							
							<div class="order-content">
								<div class="address-info">
									<view v-if="item.startAddress != null" class="address-item start-address">
										<image class="address-icon" src="/static/icons/start.png"></image>
										<text class="address-text">{{item.startAddress.title}}{{item.startAddress.detail}}</text>
									</view>
									<view class="address-item end-address">
										<image class="address-icon" src="/static/icons/end.png"></image>
										<text class="address-text">{{item.endAddress.title}}{{item.endAddress.detail}}</text>
									</view>
					</div>
								<view class="price-tag">
									<text class="price-symbol">¥</text>
									<text class="price-value">{{item.totalAmount}}</text>
								</view>
					</div>
			
							<div class="order-footer">
								<text class="order-time">{{item.timeAgo}}</text>
								<nut-button @click.stop="viewOrderDetail(item)" size="small" type="primary">查看详情</nut-button>
				</div>
			</div>
						
						<div v-if="!hasMore" class="list-bottom">
				<nut-divider dashed>到底啦~</nut-divider>
			</div>
					</div>
					
					<nut-empty v-else description="暂无待接单订单" image="empty"></nut-empty>
				</template>
			
			<nut-safe-area position="bottom" />
		</nut-tab-pane>
			
		<!-- 全部订单标签页，仅对管理员可见，放在右边 -->
		<nut-tab-pane v-if="isAdmin" title="全部订单">
				<div v-show="skeletonLoading" class="skeleton-container">
					<nut-skeleton v-for="(item, index) in 6" :key="index" width="100%" height="24px" title animated row="3" class="skeleton-item"></nut-skeleton>
				</div>
				<template v-if="!skeletonLoading">
					<div v-if="total > 0" class="order-list">
						<div class="order-card" v-for="(item, index) in rows" :key="index" @click="toOrderDetailRunner(item)">
							<div class="order-header">
								<view class="order-type-tags">
									<nut-tag v-if="item.serviceType == 0" custom-color="#e9f7ff" text-color="#4F7DF5">帮取送</nut-tag>
									<nut-tag v-if="item.serviceType == 1" custom-color="#fff5e8" text-color="#ffaa00">帮买</nut-tag>
									<nut-tag v-if="item.serviceType == 2" custom-color="#f4ebff" text-color="#8850ef">万能帮</nut-tag>
									<text class="order-tag">{{item.tag}}</text>
								</view>
								<view class="order-status" :class="'status-' + item.status">
									<text v-if="item.status == 0">待支付</text>
									<text v-if="item.status == 1">待接单</text>
									<text v-if="item.status == 2">待配送</text>
									<text v-if="item.status == 3">配送中</text>
									<text v-if="item.status == 4">已送达</text>
									<text v-if="item.status == 5">已取消</text>
									<text v-if="item.status == 10">已完成</text>
									<text v-if="item.status == 11">已申诉</text>
								</view>
							</div>
							
							<div class="order-content">
								<div class="address-info">
									<view v-if="item.startAddress != null" class="address-item start-address">
										<image class="address-icon" src="/static/icons/start.png"></image>
										<text class="address-text">{{item.startAddress.title}}{{item.startAddress.detail}}</text>
									</view>
									<view class="address-item end-address">
										<image class="address-icon" src="/static/icons/end.png"></image>
										<text class="address-text">{{item.endAddress.title}}{{item.endAddress.detail}}</text>
									</view>
					</div>
								<view class="price-tag">
									<text class="price-symbol">¥</text>
									<text class="price-value">{{item.totalAmount}}</text>
								</view>
					</div>

							<div class="order-footer">
								<text class="order-time">{{item.timeAgo}}</text>
								<nut-button @click.stop="viewOrderDetail(item)" size="small" type="primary">查看详情</nut-button>
				</div>
			</div>
						
						<div v-if="!hasMore" class="list-bottom">
				<nut-divider dashed>到底啦~</nut-divider>
			</div>
					</div>
					
					<nut-empty v-else description="暂无订单数据" image="empty"></nut-empty>
				</template>
			
			<nut-safe-area position="bottom" />
		</nut-tab-pane>
	</nut-tabs>
	</view>
</template>

<script>
	import dayjs from 'dayjs';
	import relativeTime from 'dayjs/plugin/relativeTime'; // 引入 relativeTime 插件
	import 'dayjs/locale/zh-cn';  // 导入中文语言包
	// 扩展 dayjs 插件
	dayjs.extend(relativeTime);
	// 设置为中文
	dayjs.locale('zh-cn');
	import {
		getListOrderHall
	} from "@/request/apis/order.js"
	export default {
		data() {
			return {
				skeletonLoading:true,
				currSchool: null,
				tabValue: 0,
				title: 'Hello',
				queryParams: {
					schoolId: null,
					status: null,
					pageSize: 20,
					pageNum: 1
				},
				rows: [],
				total: 0,
				hasMore: true,
				isAdmin: false
			}
		},
		onLoad() {
			this.skeletonLoading = true
			this.initData()
			this.getList()
		},
		onReachBottom() {
			if (this.hasMore) {
				this.getList(); // 触底时加载更多
			}
		},
		onPullDownRefresh() {
			this.skeletonLoading = true
			this.resizePage()
			this.getList()
		},
		methods: {
			toOrderDetailRunner(e) {
				console.log(e);
				// 仅在特定条件下导航到详情页
				if (this.canViewOrderDetail(e)) {
				uni.navigateTo({
					url:"/pages/API/order/runner/runner?orderId=" + e.id
				})
				} else {
					uni.showToast({
						title: '您无权查看此订单详情',
						icon: 'none',
						duration: 2000
					})
				}
			},
			// 判断是否可以查看订单详情
			canViewOrderDetail(order) {
				// 管理员可以查看所有订单详情
				if (this.isAdmin) {
					return true;
				}
				
				// 跑腿用户可以查看所有订单详情
				const userInfo = this.$store.state.userInfo;
				if (userInfo && userInfo.userType === 4) {
					return true;
				}
				
				// 万能订单所有用户可查看
				if (order.serviceType == 2) {
					return true;
				}
				// 其他订单普通用户不可查看
				return false;
			},
			// 处理"查看详情"按钮点击
			viewOrderDetail(item) {
				// 阻止事件冒泡
				event.stopPropagation();
				if (this.canViewOrderDetail(item)) {
					uni.navigateTo({
						url:"/pages/API/order/runner/runner?orderId=" + item.id
					})
				} else {
					uni.showToast({
						title: '您无权查看此订单详情',
						icon: 'none',
						duration: 2000
					})
				}
			},
			tabChange(e) {
				let key = e.paneKey
				// 修正标签页索引与请求参数的对应关系
				if(key == '0') this.queryParams.status = 1   // 待接单 
				if(key == '1') this.queryParams.status = null // 全部订单
				this.resizePage()
				this.skeletonLoading = true
				this.getList()
			},
			initData() {
				this.currSchool = this.$store.state.currSchool;
				this.queryParams.schoolId = this.currSchool.id;
				
				// 检查用户角色
				const userInfo = this.$store.state.userInfo;
				// 判断是否为管理员用户（userType为0, 1, 2）
				if (userInfo && userInfo.userType !== undefined && userInfo.userType < 3) {
					this.isAdmin = true;
				} else {
					this.isAdmin = false;
				}
				
				// 默认设置待接单状态
				this.tabValue = 0;
				this.queryParams.status = 1;
			},
			resizePage() {
				this.queryParams.pageNum = 1;
				this.queryParams.pageSize = 20;
				this.rows = [];
				this.total = 0;
				this.hasMore = true
			},
			getList() {
				getListOrderHall(this.queryParams).then(res => {
					console.log(res);
					let data = res.rows
					for (var i = 0; i < data.length; i++) {
						data[i].timeAgo = dayjs(data[i].createTime).fromNow()
					}
					res.rows = data
					this.total = res.total
					this.rows.push(...res.rows)
					this.queryParams.pageNum += 1;
					this.hasMore = res.rows.length > 0
					
					this.skeletonLoading = false
					uni.stopPullDownRefresh();
				}).catch(err => {
					uni.showToast({
						title:err,
						icon:"none",
						duration:3000
					})
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	page {
  background-color: #f7f9ff;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
	}

.order-page {
  padding: 20rpx;
	}

/* 自定义标签栏 */
.custom-tabs {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
  
  :deep(.nut-tabs__titles) {
    padding: 16rpx 0;
    
    .nut-tabs__titles-item {
      font-size: 30rpx;
      color: #666;
      
      &.active {
        color: #4F7DF5;
        font-weight: 600;
      }
    }
    
    .nut-tabs__line {
      background-color: #4F7DF5;
      height: 4rpx;
      bottom: 16rpx;
      border-radius: 4rpx;
	}
  }
  
  :deep(.nut-tab-pane) {
		padding: 0 !important;
	}
}

/* 骨架屏 */
.skeleton-container {
  padding: 20rpx 0;
  
  .skeleton-item {
    margin-bottom: 30rpx;
    border-radius: 16rpx;
    padding: 24rpx;
    background-color: #fff;
  }
}

/* 订单列表 */
.order-list {
  .order-card {
    margin-bottom: 24rpx;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    
    &:active {
      transform: scale(0.98);
    }
    
    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 16rpx;
      border-bottom: 1px solid #f0f2f5;
      
      .order-type-tags {
        display: flex;
        align-items: center;
        
        .nut-tag {
          padding: 6rpx 16rpx !important;
          margin-right: 16rpx !important;
          font-size: 24rpx !important;
          font-weight: 500;
          border-radius: 8rpx !important;
        }
        
        .order-tag {
          color: #666;
          font-size: 26rpx;
          margin-left: 8rpx;
        }
	}

      .order-status {
        font-size: 26rpx;
        font-weight: 600;
        
        &.status-0 { color: #ffaa00; } // 待支付
        &.status-1 { color: #4F7DF5; } // 待接单
        &.status-2 { color: #42b983; } // 进行中
        &.status-3 { color: #8850ef; } // 已送达
        &.status-4 { color: #999; }    // 已取消
        &.status-10 { color: #42b983; } // 已完成
        &.status-11 { color: #ff6b6b; } // 已申诉
      }
    }
    
    .order-content {
      display: flex;
      padding: 20rpx 0;
      
      .address-info {
        flex: 1;
        overflow: hidden;
        
        .address-item {
          display: flex;
          align-items: center;
          margin-bottom: 16rpx;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .address-icon {
            width: 32rpx;
            height: 32rpx;
            margin-right: 12rpx;
            flex-shrink: 0;
          }
          
          .address-text {
            font-size: 26rpx;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          &.start-address .address-icon {
            color: #4F7DF5;
	}
          
          &.end-address .address-icon {
            color: #ff6b6b;
          }
        }
      }
      
      .price-tag {
        margin-left: 24rpx;
        display: flex;
        align-items: center;
        color: #ff6b6b;
        
        .price-symbol {
          font-size: 24rpx;
          margin-top: 4rpx;
        }
        
        .price-value {
          font-size: 36rpx;
          font-weight: 600;
        }
      }
    }
    
    .order-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 16rpx;
      border-top: 1px solid #f0f2f5;
      
      .order-time {
        font-size: 24rpx;
        color: #999;
      }
      
      :deep(.nut-button) {
        background-color: #4F7DF5;
        border-color: #4F7DF5;
        border-radius: 36rpx;
        padding: 6rpx 24rpx;
        font-size: 24rpx;
      }
    }
  }
  
  .list-bottom {
    padding: 32rpx 0;
    text-align: center;
    
    :deep(.nut-divider) {
      color: #999;
      margin: 32rpx 0;
    }
  }
		}
</style>
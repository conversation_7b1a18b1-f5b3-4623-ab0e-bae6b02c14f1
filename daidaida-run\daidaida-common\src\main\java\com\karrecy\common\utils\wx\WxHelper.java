package com.karrecy.common.utils.wx;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 小程序订阅消息类
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WxHelper {

    public static final String TEMPLATE_MSG_UN_READ = "nR0D0mjOJmS74O1eGsI0oncXXRcQg0lSbSSrucYQMNA";
    public static final String TEMPLATE_ORDER_STATUS_CHANGE = "j8Z2M2fdVyAAMsGcJXPWAXSfu3YxexvNMLM_1Dtp9-c";
    // 补差价通知模板ID
    public static final String TEMPLATE_PRICE_ADJUSTMENT = "p0THQpfjhGnJguDx1XPKlMfZLCqAMRJC3OflL6WLYws";

    public static final String PAGE_CHAT = "/pages/API/chat/chat";
    public static final String PAGE_ORDER_DETAIL = "/pages/API/order/detail/detail";
    public static final String PAGE_ORDER_RUNNER = "/pages/API/order/runner/runner";

    private final WxMaService wxMaService;
    private final WxSubscriptionManager subscriptionManager;

    /**
     * 发送订阅消息
     * 注意：仅在用户已授权的情况下才会发送，授权是一次性的
     */
    public void sendSubMsg(
            List<WxMaSubscribeMessage.MsgData> data,
            String page,
            String templateId,
            String openid) {
        // 检查参数
        if (openid == null || templateId == null) {
            log.error("发送订阅消息失败：参数不完整 openid={}, templateId={}", openid, templateId);
            return;
        }
        
        log.info("准备发送订阅消息：templateId={}, openid={}", templateId, openid);
        
        // 检查用户是否已授权订阅该模板消息
        // 生产环境下，应该检查用户是否已经授权
        Boolean hasSubscribed = subscriptionManager.checkUserSubscription(openid, templateId);
        log.info("用户授权状态检查：hasSubscribed={}, openid={}, templateId={}", hasSubscribed, openid, templateId);
        
        // 如果是补差价通知，暂时跳过授权检查，优先尝试发送
        boolean skipCheck = TEMPLATE_PRICE_ADJUSTMENT.equals(templateId);
        log.info("是否跳过授权检查：skipCheck={}, templateId={}", skipCheck, templateId);
        
        if (!hasSubscribed && !skipCheck) {
            log.warn("用户未授权接收订阅消息，或授权已被消耗：openid={}, templateId={}", openid, templateId);
            // 这里可以添加一些默认行为，比如保存消息到数据库等
            return;
        }
        
        WxMaSubscribeMessage msg = new WxMaSubscribeMessage();
        msg.setData(data);
        msg.setPage(page);
        msg.setTemplateId(templateId);
        msg.setToUser(openid);
        try {
            log.info("发送订阅消息：openid={}, templateId={}, page={}, data={}", openid, templateId, page, data);
            wxMaService.getMsgService().sendSubscribeMsg(msg);
            // 发送成功后，移除用户的订阅授权记录（因为已经消耗了）
            subscriptionManager.removeUserSubscription(openid, templateId);
            log.info("发送订阅消息成功：openid={}, templateId={}", openid, templateId);
        } catch (WxErrorException e) {
            log.error("发送订阅消息失败：错误码={}, 错误信息={}", 
                e.getError() != null ? e.getError().getErrorCode() : "未知", 
                e.getMessage(), e);
            
            // 处理特定错误码
            if (e.getError() != null && e.getError().getErrorCode() == 43101) {
                // 用户拒绝接受消息，清除该用户对应模板的订阅记录
                subscriptionManager.removeUserSubscription(openid, templateId);
                log.warn("用户已拒绝接收订阅消息：openid={}, templateId={}", openid, templateId);
            }
        }
    }

    /**
     * 记录用户订阅授权状态
     * 前端调用wx.requestSubscribeMessage成功后应调用此方法
     */
    public void recordSubscription(String openid, String templateId) {
        subscriptionManager.recordUserSubscription(openid, templateId);
    }

    /**
     * 构建订单状态消息数据
     * @param orderId
     * @param title
     * @param status
     * @param remarks
     * @return
     */
    public List<WxMaSubscribeMessage.MsgData> buildOrderStatusData(
            Long orderId,
            String title,
            String status,
            String remarks) {
        List<WxMaSubscribeMessage.MsgData> list = new ArrayList<>();
        WxMaSubscribeMessage.MsgData orderIdData = new WxMaSubscribeMessage.MsgData();
        orderIdData.setName("character_string1");
        orderIdData.setValue(String.valueOf(orderId));
        WxMaSubscribeMessage.MsgData messageData = new WxMaSubscribeMessage.MsgData();
        messageData.setName("short_thing5");
        messageData.setValue(truncate(title,20));
        WxMaSubscribeMessage.MsgData senderNameData = new WxMaSubscribeMessage.MsgData();
        senderNameData.setName("phrase2");
        senderNameData.setValue(truncate(status,5));
        WxMaSubscribeMessage.MsgData remarksData = new WxMaSubscribeMessage.MsgData();
        remarksData.setName("thing4");
        remarksData.setValue(truncate(remarks,20));
        WxMaSubscribeMessage.MsgData timeData = new WxMaSubscribeMessage.MsgData();
        timeData.setName("time3");
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm");
        timeData.setValue(now.format(formatter));
        list.add(orderIdData);
        list.add(messageData);
        list.add(senderNameData);
        list.add(timeData);
        list.add(remarksData);
        return list;
    }

    /**
     * 构建补差价通知消息数据
     * @param orderId 订单号
     * @param orderType 订单类型
     * @param amount 金额
     * @return 消息数据列表
     */
    public List<WxMaSubscribeMessage.MsgData> buildPriceAdjustmentData(
            String orderId,
            String orderType,
            String amount) {
        List<WxMaSubscribeMessage.MsgData> list = new ArrayList<>();
        
        // 订单号
        WxMaSubscribeMessage.MsgData orderIdData = new WxMaSubscribeMessage.MsgData();
        orderIdData.setName("character_string1");
        orderIdData.setValue(orderId);
        
        // 订单类型
        WxMaSubscribeMessage.MsgData orderTypeData = new WxMaSubscribeMessage.MsgData();
        orderTypeData.setName("thing2");
        orderTypeData.setValue(truncate(orderType, 20));
        
        // 订单金额
        WxMaSubscribeMessage.MsgData amountData = new WxMaSubscribeMessage.MsgData();
        amountData.setName("amount3");
        amountData.setValue(amount);
        
        list.add(orderIdData);
        list.add(orderTypeData);
        list.add(amountData);
        
        return list;
    }

    /**
     * 构建未读消息数据
     * @param orderId
     * @param content
     * @param senderName
     * @param remarks
     * @return
     */
    public List<WxMaSubscribeMessage.MsgData> buildMsgUnReadData(
            Long orderId,
            String content,
            String senderName,
            String remarks) {
        List<WxMaSubscribeMessage.MsgData> list = new ArrayList<>();
        WxMaSubscribeMessage.MsgData orderIdData = new WxMaSubscribeMessage.MsgData();
        orderIdData.setName("character_string1");
        orderIdData.setValue(String.valueOf(orderId));
        WxMaSubscribeMessage.MsgData messageData = new WxMaSubscribeMessage.MsgData();
        messageData.setName("thing3");
        messageData.setValue(truncate(content,20));
        WxMaSubscribeMessage.MsgData senderNameData = new WxMaSubscribeMessage.MsgData();
        senderNameData.setName("thing4");
        senderNameData.setValue(truncate(senderName,20));
        WxMaSubscribeMessage.MsgData remarksData = new WxMaSubscribeMessage.MsgData();
        remarksData.setName("thing2");
        remarksData.setValue(truncate(remarks,20));
        list.add(messageData);
        list.add(orderIdData);
        list.add(senderNameData);
        list.add(remarksData);
        return list;
    }

    private String truncate(String text, int maxLength) {
        if (text == null) {
            return "";
        }
        return text.length() > maxLength ? text.substring(0, maxLength) + "..." : text;
    }
}

-- 福U跑腿系统数据库结构
-- 基于现有数据库结构整理的完整DDL脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `daidaida` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
USE `daidaida`;

-- 1. 全局用户表
CREATE TABLE `user` (
  `uid` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '全局uid',
  `device_type` tinyint(4) NOT NULL COMMENT '0 pc 1 小程序',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `login_time` datetime NOT NULL COMMENT '上次登录时间',
  `login_ip` varchar(20) NOT NULL COMMENT '登录ip',
  `login_region` varchar(50) NOT NULL COMMENT '登录地址',
  `user_type` int(11) NOT NULL COMMENT '用户类型 0 超级管理员 1 校区管理员 2 普通管理员 3 普通用户 4 跑腿用户',
  `create_id` bigint(20) NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_id` bigint(20) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局用户表';

-- 2. 微信用户表
CREATE TABLE `user_wx` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NOT NULL,
  `openid` varchar(128) NOT NULL COMMENT '小程序唯一id',
  `avatar` varchar(255) NOT NULL COMMENT '头像',
  `nickname` varchar(10) NOT NULL COMMENT '昵称',
  `phone` varchar(11) DEFAULT NULL COMMENT '手机',
  `points` int(11) NOT NULL DEFAULT '0' COMMENT '积分',
  `is_runner` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否跑腿 0 否 1 是',
  `can_order` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否可以下单 0 否 1 是',
  `can_take` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否可以接单 0 否 1 是',
  `school_id` bigint(20) DEFAULT NULL COMMENT '跑腿绑定学校id',
  `realname` varchar(20) DEFAULT NULL COMMENT '跑腿真实姓名',
  `gender` tinyint(4) DEFAULT NULL COMMENT '跑腿性别',
  `credit_score` int(11) DEFAULT NULL COMMENT '信用分',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_openid` (`openid`),
  KEY `idx_uid` (`uid`),
  KEY `idx_school_id` (`school_id`),
  CONSTRAINT `fk_user_wx_uid` FOREIGN KEY (`uid`) REFERENCES `user` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小程序用户表';

-- 3. PC用户表
CREATE TABLE `user_pc` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NOT NULL,
  `username` varchar(25) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `phone` varchar(11) NOT NULL COMMENT '手机号',
  `name` varchar(20) NOT NULL COMMENT '真实姓名',
  `student_card_url` varchar(255) DEFAULT NULL COMMENT '学生证',
  `id_card_url` varchar(255) DEFAULT NULL COMMENT '身份证',
  `sex` tinyint(4) NOT NULL COMMENT '0 女 1 男',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 禁用 1 启用',
  `avatar` varchar(255) NOT NULL COMMENT '头像',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `email_enable` tinyint(4) DEFAULT NULL COMMENT '是否启用邮箱',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  KEY `idx_uid` (`uid`),
  CONSTRAINT `fk_user_pc_uid` FOREIGN KEY (`uid`) REFERENCES `user` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PC用户表';

-- 4. 学校表
CREATE TABLE `school` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `belong_uid` bigint(20) NOT NULL COMMENT '属于谁管理',
  `adcode` char(6) DEFAULT NULL COMMENT '城市编码表',
  `name` varchar(100) NOT NULL COMMENT '学校名称',
  `logo` varchar(255) NOT NULL COMMENT '学校logo',
  `create_time` datetime NOT NULL,
  `update_time` datetime NOT NULL,
  `status` tinyint(4) NOT NULL COMMENT '状态 0 禁用 1 启用',
  `profit_plat` tinyint(4) NOT NULL COMMENT '平台收益占比',
  `profit_agent` tinyint(4) NOT NULL COMMENT '代理收益占比',
  `profit_runner` tinyint(4) NOT NULL COMMENT '跑腿收益占比',
  `floor_price` decimal(10,2) NOT NULL COMMENT '底价',
  `additional_profit_rate` tinyint(4) DEFAULT NULL COMMENT '追加金额订单平台分成比例',
  `emergency_min_amount` decimal(10,2) DEFAULT NULL COMMENT '加急订单最低追加金额',
  PRIMARY KEY (`id`),
  KEY `idx_belong_uid` (`belong_uid`),
  CONSTRAINT `fk_school_belong_uid` FOREIGN KEY (`belong_uid`) REFERENCES `user` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学校表';

-- 5. 订单主表
CREATE TABLE `order_main` (
  `id` bigint(20) NOT NULL,
  `school_id` bigint(20) NOT NULL COMMENT '学校id',
  `service_type` int(11) NOT NULL COMMENT '服务类型 0 帮取送 1 代买 2 万能服务',
  `tag` varchar(50) NOT NULL COMMENT '标签',
  `weight` varchar(20) DEFAULT NULL COMMENT '物品重量',
  `start_address` json DEFAULT NULL COMMENT '起点地址',
  `end_address` json NOT NULL COMMENT '终点地址',
  `detail` varchar(100) NOT NULL COMMENT '具体描述（暴露）',
  `is_timed` tinyint(4) NOT NULL COMMENT '是否指定时间 0 否 1 是',
  `specified_time` datetime DEFAULT NULL COMMENT '指定时间',
  `auto_cancel_ttl` int(11) NOT NULL COMMENT '未结单取消时间（秒）',
  `gender` tinyint(4) NOT NULL COMMENT '0女 1男 2不限',
  `estimated_price` decimal(10,2) DEFAULT NULL COMMENT '预估商品价格',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `status` tinyint(4) NOT NULL COMMENT '订单状态',
  `user_id` bigint(20) NOT NULL COMMENT '下单用户id',
  `runner_id` bigint(20) DEFAULT NULL COMMENT '跑腿员id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_school_id` (`school_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_runner_id` (`runner_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_order_school_id` FOREIGN KEY (`school_id`) REFERENCES `school` (`id`),
  CONSTRAINT `fk_order_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`uid`),
  CONSTRAINT `fk_order_runner_id` FOREIGN KEY (`runner_id`) REFERENCES `user` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';

-- 6. 订单支付表
CREATE TABLE `order_payment` (
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `additional_amount` decimal(10,2) DEFAULT NULL COMMENT '附加金额',
  `actual_payment` decimal(10,2) DEFAULT NULL COMMENT '实付金额',
  `payment_status` tinyint(4) NOT NULL COMMENT '支付状态 0未支付 1已支付 2退款中 3已退款',
  `payment_time` datetime DEFAULT NULL COMMENT '付款时间',
  `refund_pending_time` datetime DEFAULT NULL COMMENT '退款中时间',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `is_couponed` tinyint(4) NOT NULL COMMENT '是否使用优惠券 0 否 1 是',
  `coupon_id` bigint(20) DEFAULT NULL COMMENT '优惠券ID',
  `discount_amount` decimal(10,2) DEFAULT NULL COMMENT '优惠金额',
  PRIMARY KEY (`order_id`),
  KEY `idx_coupon_id` (`coupon_id`),
  CONSTRAINT `fk_payment_order_id` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单支付表';

-- 7. 订单进度表
CREATE TABLE `order_progress` (
  `order_id` bigint(20) NOT NULL COMMENT '订单id',
  `accepted_time` datetime DEFAULT NULL COMMENT '接单时间',
  `delivering_time` datetime DEFAULT NULL COMMENT '开始配送时间',
  `delivered_time` datetime DEFAULT NULL COMMENT '送达时间',
  `completed_time` datetime DEFAULT NULL COMMENT '完成时间',
  `completed_type` tinyint(4) DEFAULT NULL COMMENT '由谁完成',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(100) DEFAULT NULL COMMENT '取消原因',
  `cancel_user_type` int(11) DEFAULT NULL COMMENT '取消人类型',
  `cancel_user_id` bigint(20) DEFAULT NULL COMMENT '取消人ID',
  PRIMARY KEY (`order_id`),
  CONSTRAINT `fk_progress_order_id` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单进度表';

-- 8. 订单聊天表
CREATE TABLE `order_chat` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) NOT NULL,
  `sender_id` bigint(20) DEFAULT NULL COMMENT '发送者id',
  `sender_type` int(11) DEFAULT NULL COMMENT '发送者类型',
  `recipients` varchar(255) DEFAULT NULL COMMENT '接收者ids',
  `msg_type` int(11) DEFAULT NULL COMMENT '消息类型',
  `message` varchar(255) DEFAULT NULL COMMENT '消息体',
  `readIds` varchar(255) DEFAULT NULL COMMENT '已读ids',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id` DESC),
  KEY `idx_order_id` (`order_id`),
  CONSTRAINT `fk_chat_order_id` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单聊天表';

-- 9. 订单申诉表
CREATE TABLE `order_appeal` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) NOT NULL COMMENT '订单id',
  `school_id` bigint(20) DEFAULT NULL COMMENT '学校id',
  `appeal_time` datetime DEFAULT NULL COMMENT '申诉时间',
  `appeal_reason` varchar(100) DEFAULT NULL COMMENT '申诉理由',
  `appeal_status` tinyint(4) DEFAULT NULL COMMENT '申诉状态 0 不通过 1 通过 2 申诉中',
  `update_time` datetime DEFAULT NULL COMMENT '申诉更新时间',
  `remarks` varchar(100) DEFAULT NULL COMMENT '申诉驳回原因',
  `update_id` bigint(20) DEFAULT NULL COMMENT '更新人id',
  `update_type` int(11) DEFAULT NULL COMMENT '更新人类型',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  CONSTRAINT `fk_appeal_order_id` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`),
  CONSTRAINT `fk_appeal_school_id` FOREIGN KEY (`school_id`) REFERENCES `school` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单申诉表';

-- 10. 用户钱包表
CREATE TABLE `wallet` (
  `uid` bigint(20) NOT NULL COMMENT 'uid',
  `withdrawn` decimal(10,2) NOT NULL COMMENT '当前余额',
  `balance` decimal(10,2) NOT NULL COMMENT '已提现',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`uid`),
  CONSTRAINT `fk_wallet_uid` FOREIGN KEY (`uid`) REFERENCES `user` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户账户表';

-- 11. 资金流水表
CREATE TABLE `capital_flow` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单id',
  `agent_id` bigint(20) DEFAULT NULL COMMENT '代理id',
  `profit_agent` decimal(10,2) DEFAULT NULL COMMENT '代理收益',
  `runner_id` bigint(20) DEFAULT NULL COMMENT '跑腿id',
  `profit_runner` decimal(10,2) DEFAULT NULL COMMENT '跑腿收益',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `profit_user` decimal(10,2) DEFAULT NULL COMMENT '用户收益',
  `profit_plat` decimal(10,2) DEFAULT NULL COMMENT '平台收益',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `type` tinyint(3) unsigned DEFAULT NULL COMMENT '类型 订单收益 跑腿提现 代理提现',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_runner_id` (`runner_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_capital_order_id` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`),
  CONSTRAINT `fk_capital_agent_id` FOREIGN KEY (`agent_id`) REFERENCES `user` (`uid`),
  CONSTRAINT `fk_capital_runner_id` FOREIGN KEY (`runner_id`) REFERENCES `user` (`uid`),
  CONSTRAINT `fk_capital_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资金流动表';

-- 12. 用户地址表
CREATE TABLE `address` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NOT NULL,
  `title` varchar(50) NOT NULL COMMENT '地点',
  `detail` varchar(50) NOT NULL COMMENT '地址详情',
  `lon` varchar(50) NOT NULL COMMENT '经度',
  `lat` varchar(50) NOT NULL COMMENT '纬度',
  `name` varchar(20) NOT NULL COMMENT '姓名',
  `phone` varchar(11) NOT NULL COMMENT '电话',
  `is_default` tinyint(4) NOT NULL COMMENT '默认 0 否 1 是',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_id` bigint(20) NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `update_id` bigint(20) NOT NULL COMMENT '修改人',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`),
  CONSTRAINT `fk_address_uid` FOREIGN KEY (`uid`) REFERENCES `user` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户地址表';

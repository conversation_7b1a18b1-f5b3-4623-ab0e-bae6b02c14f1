package com.karrecy.payment.service;

import com.karrecy.payment.domain.vo.PayedVO;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;


public interface IPayService {
    /**
     * 支付回调
     */
    String payNotifyV3(String notifyData, HttpServletRequest request);

    /**
     * 请求微信支付
     * @param desc
     * @param orderId
     * @param totalAmount
     * @param openid
     * @return
     */
    PayedVO pay(String desc, Long orderId, BigDecimal totalAmount, String openid);
    
    /**
     * 申请退款
     * @param orderId
     * @param totalAmount
     * @param refundAmount
     */
    void refund(Long orderId, BigDecimal totalAmount,BigDecimal refundAmount);
    
    /**
     * 退款成功回调
     * @param notifyData
     * @param request
     * @return
     */
    String refundNotify(String notifyData, HttpServletRequest request);
    
    /**
     * 补差价支付
     * 专门处理已申诉订单的补差价支付，忽略订单状态检查
     * 
     * @param desc 支付描述
     * @param orderId 原订单ID
     * @param extraAmount 补差价金额
     * @param openid 用户openid
     * @return 支付参数
     */
    PayedVO payExtraAmount(String desc, Long orderId, BigDecimal extraAmount, String openid);
}

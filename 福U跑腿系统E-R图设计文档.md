# 福U跑腿系统数据库E-R图设计文档

## 实体列表

### 1. 用户实体 (USER)
- **主键**: uid (bigint) - 全局用户ID
- **属性**:
  - device_type (tinyint) - 设备类型 (0:PC, 1:小程序)
  - create_time (datetime) - 创建时间
  - login_time (datetime) - 最后登录时间
  - login_ip (varchar) - 登录IP地址
  - login_region (varchar) - 登录地区
  - user_type (int) - 用户类型 (0:超级管理员, 1:校区管理员, 2:普通管理员, 3:普通用户, 4:跑腿用户)
  - create_id (bigint) - 创建人ID
  - update_time (datetime) - 更新时间
  - update_id (bigint) - 更新人ID

### 2. 微信用户实体 (USER_WX)
- **主键**: id (bigint)
- **外键**: uid (bigint) → USER.uid
- **属性**:
  - openid (varchar) - 微信OpenID
  - avatar (varchar) - 头像URL
  - nickname (varchar) - 昵称
  - phone (varchar) - 手机号
  - points (int) - 积分
  - is_runner (tinyint) - 是否跑腿员 (0:否, 1:是)
  - can_order (tinyint) - 是否可下单 (0:否, 1:是)
  - can_take (tinyint) - 是否可接单 (0:否, 1:是)
  - school_id (bigint) - 绑定学校ID
  - realname (varchar) - 真实姓名
  - gender (tinyint) - 性别 (0:女, 1:男)
  - credit_score (int) - 信用分

### 3. PC用户实体 (USER_PC)
- **主键**: id (bigint)
- **外键**: uid (bigint) → USER.uid
- **属性**:
  - username (varchar) - 用户名
  - password (varchar) - 密码
  - phone (varchar) - 手机号
  - name (varchar) - 真实姓名
  - student_card_url (varchar) - 学生证URL
  - id_card_url (varchar) - 身份证URL
  - sex (tinyint) - 性别 (0:女, 1:男)
  - status (tinyint) - 状态 (0:禁用, 1:启用)
  - avatar (varchar) - 头像URL
  - email (varchar) - 邮箱
  - email_enable (tinyint) - 邮箱是否启用

### 4. 学校实体 (SCHOOL)
- **主键**: id (bigint)
- **外键**: belong_uid (bigint) → USER.uid
- **属性**:
  - adcode (char) - 城市编码
  - name (varchar) - 学校名称
  - logo (varchar) - 学校logo URL
  - create_time (datetime) - 创建时间
  - update_time (datetime) - 更新时间
  - status (tinyint) - 状态 (0:禁用, 1:启用)
  - profit_plat (tinyint) - 平台收益占比
  - profit_agent (tinyint) - 代理收益占比
  - profit_runner (tinyint) - 跑腿收益占比
  - floor_price (decimal) - 底价
  - additional_profit_rate (tinyint) - 追加金额分成比例
  - emergency_min_amount (decimal) - 加急最低追加金额

### 5. 订单主表实体 (ORDER_MAIN)
- **主键**: id (bigint)
- **外键**: 
  - school_id (bigint) → SCHOOL.id
  - user_id (bigint) → USER_WX.uid
  - runner_id (bigint) → USER_WX.uid
- **属性**:
  - service_type (int) - 服务类型 (0:帮取送, 1:代买, 2:万能服务)
  - tag (varchar) - 标签
  - weight (varchar) - 物品重量
  - start_address (json) - 起点地址
  - end_address (json) - 终点地址
  - detail (varchar) - 具体描述
  - is_timed (tinyint) - 是否指定时间
  - specified_time (datetime) - 指定时间
  - auto_cancel_ttl (int) - 自动取消时间(秒)
  - gender (tinyint) - 性别要求 (0:女, 1:男, 2:不限)
  - estimated_price (decimal) - 预估商品价格
  - total_amount (decimal) - 订单总金额
  - status (tinyint) - 订单状态
  - create_time (datetime) - 创建时间
  - update_time (datetime) - 更新时间

### 6. 订单支付实体 (ORDER_PAYMENT)
- **主键**: order_id (bigint)
- **外键**: order_id (bigint) → ORDER_MAIN.id
- **属性**:
  - additional_amount (decimal) - 附加金额
  - actual_payment (decimal) - 实付金额
  - payment_status (tinyint) - 支付状态 (0:未支付, 1:已支付, 2:退款中, 3:已退款)
  - payment_time (datetime) - 付款时间
  - refund_pending_time (datetime) - 退款中时间
  - refund_time (datetime) - 退款时间
  - is_couponed (tinyint) - 是否使用优惠券
  - coupon_id (bigint) - 优惠券ID
  - discount_amount (decimal) - 优惠金额

### 7. 订单进度实体 (ORDER_PROGRESS)
- **主键**: order_id (bigint)
- **外键**: order_id (bigint) → ORDER_MAIN.id
- **属性**:
  - accepted_time (datetime) - 接单时间
  - delivering_time (datetime) - 开始配送时间
  - delivered_time (datetime) - 送达时间
  - completed_time (datetime) - 完成时间
  - completed_type (tinyint) - 完成类型
  - cancel_time (datetime) - 取消时间
  - cancel_reason (varchar) - 取消原因
  - cancel_user_type (int) - 取消人类型
  - cancel_user_id (bigint) - 取消人ID

### 8. 订单聊天实体 (ORDER_CHAT)
- **主键**: id (bigint)
- **外键**: order_id (bigint) → ORDER_MAIN.id
- **属性**:
  - sender_id (bigint) - 发送者ID
  - sender_type (int) - 发送者类型
  - recipients (varchar) - 接收者IDs
  - msg_type (int) - 消息类型
  - message (varchar) - 消息内容
  - readIds (varchar) - 已读IDs
  - create_time (datetime) - 创建时间

### 9. 订单申诉实体 (ORDER_APPEAL)
- **主键**: id (bigint)
- **外键**: 
  - order_id (bigint) → ORDER_MAIN.id
  - school_id (bigint) → SCHOOL.id
- **属性**:
  - appeal_time (datetime) - 申诉时间
  - appeal_reason (varchar) - 申诉理由
  - appeal_status (tinyint) - 申诉状态 (0:不通过, 1:通过, 2:申诉中)
  - update_time (datetime) - 更新时间
  - remarks (varchar) - 申诉备注
  - update_id (bigint) - 更新人ID
  - update_type (int) - 更新人类型

### 10. 钱包实体 (WALLET)
- **主键**: uid (bigint)
- **外键**: uid (bigint) → USER.uid
- **属性**:
  - withdrawn (decimal) - 当前余额
  - balance (decimal) - 已提现
  - create_time (datetime) - 创建时间
  - update_time (datetime) - 更新时间

### 11. 资金流水实体 (CAPITAL_FLOW)
- **主键**: id (bigint)
- **外键**: 
  - order_id (bigint) → ORDER_MAIN.id
  - agent_id (bigint) → USER.uid
  - runner_id (bigint) → USER.uid
  - user_id (bigint) → USER.uid
- **属性**:
  - profit_agent (decimal) - 代理收益
  - profit_runner (decimal) - 跑腿收益
  - profit_user (decimal) - 用户收益
  - profit_plat (decimal) - 平台收益
  - create_time (datetime) - 创建时间
  - type (tinyint) - 类型

### 12. 地址实体 (ADDRESS)
- **主键**: id (bigint)
- **外键**: uid (bigint) → USER.uid
- **属性**:
  - title (varchar) - 地点
  - detail (varchar) - 地址详情
  - lon (varchar) - 经度
  - lat (varchar) - 纬度
  - name (varchar) - 姓名
  - phone (varchar) - 电话
  - is_default (tinyint) - 是否默认
  - create_time (datetime) - 创建时间
  - create_id (bigint) - 创建人
  - update_time (datetime) - 更新时间
  - update_id (bigint) - 更新人

## 实体关系

### 一对一关系
- USER ↔ WALLET (用户与钱包)
- ORDER_MAIN ↔ ORDER_PAYMENT (订单与支付信息)
- ORDER_MAIN ↔ ORDER_PROGRESS (订单与进度信息)

### 一对多关系
- USER → USER_WX (一个全局用户可以有多个微信账户)
- USER → USER_PC (一个全局用户可以有多个PC账户)
- USER → ADDRESS (一个用户可以有多个地址)
- SCHOOL → ORDER_MAIN (一个学校可以有多个订单)
- SCHOOL → USER_WX (一个学校可以有多个微信用户)
- ORDER_MAIN → ORDER_CHAT (一个订单可以有多条聊天记录)
- ORDER_MAIN → ORDER_APPEAL (一个订单可以有多次申诉)
- ORDER_MAIN → CAPITAL_FLOW (一个订单可以产生多条资金流水)

### 多对多关系
- USER_WX ↔ ORDER_MAIN (用户下单关系)
- USER_WX ↔ ORDER_MAIN (跑腿员接单关系)

## 索引建议
- USER表: uid(主键), user_type
- USER_WX表: uid, openid, school_id
- ORDER_MAIN表: id(主键), school_id, user_id, runner_id, status, create_time
- ORDER_PAYMENT表: order_id(主键), payment_status
- CAPITAL_FLOW表: order_id, agent_id, runner_id, user_id

package com.karrecy.admin.controller.wx;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.karrecy.common.core.domain.R;
import com.karrecy.common.core.domain.entity.UserWx;
import com.karrecy.common.helper.LoginHelper;
import com.karrecy.common.utils.wx.WxHelper;
import com.karrecy.system.mapper.UserWxMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 微信订阅消息控制器
 */
@RestController
@RequestMapping("/wx/subscription")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin // 添加跨域支持
public class WxSubscriptionController {

    private final WxHelper wxHelper;
    private final UserWxMapper userWxMapper;

    /**
     * 记录用户订阅授权
     * @param templateId 模板ID
     * @return 结果
     */
    @PostMapping("/record")
    @SaIgnore // 忽略权限检查，允许小程序直接访问
    public R<Void> recordSubscription(@RequestParam String templateId) {
        try {
            // 获取当前登录用户
            Long userId = LoginHelper.getUserId();
            if (userId == null) {
                return R.fail("用户未登录");
            }
            
            // 获取用户openid
            UserWx userWx = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>()
                    .eq(UserWx::getUid, userId));
            if (userWx == null || userWx.getOpenid() == null) {
                return R.fail("未找到用户微信信息");
            }
            
            // 记录用户订阅授权
            wxHelper.recordSubscription(userWx.getOpenid(), templateId);
            log.info("用户订阅授权记录成功: userId={}, openid={}, templateId={}", 
                    userId, userWx.getOpenid(), templateId);
            
            return R.ok();
        } catch (Exception e) {
            log.error("记录用户订阅授权失败", e);
            return R.fail("记录订阅授权失败");
        }
    }
    
    /**
     * 记录多个模板的订阅授权
     * @param templateIds 模板ID数组
     * @return 结果
     */
    @PostMapping("/record-batch")
    @SaIgnore // 忽略权限检查，允许小程序直接访问
    public R<Void> recordBatchSubscription(@RequestBody String[] templateIds) {
        try {
            if (templateIds == null || templateIds.length == 0) {
                return R.fail("模板ID不能为空");
            }
            
            // 获取当前登录用户
            Long userId = LoginHelper.getUserId();
            if (userId == null) {
                return R.fail("用户未登录");
            }
            
            // 获取用户openid
            UserWx userWx = userWxMapper.selectOne(new LambdaQueryWrapper<UserWx>()
                    .eq(UserWx::getUid, userId));
            if (userWx == null || userWx.getOpenid() == null) {
                return R.fail("未找到用户微信信息");
            }
            
            // 记录多个模板订阅授权
            for (String templateId : templateIds) {
                wxHelper.recordSubscription(userWx.getOpenid(), templateId);
            }
            
            log.info("用户批量订阅授权记录成功: userId={}, openid={}, templateCount={}", 
                    userId, userWx.getOpenid(), templateIds.length);
            
            return R.ok();
        } catch (Exception e) {
            log.error("记录用户批量订阅授权失败", e);
            return R.fail("记录订阅授权失败");
        }
    }
} 
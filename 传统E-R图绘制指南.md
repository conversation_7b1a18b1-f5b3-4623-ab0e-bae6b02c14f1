# 福U跑腿系统传统E-R图绘制指南

## 图例说明
- **矩形框**: 实体 (Entity)
- **椭圆形**: 属性 (Attribute)  
- **菱形**: 关系 (Relationship)
- **双椭圆**: 多值属性
- **虚椭圆**: 派生属性
- **下划线**: 主键属性

## 核心实体及其属性

### 1. 用户实体 [USER]
**位置**: 图的左上角
**属性**:
- <u>uid</u> (主键，下划线)
- 设备类型
- 创建时间  
- 登录时间
- 登录IP
- 用户类型

### 2. 微信用户实体 [USER_WX]
**位置**: 用户实体右侧
**属性**:
- <u>id</u> (主键)
- uid (外键)
- openid
- 头像
- 昵称
- 手机号
- 积分
- 是否跑腿员
- 可否下单
- 可否接单
- 信用分

### 3. 学校实体 [SCHOOL]
**位置**: 图的上方中央
**属性**:
- <u>id</u> (主键)
- 学校名称
- logo
- 状态
- 平台收益占比
- 代理收益占比
- 跑腿收益占比
- 底价

### 4. 订单实体 [ORDER_MAIN]
**位置**: 图的中央
**属性**:
- <u>id</u> (主键)
- 服务类型
- 标签
- 重量
- 起点地址
- 终点地址
- 详细描述
- 总金额
- 订单状态
- 创建时间

### 5. 订单支付实体 [ORDER_PAYMENT]
**位置**: 订单实体下方
**属性**:
- <u>order_id</u> (主键，外键)
- 支付状态
- 实付金额
- 付款时间
- 退款时间
- 是否使用优惠券

### 6. 订单进度实体 [ORDER_PROGRESS]
**位置**: 订单实体右下方
**属性**:
- <u>order_id</u> (主键，外键)
- 接单时间
- 开始配送时间
- 送达时间
- 完成时间
- 取消时间
- 取消原因

### 7. 钱包实体 [WALLET]
**位置**: 用户实体下方
**属性**:
- <u>uid</u> (主键，外键)
- 当前余额
- 已提现
- 创建时间
- 更新时间

### 8. 资金流水实体 [CAPITAL_FLOW]
**位置**: 图的右下角
**属性**:
- <u>id</u> (主键)
- 订单id (外键)
- 代理收益
- 跑腿收益
- 用户收益
- 平台收益
- 创建时间
- 类型

### 9. 地址实体 [ADDRESS]
**位置**: 用户实体左下方
**属性**:
- <u>id</u> (主键)
- uid (外键)
- 地点
- 地址详情
- 经度
- 纬度
- 姓名
- 电话
- 是否默认

### 10. 跑腿申请实体 [RUNNER_APPLY]
**位置**: 图的左下角
**属性**:
- <u>id</u> (主键)
- uid (外键)
- 学校id (外键)
- 真实姓名
- 性别
- 学生证
- 申请状态
- 备注

## 关系及基数

### 主要关系 (用菱形表示)

1. **拥有** {USER - USER_WX}
   - 基数: 1:N (一个用户可以有多个微信账户)
   - 连接: USER(1) ——— {拥有} ——— USER_WX(N)

2. **管理** {USER - SCHOOL}
   - 基数: 1:N (一个管理员可以管理多个学校)
   - 连接: USER(1) ——— {管理} ——— SCHOOL(N)

3. **下单** {USER_WX - ORDER_MAIN}
   - 基数: 1:N (一个用户可以下多个订单)
   - 连接: USER_WX(1) ——— {下单} ——— ORDER_MAIN(N)

4. **接单** {USER_WX - ORDER_MAIN}
   - 基数: 1:N (一个跑腿员可以接多个订单)
   - 连接: USER_WX(1) ——— {接单} ——— ORDER_MAIN(N)

5. **属于** {ORDER_MAIN - SCHOOL}
   - 基数: N:1 (多个订单属于一个学校)
   - 连接: ORDER_MAIN(N) ——— {属于} ——— SCHOOL(1)

6. **支付** {ORDER_MAIN - ORDER_PAYMENT}
   - 基数: 1:1 (一个订单对应一个支付记录)
   - 连接: ORDER_MAIN(1) ——— {支付} ——— ORDER_PAYMENT(1)

7. **进度** {ORDER_MAIN - ORDER_PROGRESS}
   - 基数: 1:1 (一个订单对应一个进度记录)
   - 连接: ORDER_MAIN(1) ——— {进度} ——— ORDER_PROGRESS(1)

8. **拥有钱包** {USER - WALLET}
   - 基数: 1:1 (一个用户对应一个钱包)
   - 连接: USER(1) ——— {拥有钱包} ——— WALLET(1)

9. **拥有地址** {USER - ADDRESS}
   - 基数: 1:N (一个用户可以有多个地址)
   - 连接: USER(1) ——— {拥有地址} ——— ADDRESS(N)

10. **产生流水** {ORDER_MAIN - CAPITAL_FLOW}
    - 基数: 1:N (一个订单可以产生多条资金流水)
    - 连接: ORDER_MAIN(1) ——— {产生流水} ——— CAPITAL_FLOW(N)

11. **申请跑腿** {USER - RUNNER_APPLY}
    - 基数: 1:N (一个用户可以有多次申请记录)
    - 连接: USER(1) ——— {申请跑腿} ——— RUNNER_APPLY(N)

## 绘制布局建议

```
[用户] ——— {拥有} ——— [微信用户] ——— {下单} ——— [订单] ——— {属于} ——— [学校]
  |                                        |                    |
  |                                        |                    |
{拥有钱包}                              {支付}               {管理}
  |                                        |                    |
[钱包]                                [订单支付]               |
  |                                        |                    |
{拥有地址}                              {进度}                 |
  |                                        |                    |
[地址]                                [订单进度]               |
  |                                        |                    |
{申请跑腿}                            {产生流水}               |
  |                                        |                    |
[跑腿申请] ——————————————————————————— [资金流水] ——————————————
```

## 绘制要点

1. **实体**: 用矩形框表示，实体名称居中
2. **属性**: 用椭圆形表示，主键属性加下划线
3. **关系**: 用菱形表示，关系名称居中
4. **连线**: 实体与关系用直线连接
5. **基数**: 在连线上标注1、N等基数
6. **外键**: 在属性中标注FK
7. **颜色**: 可以用不同颜色区分不同类型的元素

这个传统E-R图完整展示了福U跑腿系统的数据库设计，包含了所有核心实体、属性和关系。

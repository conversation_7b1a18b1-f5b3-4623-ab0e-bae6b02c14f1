package com.karrecy.payment.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.wxpay.sdk.WXPayUtil;
import com.karrecy.common.exception.PayException;
import com.karrecy.payment.config.WxPayConfig;
import com.karrecy.payment.domain.dto.PayOrderDTO;
import com.karrecy.payment.domain.vo.WxPayVO;
import com.karrecy.payment.service.WxPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManagerFactory;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 微信支付服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxPayServiceImpl implements WxPayService {

    private final WxPayConfig wxPayConfig;

    /**
     * 统一下单接口 - JSAPI支付（小程序支付）
     */
    @Override
    public WxPayVO createJsapiOrder(PayOrderDTO orderDTO, String openId, String clientIp) {
        try {
            // 构建统一下单请求参数 - 确保按照微信支付文档的要求设置所有必要字段
            Map<String, String> requestParams = new HashMap<>();
            requestParams.put("appid", wxPayConfig.getAppId());
            requestParams.put("mch_id", wxPayConfig.getMchId());
            // 使用UUID生成更标准的随机字符串
            requestParams.put("nonce_str", UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
            requestParams.put("body", orderDTO.getSubject());
            requestParams.put("out_trade_no", orderDTO.getOrderNo());
            
            // 确保支付金额至少为1分钱（微信支付的最低限额）
            Integer amount = orderDTO.getAmount();
            if (amount == null || amount < 1) {
                amount = 1; // 设置最小金额为1分钱
            }
            requestParams.put("total_fee", amount.toString());
            
            // 确保IP地址符合要求
            clientIp = (clientIp == null || clientIp.isEmpty()) ? "127.0.0.1" : clientIp;
            requestParams.put("spbill_create_ip", clientIp);
            requestParams.put("notify_url", wxPayConfig.getReturnUrl());
            requestParams.put("trade_type", "JSAPI");
            requestParams.put("openid", openId);
            
            // 确保签名使用的密钥正确，必要时手动调试密钥值
            String key = wxPayConfig.getKey();
            log.debug("使用密钥进行签名: {}", key);
            
            // 计算签名 - 使用官方SDK生成，避免自定义实现可能的错误
            String sign = WXPayUtil.generateSignature(requestParams, key);
            requestParams.put("sign", sign);

            // 将参数转换为XML格式
            String requestXml = WXPayUtil.mapToXml(requestParams);
            log.info("微信支付统一下单请求参数: {}", requestXml);

            // 发送请求
            String responseXml = postRequest(WxPayConfig.UNIFIED_ORDER_URL, requestXml);
            log.info("微信支付统一下单响应结果: {}", responseXml);

            // 解析响应结果
            Map<String, String> responseMap = WXPayUtil.xmlToMap(responseXml);
            if ("SUCCESS".equals(responseMap.get("return_code")) && "SUCCESS".equals(responseMap.get("result_code"))) {
                // 组装JSAPI支付参数 - 按照正确顺序构建，避免签名问题
                Map<String, String> payParams = new HashMap<>();
                payParams.put("appId", wxPayConfig.getAppId());
                payParams.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
                payParams.put("nonceStr", UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
                payParams.put("package", "prepay_id=" + responseMap.get("prepay_id"));
                payParams.put("signType", "MD5");
                
                // 使用正确的密钥生成签名
                String paySign = WXPayUtil.generateSignature(payParams, key);
                payParams.put("paySign", paySign);

                WxPayVO result = new WxPayVO();
                result.setAppId(payParams.get("appId"));
                result.setTimeStamp(payParams.get("timeStamp"));
                result.setNonceStr(payParams.get("nonceStr"));
                result.setPackageValue(payParams.get("package"));
                result.setSignType(payParams.get("signType"));
                result.setPaySign(payParams.get("paySign"));
                result.setOrderNo(orderDTO.getOrderNo());
                return result;
            } else {
                String errMsg = responseMap.get("err_code_des");
                if (StrUtil.isEmpty(errMsg)) {
                    errMsg = responseMap.get("return_msg");
                }
                throw new PayException("微信支付下单失败: " + errMsg);
            }
        } catch (Exception e) {
            log.error("微信支付下单异常", e);
            throw new PayException("微信支付下单异常: " + e.getMessage());
        }
    }

    /**
     * 查询订单状态
     */
    @Override
    public Map<String, String> queryOrderStatus(String orderNo) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("appid", wxPayConfig.getAppId());
            params.put("mch_id", wxPayConfig.getMchId());
            params.put("out_trade_no", orderNo);
            params.put("nonce_str", UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));

            // 计算签名 - 确保使用正确的密钥
            String sign = WXPayUtil.generateSignature(params, wxPayConfig.getKey());
            params.put("sign", sign);

            // 将参数转换为XML
            String requestXml = WXPayUtil.mapToXml(params);
            log.info("微信支付查询订单请求参数: {}", requestXml);

            // 发送请求
            String responseXml = postRequest(WxPayConfig.ORDER_QUERY_URL, requestXml);
            log.info("微信支付查询订单响应结果: {}", responseXml);

            // 解析响应
            return WXPayUtil.xmlToMap(responseXml);
        } catch (Exception e) {
            log.error("查询微信订单状态异常", e);
            throw new PayException("查询微信订单状态异常: " + e.getMessage());
        }
    }

    /**
     * 申请退款
     */
    @Override
    public Map<String, String> refund(String orderNo, String refundNo, Integer totalFee, Integer refundFee) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("appid", wxPayConfig.getAppId());
            params.put("mch_id", wxPayConfig.getMchId());
            params.put("nonce_str", UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));
            params.put("out_trade_no", orderNo);
            params.put("out_refund_no", refundNo);
            params.put("total_fee", String.valueOf(totalFee));
            params.put("refund_fee", String.valueOf(refundFee));
            params.put("notify_url", wxPayConfig.getRefundUrl());

            // 计算签名 - 确保使用正确的密钥
            String sign = WXPayUtil.generateSignature(params, wxPayConfig.getKey());
            params.put("sign", sign);

            // 将参数转换为XML
            String requestXml = WXPayUtil.mapToXml(params);
            log.info("微信支付申请退款请求参数: {}", requestXml);

            // 检查证书配置并尝试自动查找证书文件
            String certPath = wxPayConfig.getCertPath();
            File certFile = new File(certPath);
            
            if (!certFile.exists() || !certFile.canRead()) {
                log.warn("配置的证书文件不存在或不可读: {}", certPath);
                // 自动查找证书文件
                String foundCertPath = findCertificateFile();
                if (foundCertPath != null) {
                    // 复制证书到临时目录
                    File tempDir = new File("/tmp/wxpay_cert");
                    if (!tempDir.exists()) {
                        tempDir.mkdirs();
                    }
                    File tempCertFile = new File("/tmp/wxpay_cert/apiclient_cert.p12");
                    
                    try {
                        log.info("尝试复制证书文件: {} -> {}", foundCertPath, tempCertFile.getAbsolutePath());
                        copyFile(new File(foundCertPath), tempCertFile);
                        certPath = tempCertFile.getAbsolutePath();
                        certFile = tempCertFile;
                        log.info("成功找到并复制证书文件到: {}", certPath);
                    } catch (Exception e) {
                        log.error("复制证书文件失败", e);
                    }
                }
            }
            
            // 最终检查证书是否可用
            if (!certFile.exists()) {
                throw new PayException("商户API证书文件不存在: " + certPath + "，请确保文件已上传且路径正确");
            }

            // 发送请求时使用找到的或配置的证书路径
            wxPayConfig.setCertPath(certPath);
            
            // 发送请求
            String responseXml = postRequest(WxPayConfig.REFUND_URL, requestXml);
            log.info("微信支付申请退款响应结果: {}", responseXml);

            // 解析响应
            Map<String, String> resultMap = WXPayUtil.xmlToMap(responseXml);
            
            // 检查退款结果
            if ("SUCCESS".equals(resultMap.get("return_code")) && "SUCCESS".equals(resultMap.get("result_code"))) {
                log.info("退款申请成功，退款ID: {}", resultMap.get("refund_id"));
            } else {
                String returnMsg = resultMap.get("return_msg");
                String errCode = resultMap.get("err_code");
                String errCodeDes = resultMap.get("err_code_des");
                log.error("退款失败: return_msg={}, err_code={}, err_code_des={}", returnMsg, errCode, errCodeDes);
            }
            
            return resultMap;
        } catch (Exception e) {
            log.error("申请微信退款异常", e);
            throw new PayException("申请微信退款异常: " + e.getMessage());
        }
    }

    /**
     * 在系统中查找证书文件
     * @return 找到的证书路径，未找到则返回null
     */
    private String findCertificateFile() {
        String[] searchPaths = {
            "/cert/apiclient_cert.p12",
            "/data/cert/wx/apiclient_cert.p12",
            "/app/cert/wx/apiclient_cert.p12",
            "/tmp/cert/wx/apiclient_cert.p12",
            "/opt/app/backend/cert/apiclient_cert.p12",
            "/apiclient_cert.p12"
        };
        
        log.info("开始查找证书文件...");
        
        // 首先检查常见路径
        for (String path : searchPaths) {
            File file = new File(path);
            if (file.exists() && file.canRead()) {
                log.info("在预定义路径找到证书文件: {}", path);
                return path;
            }
        }
        
        // 如果常见路径没找到，尝试执行系统命令查找
        try {
            log.info("在预定义路径未找到证书，尝试系统搜索...");
            ProcessBuilder pb = new ProcessBuilder("find", "/", "-name", "apiclient_cert.p12", "-type", "f");
            pb.redirectErrorStream(true);
            Process process = pb.start();
            
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // 跳过/proc目录下的结果和错误消息
                    if (!line.startsWith("/proc") && !line.contains("Invalid argument") && !line.contains("Permission denied")) {
                        File file = new File(line);
                        if (file.exists() && file.canRead()) {
                            log.info("通过系统搜索找到证书文件: {}", line);
                            return line;
                        }
                    }
                }
            }
            
            process.waitFor(30, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("执行系统命令查找证书文件失败", e);
        }
        
        log.warn("未找到可用的证书文件");
        return null;
    }
    
    /**
     * 复制文件
     */
    private void copyFile(File source, File dest) throws IOException {
        try (FileInputStream fis = new FileInputStream(source);
             FileOutputStream fos = new FileOutputStream(dest)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                fos.write(buffer, 0, length);
            }
        }
        // 确保目标文件有正确的读权限
        dest.setReadable(true, false);
    }

    /**
     * 发送HTTP POST请求
     */
    private String postRequest(String url, String data) throws Exception {
        log.info("请求URL: {}", url);
        log.info("请求数据: {}", data);

        URL httpUrl = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) httpUrl.openConnection();
        
        // 添加证书支持（用于退款等安全接口）
        if (url.contains("secapi") && conn instanceof HttpsURLConnection) {
            HttpsURLConnection httpsConn = (HttpsURLConnection) conn;
            try {
                // 检查证书文件是否存在
                String certPath = wxPayConfig.getCertPath();
                log.info("使用证书路径: {}", certPath);
                
                File certFile = new File(certPath);
                if (!certFile.exists()) {
                    log.error("证书文件不存在: {}", certPath);
                    // 尝试找证书
                    String foundCertPath = findCertificateFile();
                    if (foundCertPath != null) {
                        certPath = foundCertPath;
                        certFile = new File(certPath);
                        log.info("找到替代证书: {}", certPath);
                    } else {
                        throw new Exception("商户API证书文件不存在: " + certPath);
                    }
                }
                
                // 加载证书
                char[] password = wxPayConfig.getMchId().toCharArray();
                KeyStore ks = KeyStore.getInstance("PKCS12");
                log.info("开始加载证书: {}", certPath);
                FileInputStream certStream = new FileInputStream(certFile);
                ks.load(certStream, password);
                certStream.close();
                
                // 实例化密钥库
                KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
                kmf.init(ks, password);
                
                // 创建SSLContext
                SSLContext sslContext = SSLContext.getInstance("TLS");
                sslContext.init(kmf.getKeyManagers(), null, new SecureRandom());
                
                // 设置SSL套接字工厂
                httpsConn.setSSLSocketFactory(sslContext.getSocketFactory());
                
                log.info("已成功配置商户API证书");
            } catch (Exception e) {
                log.error("配置商户API证书失败", e);
                throw e;
            }
        }

        conn.setRequestMethod("POST");
        conn.setRequestProperty("Accept", "application/xml");
        conn.setRequestProperty("Content-Type", "text/xml; charset=UTF-8");
        conn.setDoOutput(true);
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(10000);
        
        try (OutputStream os = conn.getOutputStream()) {
            os.write(data.getBytes(StandardCharsets.UTF_8));
        }

        StringBuilder sb = new StringBuilder();
        try {
            int responseCode = conn.getResponseCode();
            if (responseCode != 200) {
                // 处理错误响应
                BufferedReader errorReader = new BufferedReader(
                    new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8));
                String errorLine;
                StringBuilder errorResponse = new StringBuilder();
                while ((errorLine = errorReader.readLine()) != null) {
                    errorResponse.append(errorLine);
                }
                errorReader.close();
                
                log.error("HTTP请求失败，响应码: {}，错误信息: {}", responseCode, errorResponse.toString());
                throw new Exception("HTTP请求失败，响应码: " + responseCode + 
                    "，详细信息: " + errorResponse.toString());
            }
            
            BufferedReader in = new BufferedReader(
                new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                sb.append(line);
            }
            in.close();
        } catch (Exception e) {
            log.error("HTTP请求异常", e);
            throw new Exception("HTTP请求异常: " + e.getMessage());
        }
        
        return sb.toString();
    }
} 
<template>
	 <nut-navbar  fixed="true" placeholder="true" class="custom-navbar">
	    <template #left>
			<div @click="goToSelectSchool" class="school-selector">
			  <span class="school-name">{{ currSchool.name }}</span>
			  <nut-icon name="triangle-down" size="12"/>
			</div>
	    </template>
	  </nut-navbar>
	  
	  <!-- 轮播图独立容器，使用text-align居中 -->
	  <view class="swiper-text-center-container">
		  <view class="swiper-inline-block">
			  <nut-swiper class="custom-swiper" :init-page="page" :pagination-visible="true" pagination-color="#4F7DF5" auto-play="3000">
			   <nut-swiper-item v-for="item in list" :key="item" class="swiper-item-center">
					  <img :src="item" alt="" class="swiper-image"
					       style="width: 100% !important; height: 386rpx !important; display: block !important; object-fit: cover !important; border-radius: 24rpx !important;" />
				</nut-swiper-item>
			</nut-swiper>
		  </view>
	  </view>

	  <view class="container">
		  <view class="service-grid">
			<nut-grid :gutter="16" :column-num="2" direction="horizontal">
				<nut-grid-item @click="toOrderQusong" class="grid-item">
					<view class="grid-card">
						<nut-icon font-class-name="gridIcon" name="/static/index/qu.jpg" />
						<text class="grid-text">帮取</text>
					</view>
				</nut-grid-item>
				<nut-grid-item @click="toOrderQusong" class="grid-item">
					<view class="grid-card">
						<nut-icon font-class-name="gridIcon" name="/static/index/song.jpg" />
						<text class="grid-text">帮送</text>
					</view>
				</nut-grid-item>
				<nut-grid-item @click="toOrderBangmai" class="grid-item">
					<view class="grid-card">
						<nut-icon font-class-name="gridIcon" name="/static/index/mai.jpg" />
						<text class="grid-text">帮买</text>
					</view>
				</nut-grid-item>
				<nut-grid-item @click="toOrderWanneg" class="grid-item">
					<view class="grid-card">
						<nut-icon font-class-name="gridIcon" name="/static/index/wan.jpg" />
						<text class="grid-text">万能</text>
					</view>
				</nut-grid-item>
	  </nut-grid>
		  </view>
	  </view>

  <!-- 订阅消息引导弹窗 -->
  <nut-dialog
    v-model:visible="showSubscribeDialog"
    title="接收订单通知"
    content="为了及时通知您订单状态变更、补差价等重要信息，请允许接收订阅消息"
    @cancel="onDialogCancel"
    @ok="requestSubscription"
    cancel-text="暂不需要"
    ok-text="立即开启"
  ></nut-dialog>

  <nut-toast></nut-toast>
   <nut-notify></nut-notify>
  
</template>
<script>
	import { getCarousel } from "@/request/apis/login.js"
	import { toRaw } from "vue";
	export default {
		data() {
			return {
				// 订阅消息模板ID
				ORDER_STATUS_TEMPLATE_ID: 'j8Z2M2fdVyAAMsGcJXPWAXSfu3YxexvNMLM_1Dtp9-c',
				MESSAGE_TEMPLATE_ID: 'nR0D0mjOJmS74O1eGsI0oncXXRcQg0lSbSSrucYQMNA',
				PRICE_ADJUSTMENT_TEMPLATE_ID: 'p0THQpfjhGnJguDx1XPKlMfZLCqAMRJC3OflL6WLYws',
				// 是否显示订阅消息对话框
				showSubscribeDialog: false,
				list:[
            'https://pic1.zhimg.com/80/v2-f24ff4c534347c07ee0d528e0fe33f9d.jpg',
            'https://gss0.baidu.com/9vo3dSag_xI4khGko9WTAnF6hhy/zhidao/pic/item/a8014c086e061d95b3645b8d75f40ad162d9ca61.jpg',
          ],
				title: 'Hello',
				currSchool: {
					name:'选择校区'
				},
				orderServers:[]
			}
		},
		watch:{
			"$store.state.appLaunch": function(val, oldval) {
					console.log(val,oldval);
					if(val) {
						this.initSchool()
					}
			}
		},
		mounted() {
			console.log("mounted");
			this.initSchool()
			
		},
		onLoad() {
			console.log("index onLoad");
			getCarousel().then(res => {
				this.list = res.data
			})
			const checkOperationStatus = setInterval(() => {
			  if (this.$store.state.appLaunch) {
				  this.initSchool()
				  // 检查用户登录状态后显示订阅消息对话框
				  setTimeout(() => {
					  this.checkAndShowSubscribeDialog()
				  }, 1500); // 延迟1.5秒显示，避免与其他初始化对话框冲突
				clearInterval(checkOperationStatus);
				console.log('首页的js文件中的代码执行');
			  }
			}, 100); // 每100毫秒检查一次状态变化
			
		},
		onShow() {
			
		},
		methods: {
			// 检查并显示订阅消息对话框
			checkAndShowSubscribeDialog() {
				// 检查是否已经显示过订阅消息对话框
				let hasShownSubscribe = uni.getStorageSync('hasShownSubscribeDialog');
				if (!hasShownSubscribe) {
					// 首次打开应用，显示订阅对话框
					this.showSubscribeDialog = true;
				} else {
					// 不是首次，检查上次提示时间
					let lastSubscribeTime = uni.getStorageSync('lastSubscribeTime');
					let now = new Date().getTime();
				}
			},
			
			// 用户点击取消订阅对话框
			onDialogCancel() {
				// 标记已显示过订阅对话框
				uni.setStorageSync('hasShownSubscribeDialog', true);
				uni.setStorageSync('lastSubscribeTime', new Date().getTime());
				this.showSubscribeDialog = false;
			},
			
			// 请求订阅消息权限
			requestSubscription() {
				if (wx.requestSubscribeMessage) {
					wx.requestSubscribeMessage({
						tmplIds: [
							this.ORDER_STATUS_TEMPLATE_ID, 
							this.MESSAGE_TEMPLATE_ID, 
							this.PRICE_ADJUSTMENT_TEMPLATE_ID
						],
						success: (res) => {
							console.log('订阅结果:', res);
							// 标记已显示过订阅对话框
							uni.setStorageSync('hasShownSubscribeDialog', true);
							uni.setStorageSync('lastSubscribeTime', new Date().getTime());
							
							// 统计接受的模板
							let acceptedTemplates = [];
							for (let tmplId in res) {
								if (res[tmplId] === 'accept') {
									acceptedTemplates.push(tmplId);
								}
							}
							
							// 如果有接受的模板，向后端报告
							if (acceptedTemplates.length > 0) {
								this.reportSubscription(acceptedTemplates);
								uni.showToast({
									title: '订阅成功，将为您推送重要通知',
									icon: 'none',
									duration: 2000
								});
							} else {
								uni.showToast({
									title: '您已拒绝接收通知，将无法收到重要信息',
									icon: 'none',
									duration: 2000
								});
							}
						},
						fail: (err) => {
							console.log('订阅请求失败:', err);
							// 标记已显示过订阅对话框，避免频繁弹窗
							uni.setStorageSync('hasShownSubscribeDialog', true);
							uni.setStorageSync('lastSubscribeTime', new Date().getTime());
							
							uni.showToast({
								title: '订阅失败，您可在"我的"页面重新开启通知',
								icon: 'none',
								duration: 2000
							});
						}
					});
				} else {
					uni.showToast({
						title: '当前微信版本不支持订阅消息，请升级微信',
						icon: 'none',
						duration: 2000
					});
					// 标记已显示过
					uni.setStorageSync('hasShownSubscribeDialog', true);
					uni.setStorageSync('lastSubscribeTime', new Date().getTime());
				}
				this.showSubscribeDialog = false;
			},
			
			// 向后端报告订阅状态
			reportSubscription(templateIds) {
				let baseUrl = this.$baseUrl;
				if (!baseUrl) {
					baseUrl = 'https://api.daidaida.xyz';
				}
				
				const token = uni.getStorageSync('token');
				
				templateIds.forEach(templateId => {
					uni.request({
						url: baseUrl + '/wx/subscription/record',
						method: 'POST',
						header: {
							'Authorization': 'Bearer ' + token,
							'Content-Type': 'application/x-www-form-urlencoded'
						},
						data: 'templateId=' + templateId,
						success: function(res) {
							console.log('订阅状态记录成功:', res);
						},
						fail: function(err) {
							console.error('订阅状态记录失败:', err);
						}
					});
				});
			},
			toLogin() {
				uni.navigateTo({
					url:'/pages/API/login/login'
				})
			},
			toChat() {
				
				uni.navigateTo({
					url:"/pages/API/chat/chat"
				})
			},
			toDetail() {
				uni.navigateTo({
					url:"/pages/API/order/detail/detail"
				})
			},
			test() {
				const SUBSCRIBE_ID = 'nR0D0mjOJmS74O1eGsI0oncXXRcQg0lSbSSrucYQMNA' // 模板ID
				let that = this;
				    if (wx.requestSubscribeMessage) {
				      wx.requestSubscribeMessage({
				        tmplIds: [SUBSCRIBE_ID],
				        success(res) {
				          if (res[SUBSCRIBE_ID] === 'accept') {
				            // 用户主动点击同意...do something
				          } else if (res[SUBSCRIBE_ID] === 'reject') {
				            // 用户主动点击拒绝...do something
				          } else {
				            wx.showToast({
				              title: '授权订阅消息有误',
				              icon: 'none'
				            })
				          }
				        },
				        fail(res) {
				          // 20004:用户关闭了主开关，无法进行订阅,引导开启
				          if (res.errCode == 20004) {
				          	// 显示引导设置弹窗
				            that.setData({
				              isShowSetModel: true
				            })
				          }else{
				          	// 其他错误信息码，对应文档找出原因
				            wx.showModal({
				              title: '提示',
				              content: res.errMsg,
				              showCancel: false
				            })
				          }
				        }
				      });
				    } else {
				      wx.showModal({
				        title: '提示',
				        content: '请更新您微信版本，来获取订阅消息功能',
				        showCancel: false
				      })
				    }
			},
			toOrderQusong() {
				uni.navigateTo({
					url:"/pages/API/order/qusong/qusong"
				})
			},
			toOrderBangmai() {
				uni.navigateTo({
					url:"/pages/API/order/bangmai/bangmai"
				})
			},
			toOrderWanneg() {
				uni.navigateTo({
					url:"/pages/API/order/wanneng/wanneng"
				})
			},
			toOrderSubmit(){
				// uni.navigateTo({
				// 	url:"/pages/API/order/qusong/qusong"
				// })
				// uni.navigateTo({
				// 	url:"/pages/API/order/bangmai/bangmai"
				// })
				uni.navigateTo({
					url:"/pages/API/order/wanneng/wanneng"
				})
				// uni.navigateTo({
				// 	url:"/pages/API/order/test/test"
				// })
			},
			initSchool(){
				let currSchool = this.$store.state.currSchool;
				console.log(currSchool);
				if(currSchool == null) {
					
				}
				else {
					this.loadSchoolByStore();
				}
			},
			loadSchoolByStore(){
				this.currSchool = toRaw(this.$store.state.currSchool);
				console.log(this.currSchool);
			},
			goToSelectSchool(){
				uni.navigateTo({
					url:"/pages/API/school/select/select"
				})
			}
		},
		
	}
</script>
<style lang="scss" scoped>
// 页面背景色
page {
  background-color: #f7f9ff;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.container {
  padding: 0 16rpx 32rpx;
}

// 自定义导航栏
.custom-navbar {
  :deep(.nut-navbar__title) {
    color: #4F7DF5;
    font-weight: 600;
  }

  :deep(.nut-navbar__left) {
    padding-left: 24rpx;
  }
}

// 学校选择器
.school-selector {
  display: flex;
  align-items: center;
  background-color: rgba(79, 125, 245, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;

  .school-name {
    max-width: 160rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 28rpx;
    font-weight: 500;
    color: #4F7DF5;
    margin-right: 8rpx;
}

  :deep(.nut-icon) {
    color: #4F7DF5;
  }
}

// 轮播图样式 - 绝对居中
.custom-swiper {
  border-radius: 24rpx !important;
  overflow: hidden !important;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08) !important;
  display: block !important;

  .swiper-image {
    width: 100% !important;
    height: 386rpx !important; /* 16:9比例：考虑padding后实际宽度686rpx对应386rpx高度 */
    border-radius: 24rpx;
    object-fit: cover; /* 保持cover确保图片填满容器 */
    display: block !important;
  }

  /* 强制覆盖微信小程序wx-image的默认样式 */
  :deep(wx-image) {
    width: 100% !important;
    height: 386rpx !important;
    display: block !important;
  }

  /* 针对image标签的强制样式覆盖 */
  .swiper-item-center image,
  .swiper-item-center img {
    width: 100% !important;
    height: 386rpx !important;
    display: block !important;
    object-fit: cover !important;
    border-radius: 24rpx !important;
  }

  :deep(.nut-swiper-pagination-bullet) {
    width: 16rpx;
    height: 16rpx;
    opacity: 0.6;
    background: #fff;
    border-radius: 8rpx;
    transition: all 0.3s;
  }

  :deep(.nut-swiper-pagination-bullet-active) {
    width: 32rpx;
    opacity: 1;
    background: #4F7DF5;
  }

  /* 强制覆盖NutUI所有可能的偏移样式 - 确保填满宽度 */
  :deep(.nut-swiper) {
    width: 100% !important;
    height: 386rpx !important; /* 16:9比例：考虑padding后实际宽度686rpx对应386rpx高度 */
    margin: 0 !important;
    padding: 0 !important;
    position: relative !important;
    box-sizing: border-box !important;
  }

  /* 强制覆盖轮播图内所有图片元素的样式 */
  :deep(.nut-swiper) wx-image,
  :deep(.nut-swiper) image,
  :deep(.nut-swiper) img {
    width: 100% !important;
    height: 386rpx !important;
    display: block !important;
    object-fit: cover !important;
    border-radius: 24rpx !important;
  }

  :deep(.nut-swiper-inner) {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }

  :deep(.nut-swiper-item) {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important;
  }

  :deep(.nut-swiper-wrapper) {
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }

  /* 优化分页器位置 */
  :deep(.nut-swiper-pagination) {
    bottom: 16rpx;
    text-align: center;
  }
}

/* 轮播项居中样式 */
.swiper-item-center {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 轮播图居中容器 - 修复右边空白问题 */
.swiper-text-center-container {
  width: 100%;
  padding: 32rpx;
  background-color: #f7f9ff;
}

.swiper-inline-block {
  width: 100%;

  .custom-swiper {
    width: 100% !important;
    margin: 0 !important;
    display: block !important;
  }
}

/* 确保轮播图填满容器宽度 */
.swiper-text-center-container :deep(.nut-swiper) {
  margin: 0 !important;
  width: 100% !important;
}

/* 全局强制覆盖微信小程序wx-image默认样式 */
.swiper-text-center-container wx-image {
  width: 100% !important;
  height: 386rpx !important;
  display: block !important;
  object-fit: cover !important;
  border-radius: 24rpx !important;
}

.swiper-text-center-container image {
  width: 100% !important;
  height: 386rpx !important;
  display: block !important;
  object-fit: cover !important;
  border-radius: 24rpx !important;
}

// 服务网格
.service-grid {
  margin-top: 48rpx;

  :deep(.nut-grid) {
    padding: 16rpx 0;
  }

  .grid-item {
    padding: 12rpx;
  }

  .grid-card {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 32rpx 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
      transition: all 0.3s ease;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

      &:active {
      transform: scale(0.98);
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      }

    .gridIcon {
      width: 120rpx !important;
      height: 120rpx !important;
      margin-bottom: 16rpx;
      border-radius: 20rpx;
    }

    .grid-text {
      font-size: 32rpx;
      font-weight: 600;
        color: #333;
      margin-top: 16rpx;
    }
  }
}
</style>

<!-- 全局样式，强制覆盖微信小程序默认样式 -->
<style>
/* 使用全局样式强制覆盖wx-image默认样式 */
.swiper-text-center-container wx-image {
  width: 100% !important;
  height: 386rpx !important;
  display: block !important;
  object-fit: cover !important;
  border-radius: 24rpx !important;
}

.swiper-text-center-container image {
  width: 100% !important;
  height: 386rpx !important;
  display: block !important;
  object-fit: cover !important;
  border-radius: 24rpx !important;
}

/* 更高优先级的选择器 */
.custom-swiper wx-image,
.custom-swiper image,
.custom-swiper img {
  width: 100% !important;
  height: 386rpx !important;
  display: block !important;
  object-fit: cover !important;
  border-radius: 24rpx !important;
}
</style>
# 生产环境部署教程（1Panel面板 + OpenResty + fuu-run全栈项目）

> 本文档适用于基于 1Panel 面板，Web 服务器为 OpenResty，部署 fuu-run-master 项目（含 daidaida-run、daidaida-run-admin、daidaida-run-uni）到生产环境。

---

## 1. 环境准备

### 1.1 服务器要求
- 推荐系统：**Ubuntu 20.04 LTS** 或 **CentOS 7/8**
- 已安装 1Panel 面板（https://1panel.cn/）
- 1Panel 已安装 OpenResty（代替 Nginx）、MySQL、Redis、Docker（如需容器化）
- 已开放端口：80（http）、443（https）、3306（MySQL）、7001/7002（如需 SSL 证书管理）、后端端口（如 8081）、WebSocket 端口（如 4400）
- 已有域名并解析到服务器公网IP

### 1.2 本地准备
- 前端、后端、uni-app 小程序代码均已在本地开发环境运行通过
- 具备上传文件到服务器的能力（如 SFTP、1Panel 文件管理、scp 等）

---

## 2. 项目打包

### 2.1 前端（daidaida-run-admin）打包
```bash
cd /path/to/daidaida-run-admin
npm install
npm run build
# 生成 dist 目录，dist 即为生产环境静态资源
```

### 2.2 后端（daidaida-run）打包
```bash
cd /path/to/daidaida-run
# 使用 Maven 打包
mvn clean package -DskipTests
# 生成 target/fuu-admin.jar（或类似名 jar 包）
```

### 2.3 uni-app 小程序（daidaida-run-uni）
- 通过 HBuilderX 打包为对应平台的小程序包（如微信/支付宝/抖音等），不参与服务器部署。

---

## 3. 服务器部署目录结构建议

```
/opt/app/
├── backend/                 # 后端相关
│   ├── fuu-admin.jar        # SpringBoot jar 包
│   └── logs/                # 日志目录
├── frontend/                # 前端相关
│   ├── dist/                # 前端静态资源
│   └── ssl/                 # SSL 证书（如需 https）
└── ...
```

---

## 4. 数据库与Redis配置

### 4.1 MySQL
- 通过 1Panel 安装 MySQL，创建数据库 `fuudb`，设置好 root 密码。
- 记下数据库连接信息，后端配置需用。

### 4.2 Redis
- 通过 1Panel 安装 Redis，记下连接信息。

---

## 5. 后端部署（SpringBoot jar 方式）

### 5.1 上传 jar 包
- 将 `fuu-admin.jar` 上传到 `/opt/app/backend/` 目录。

### 5.2 配置 application-prod.yml
- 修改 jar 包内的 `application-prod.yml`（或外部挂载），主要配置数据库、Redis、OSS、端口等。
- 关键配置示例：

```yaml
server:
  port: 8081
spring:
  datasource:
    url: *****************************************************************************************************
    username: root
    password: 你的数据库密码
  redis:
    host: 127.0.0.1
    port: 6379
    password: 你的redis密码（如有）
# OSS配置（如用本地、阿里云、MinIO等，按实际填写）
```

### 5.3 启动后端服务
- 点击网站-运行环境-Java，创建运行环境。
启动命令：
```bash
java --add-opens java.base/java.lang=ALL-UNNAMED -jar daidaida-admin.jar
- 应用端口设置为：
- 8081-8081
- 4400-4400
```

## 6. 前端部署（OpenResty 静态资源）

### 6.1 上传 dist 目录
- 将 `daidaida-run-admin/dist` 上传到 `/opt/app/frontend/dist/`

### 6.2 配置 OpenResty 站点
- 在 1Panel 面板新建站点，类型选择 OpenResty。
- 站点根目录指向 `/opt/app/frontend/dist`
- 绑定你的域名。

### 6.3 配置反向代理（API转发）
- 在 1Panel 的 OpenResty 站点设置中，添加反向代理规则，将 `/dev/` 路径代理到后端服务。
- 参考配置：

```nginx
location /dev/ {
    proxy_pass http://127.0.0.1:8081/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    # CORS 支持
    add_header Access-Control-Allow-Origin '*';
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS, PUT, DELETE';
    add_header Access-Control-Allow-Headers '*';
}
```

### 6.4 配置 WebSocket
```nginx
location /ws/ {
    proxy_pass http://127.0.0.1:4400/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_read_timeout 3600s;
    proxy_send_timeout 3600s;
}
```

### 6.5 配置 HTTPS（如需）
- 在 1Panel 面板申请 SSL 证书，配置到 OpenResty 站点。
- 证书文件一般放在 `/opt/app/frontend/ssl/`，在站点设置中指定证书路径。

---

## 7. 其他注意事项

- **端口安全**：确保 8081、4400 等后端端口仅本地可访问，前端通过 OpenResty 代理访问。
- **环境变量**：如需用到环境变量，可在 1Panel 的服务管理中设置。
- **日志管理**：建议将日志输出到 `/opt/app/backend/logs/`，便于排查问题。
- **定时任务/守护进程**：可用 1Panel 的进程守护功能，或用 systemd/service/pm2 等方式保证服务稳定。

---

## 8. 常见问题排查

- **前端页面无法访问**：检查 dist 目录是否上传完整，OpenResty 站点根目录是否正确。
- **API 404/502**：检查反向代理配置，后端服务是否启动，端口是否一致。
- **WebSocket 无法连接**：检查 ws 代理配置，后端端口是否开放。
- **HTTPS 证书问题**：检查证书路径、权限、域名是否一致。
- **数据库/Redis 连接失败**：检查配置、端口、密码、服务是否正常。

---

## 9. 其它
- 小程序（daidaida-run-uni）无需部署到服务器，打包后上传到对应小程序平台即可。
- 如需 Docker 部署，可参考原有 docker-compose 方案，1Panel 也支持容器管理。

---

如有特殊需求或遇到问题，请提供具体报错或截图，便于进一步协助。

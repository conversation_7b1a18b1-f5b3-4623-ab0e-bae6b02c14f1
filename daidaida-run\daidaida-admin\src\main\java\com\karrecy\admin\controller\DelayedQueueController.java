package com.karrecy.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.karrecy.admin.config.DelayedQueueConfig;
import com.karrecy.common.constant.QueueNames;
import com.karrecy.common.core.domain.R;
import com.karrecy.common.enums.OrderStatus;
import com.karrecy.common.utils.redis.QueueUtils;
import com.karrecy.order.domain.po.OrderMain;
import com.karrecy.order.domain.po.OrderProgress;
import com.karrecy.order.service.IOrderMainService;
import com.karrecy.order.service.IOrderProgressService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 延迟队列 演示案例
 * <p>
 * 轻量级队列 重量级数据量 请使用 MQ
 * 例如: 创建订单30分钟后过期处理
 * <p>
 * 集群测试通过 同一个数据只会被消费一次 做好事务补偿
 * 集群测试流程 两台集群分别开启订阅 在其中一台发送数据 观察接收消息的规律
 *
 * <AUTHOR> Li
 * @version 3.6.0
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/demo/queue/delayed")
public class DelayedQueueController {

    private final IOrderMainService orderMainService;
    private final IOrderProgressService orderProgressService;
    private final DelayedQueueConfig delayedQueueConfig;

    /**
     * 订阅队列
     *
     * @param queueName 队列名
     */
    @GetMapping("/subscribe")
    public R<Void> subscribe(String queueName) {
        log.info("通道: {} 监听中......", queueName);
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(queueName, (String orderNum) -> {
            // 观察接收时间
            log.info("通道: {}, 收到数据: {}", queueName, orderNum);
        }, true);
        return R.ok("操作成功");
    }

    /**
     * 添加队列数据
     *
     * @param queueName 队列名
     * @param orderNum  订单号
     * @param time      延迟时间(秒)
     */
    @GetMapping("/add")
    public R<Void> add(String queueName, String orderNum, Long time) {
        QueueUtils.addDelayedQueueObject(queueName, orderNum, time, TimeUnit.SECONDS);
        // 观察发送时间
        log.info("通道: {} , 发送数据: {}", queueName, orderNum);
        return R.ok("操作成功");
    }

    /**
     * 删除队列数据
     *
     * @param queueName 队列名
     * @param orderNum  订单号
     */
    @GetMapping("/remove")
    public R<Void> remove(String queueName, String orderNum) {
        if (QueueUtils.removeDelayedQueueObject(queueName, orderNum)) {
            log.info("通道: {} , 删除数据: {}", queueName, orderNum);
        } else {
            return R.fail("操作失败");
        }
        return R.ok("操作成功");
    }

    /**
     * 销毁队列
     *
     * @param queueName 队列名
     */
    @GetMapping("/destroy")
    public R<Void> destroy(String queueName) {
        // 用完了一定要销毁 否则会一直存在
        QueueUtils.destroyDelayedQueue(queueName);
        return R.ok("操作成功");
    }

    /**
     * 诊断已送达但未自动完成的订单
     */
    @GetMapping("/diagnose/delivered")
    @SaCheckPermission("system:queue:diagnose")
    public R<Map<String, Object>> diagnoseDeliveredOrders() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询所有已送达状态的订单
            List<OrderMain> deliveredOrders = orderMainService.list(
                new LambdaQueryWrapper<OrderMain>()
                    .eq(OrderMain::getStatus, OrderStatus.DELIVERED.getCode())
                    .orderByDesc(OrderMain::getCreateTime)
            );

            List<Map<String, Object>> problemOrders = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (OrderMain order : deliveredOrders) {
                // 获取订单进度信息
                OrderProgress progress = orderProgressService.getById(order.getId());
                if (progress != null && progress.getDeliveredTime() != null) {
                    // 计算送达时间到现在的小时数
                    long hoursFromDelivered = ChronoUnit.HOURS.between(progress.getDeliveredTime(), now);

                    if (hoursFromDelivered >= 24) {
                        Map<String, Object> orderInfo = new HashMap<>();
                        orderInfo.put("orderId", order.getId());
                        orderInfo.put("tag", order.getTag());
                        orderInfo.put("status", order.getStatus());
                        orderInfo.put("deliveredTime", progress.getDeliveredTime());
                        orderInfo.put("hoursFromDelivered", hoursFromDelivered);
                        orderInfo.put("shouldAutoComplete", true);
                        problemOrders.add(orderInfo);
                    }
                }
            }

            result.put("totalDeliveredOrders", deliveredOrders.size());
            result.put("problemOrdersCount", problemOrders.size());
            result.put("problemOrders", problemOrders);
            result.put("checkTime", now);

            log.info("诊断完成：总共{}个已送达订单，其中{}个超过24小时未自动完成",
                    deliveredOrders.size(), problemOrders.size());

        } catch (Exception e) {
            log.error("诊断已送达订单失败", e);
            return R.fail("诊断失败：" + e.getMessage());
        }

        return R.ok(result);
    }

    /**
     * 手动触发订单自动完成
     */
    @GetMapping("/trigger/autoComplete")
    @SaCheckPermission("system:queue:trigger")
    public R<Void> triggerAutoComplete(Long orderId) {
        try {
            // 检查订单状态
            OrderMain order = orderMainService.getById(orderId);
            if (order == null) {
                return R.fail("订单不存在");
            }

            if (!OrderStatus.DELIVERED.getCode().equals(order.getStatus())) {
                return R.fail("订单状态不是已送达，无法触发自动完成");
            }

            // 手动添加到延迟队列，立即执行
            QueueUtils.addDelayedQueueObject(
                QueueNames.ORDER_DELIVERED_AUTO_COMPLETE,
                orderId.toString(),
                1,
                TimeUnit.SECONDS
            );

            log.info("手动触发订单自动完成，订单ID: {}", orderId);
            return R.ok("触发成功，订单将在1秒后自动完成");

        } catch (Exception e) {
            log.error("手动触发订单自动完成失败，订单ID: {}", orderId, e);
            return R.fail("触发失败：" + e.getMessage());
        }
    }

    /**
     * 测试延迟队列是否正常工作
     */
    @GetMapping("/test/delayedQueue")
    @SaCheckPermission("system:queue:test")
    public R<Void> testDelayedQueue() {
        try {
            String testMessage = "test-" + System.currentTimeMillis();

            // 添加一个5秒后执行的测试任务
            QueueUtils.addDelayedQueueObject(
                "TEST_QUEUE",
                testMessage,
                5,
                TimeUnit.SECONDS
            );

            // 订阅测试队列
            QueueUtils.subscribeBlockingQueue("TEST_QUEUE", message -> {
                log.info("延迟队列测试成功，收到消息: {}", message);
            }, false);

            log.info("延迟队列测试任务已添加，消息: {}", testMessage);
            return R.ok("测试任务已添加，请查看日志确认5秒后是否收到消息");

        } catch (Exception e) {
            log.error("延迟队列测试失败", e);
            return R.fail("测试失败：" + e.getMessage());
        }
    }

    /**
     * 批量修复超时未自动完成的订单
     */
    @GetMapping("/fix/overdueOrders")
    @SaCheckPermission("system:queue:fix")
    public R<Map<String, Object>> fixOverdueOrders() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询所有已送达状态的订单
            List<OrderMain> deliveredOrders = orderMainService.list(
                new LambdaQueryWrapper<OrderMain>()
                    .eq(OrderMain::getStatus, OrderStatus.DELIVERED.getCode())
                    .orderByDesc(OrderMain::getCreateTime)
            );

            int totalOrders = deliveredOrders.size();
            int fixedCount = 0;
            List<Long> fixedOrderIds = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (OrderMain order : deliveredOrders) {
                try {
                    // 获取订单进度信息
                    OrderProgress progress = orderProgressService.getById(order.getId());
                    if (progress != null && progress.getDeliveredTime() != null) {
                        // 计算送达时间到现在的小时数
                        long hoursFromDelivered = ChronoUnit.HOURS.between(progress.getDeliveredTime(), now);

                        // 如果超过24小时，立即加入延迟队列执行自动完成
                        if (hoursFromDelivered >= 24) {
                            QueueUtils.addDelayedQueueObject(
                                QueueNames.ORDER_DELIVERED_AUTO_COMPLETE,
                                order.getId().toString(),
                                1,
                                TimeUnit.SECONDS
                            );

                            fixedCount++;
                            fixedOrderIds.add(order.getId());
                            log.info("已修复超时订单，订单ID: {}, 送达时间: {}, 已过去{}小时",
                                    order.getId(), progress.getDeliveredTime(), hoursFromDelivered);
                        }
                    }
                } catch (Exception e) {
                    log.error("修复订单{}时发生异常: {}", order.getId(), e.getMessage());
                }
            }

            result.put("totalDeliveredOrders", totalOrders);
            result.put("fixedOrdersCount", fixedCount);
            result.put("fixedOrderIds", fixedOrderIds);
            result.put("fixTime", now);

            log.info("批量修复完成：总共{}个已送达订单，修复了{}个超时订单", totalOrders, fixedCount);
            return R.ok(result);

        } catch (Exception e) {
            log.error("批量修复超时订单失败", e);
            result.put("error", e.getMessage());
            return R.fail("批量修复失败", result);
        }
    }

    /**
     * 检查Redis延迟队列状态
     */
    @GetMapping("/debug/queueStatus")
    @SaCheckPermission("system:queue:debug")
    public R<Map<String, Object>> debugQueueStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            String queueName = QueueNames.ORDER_DELIVERED_AUTO_COMPLETE;

            // 检查普通队列
            Object normalQueueData = QueueUtils.getQueueObject(queueName);
            result.put("normalQueueData", normalQueueData);

            // 检查延迟队列
            Object delayedQueueData = QueueUtils.getDelayedQueueObject(queueName);
            result.put("delayedQueueData", delayedQueueData);

            // 手动添加一个测试消息到延迟队列
            String testOrderId = "TEST_ORDER_" + System.currentTimeMillis();
            QueueUtils.addDelayedQueueObject(queueName, testOrderId, 3, TimeUnit.SECONDS);
            result.put("testOrderAdded", testOrderId);

            log.info("Redis队列状态检查完成，测试订单已添加: {}", testOrderId);
            return R.ok(result);

        } catch (Exception e) {
            log.error("检查Redis队列状态失败", e);
            result.put("error", e.getMessage());
            return R.fail("检查失败", result);
        }
    }

    /**
     * 诊断延迟队列监听器状态
     */
    @GetMapping("/diagnose/listenerStatus")
    @SaCheckPermission("system:queue:diagnose")
    public R<Map<String, Object>> diagnoseListenerStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            String queueName = QueueNames.ORDER_DELIVERED_AUTO_COMPLETE;

            // 获取Redis客户端
            RedissonClient client = QueueUtils.getClient();
            RBlockingQueue<String> queue = client.getBlockingQueue(queueName);
            RDelayedQueue<String> delayedQueue = client.getDelayedQueue(queue);

            // 检查队列大小
            result.put("queueSize", queue.size());
            result.put("delayedQueueSize", delayedQueue.size());

            // 检查队列是否存在
            result.put("queueExists", queue.isExists());

            // 添加测试消息并检查是否能被消费
            String testMessage = "LISTENER_TEST_" + System.currentTimeMillis();
            log.info("添加测试消息到延迟队列: {}", testMessage);

            // 添加一个5秒后执行的测试消息
            QueueUtils.addDelayedQueueObject(queueName, testMessage, 5, TimeUnit.SECONDS);
            result.put("testMessageAdded", testMessage);
            result.put("testMessageDelay", "5秒");

            // 等待一小段时间检查队列状态
            Thread.sleep(1000);
            result.put("queueSizeAfterAdd", queue.size());
            result.put("delayedQueueSizeAfterAdd", delayedQueue.size());

            log.info("延迟队列监听器状态诊断完成，测试消息: {}", testMessage);
            return R.ok(result);

        } catch (Exception e) {
            log.error("诊断延迟队列监听器状态失败", e);
            result.put("error", e.getMessage());
            return R.fail("诊断失败", result);
        }
    }

    /**
     * 正确订阅延迟队列监听器
     */
    @GetMapping("/subscribe/delayedQueue")
    @SaCheckPermission("system:queue:subscribe")
    public R<Void> subscribeDelayedQueue() {
        try {
            String queueName = QueueNames.ORDER_DELIVERED_AUTO_COMPLETE;
            log.info("正确订阅延迟队列监听器: {}", queueName);

            // 使用正确的参数订阅延迟队列
            QueueUtils.subscribeBlockingQueue(queueName, (String orderNum) -> {
                log.info("延迟队列收到订单数据: {}", orderNum);

                // 检查是否是测试订单
                if (orderNum.startsWith("TEST_ORDER_")) {
                    log.info("收到测试订单，延迟队列工作正常: {}", orderNum);
                    return;
                }

                try {
                    // 使用统一的处理方法
                    processOrderAutoComplete(orderNum);
                } catch (Exception e) {
                    log.error("处理订单自动完成失败，订单号: {}, 错误: {}", orderNum, e.getMessage(), e);
                }
            }, true); // 重要：延迟队列必须使用 isDelayed=true

            log.info("延迟队列监听器订阅成功: {}", queueName);
            return R.ok("延迟队列监听器订阅成功");

        } catch (Exception e) {
            log.error("订阅延迟队列监听器失败", e);
            return R.fail("订阅失败：" + e.getMessage());
        }
    }

    /**
     * 直接使用Redisson API订阅延迟队列
     */
    @GetMapping("/subscribe/directDelayedQueue")
    @SaCheckPermission("system:queue:subscribe")
    public R<Void> subscribeDirectDelayedQueue() {
        try {
            String queueName = QueueNames.ORDER_DELIVERED_AUTO_COMPLETE;
            log.info("直接使用Redisson API订阅延迟队列: {}", queueName);

            // 直接使用Redisson API
            RedissonClient client = QueueUtils.getClient();
            RBlockingQueue<String> queue = client.getBlockingQueue(queueName);
            RDelayedQueue<String> delayedQueue = client.getDelayedQueue(queue);

            // 启动一个后台线程来监听队列
            new Thread(() -> {
                log.info("延迟队列监听线程启动: {}", queueName);
                while (true) {
                    try {
                        // 阻塞等待队列中的消息
                        String orderNum = queue.take();
                        log.info("延迟队列收到订单数据: {}", orderNum);

                        // 检查是否是测试订单
                        if (orderNum.startsWith("TEST_ORDER_")) {
                            log.info("收到测试订单，延迟队列工作正常: {}", orderNum);
                            continue;
                        }

                        // 处理真实订单
                        processOrderAutoComplete(orderNum);

                    } catch (InterruptedException e) {
                        log.warn("延迟队列监听线程被中断: {}", e.getMessage());
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        log.error("处理延迟队列消息异常: {}", e.getMessage(), e);
                    }
                }
            }).start();

            log.info("延迟队列监听器启动成功: {}", queueName);
            return R.ok("延迟队列监听器启动成功");

        } catch (Exception e) {
            log.error("启动延迟队列监听器失败", e);
            return R.fail("启动失败：" + e.getMessage());
        }
    }

    /**
     * 处理订单自动完成
     */
    private void processOrderAutoComplete(String orderNum) {
        try {
            log.info("处理订单送达未完成 自动完成，订单号: {}", orderNum);
            Long orderId = Long.valueOf(orderNum);
            OrderMain orderMainDB = orderMainService.getById(orderId);

            if (orderMainDB == null) {
                log.warn("订单不存在，订单ID: {}", orderId);
                return;
            }

            if (!ObjectUtil.equals(orderMainDB.getStatus(), OrderStatus.DELIVERED.getCode())) {
                log.info("订单状态已变更，不需自动完成，订单ID: {}, 当前状态: {}", orderId, orderMainDB.getStatus());
                return;
            }

            log.info("开始自动确认订单完成，订单ID: {}", orderId);

            // 直接调用系统自动确认方法，无需权限检查
            orderMainService.autoConfirmBySystem(orderId);
            log.info("自动确认订单完成成功，订单ID: {}", orderId);

        } catch (Exception e) {
            log.error("处理订单自动完成失败，订单号: {}, 错误: {}", orderNum, e.getMessage(), e);
        }
    }



    /**
     * 测试系统自动确认订单功能
     */
    @GetMapping("/test/systemConfirm")
    @SaCheckPermission("system:queue:test")
    public R<Void> testSystemConfirm(Long orderId) {
        try {
            log.info("测试系统自动确认订单功能，订单ID: {}", orderId);

            // 检查订单状态
            OrderMain order = orderMainService.getById(orderId);
            if (order == null) {
                return R.fail("订单不存在");
            }

            if (!OrderStatus.DELIVERED.getCode().equals(order.getStatus())) {
                return R.fail("订单状态不是已送达，当前状态: " + order.getStatus());
            }

            // 直接调用系统自动确认方法
            orderMainService.autoConfirmBySystem(orderId);
            log.info("测试系统自动确认订单成功，订单ID: {}", orderId);

            return R.ok("测试系统自动确认订单成功");

        } catch (Exception e) {
            log.error("测试系统自动确认订单失败，订单ID: {}", orderId, e);
            return R.fail("测试失败：" + e.getMessage());
        }
    }

    /**
     * 重启延迟队列监听器
     */
    @GetMapping("/restart/listener")
    @SaCheckPermission("system:queue:restart")
    public R<Void> restartDelayedQueueListener() {
        try {
            log.info("手动重启延迟队列监听器");

            // 重新启动监听器
            delayedQueueConfig.startDelayedQueueListener();

            log.info("延迟队列监听器重启成功");
            return R.ok("延迟队列监听器重启成功");

        } catch (Exception e) {
            log.error("重启延迟队列监听器失败", e);
            return R.fail("重启失败：" + e.getMessage());
        }
    }

    /**
     * 检查延迟队列监听器状态并测试
     */
    @GetMapping("/test/listenerWorking")
    @SaCheckPermission("system:queue:test")
    public R<Map<String, Object>> testListenerWorking() {
        Map<String, Object> result = new HashMap<>();

        try {
            String queueName = QueueNames.ORDER_DELIVERED_AUTO_COMPLETE;
            String testMessage = "LISTENER_TEST_" + System.currentTimeMillis();

            log.info("测试延迟队列监听器是否工作，测试消息: {}", testMessage);

            // 添加测试消息，3秒后执行
            QueueUtils.addDelayedQueueObject(queueName, testMessage, 3, TimeUnit.SECONDS);

            result.put("testMessage", testMessage);
            result.put("queueName", queueName);
            result.put("delay", "3秒");
            result.put("instruction", "请查看日志，3秒后应该看到 '收到测试订单，延迟队列工作正常' 的日志");

            log.info("测试消息已添加到延迟队列: {}", testMessage);
            return R.ok(result);

        } catch (Exception e) {
            log.error("测试延迟队列监听器失败", e);
            result.put("error", e.getMessage());
            return R.fail("测试失败", result);
        }
    }

}

package com.karrecy.payment.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信支付配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayConfig {

    /**
     * 小程序或公众号AppID
     */
    private String appId;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 商户API密钥
     */
    private String key;

    /**
     * 回调地址
     */
    private String returnUrl;

    /**
     * 退款回调地址
     */
    private String refundUrl;

    /**
     * 是否启用降级策略
     */
    private boolean enableFallback = false;

    /**
     * 统一下单API
     */
    public static final String UNIFIED_ORDER_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder";

    /**
     * 查询订单API
     */
    public static final String ORDER_QUERY_URL = "https://api.mch.weixin.qq.com/pay/orderquery";

    /**
     * 关闭订单API
     */
    public static final String CLOSE_ORDER_URL = "https://api.mch.weixin.qq.com/pay/closeorder";

    /**
     * 申请退款API
     */
    public static final String REFUND_URL = "https://api.mch.weixin.qq.com/secapi/pay/refund";

    /**
     * 查询退款API
     */
    public static final String REFUND_QUERY_URL = "https://api.mch.weixin.qq.com/pay/refundquery";

    /**
     * 下载对账单
     */
    public static final String DOWNLOAD_BILL_URL = "https://api.mch.weixin.qq.com/pay/downloadbill";

    /**
     * 下载资金对账单
     */
    public static final String DOWNLOAD_FUND_FLOW_URL = "https://api.mch.weixin.qq.com/pay/downloadfundflow";

    /**
     * 交易保障
     */
    public static final String REPORT_URL = "https://api.mch.weixin.qq.com/payitil/report";

    /**
     * 转换短链接
     */
    public static final String SHORT_URL = "https://api.mch.weixin.qq.com/tools/shorturl";

    /**
     * 授权码查询openId接口
     */
    public static final String AUTH_CODE_TO_OPENID_URL = "https://api.mch.weixin.qq.com/tools/authcodetoopenid";

    /**
     * 商户API证书路径
     */
    private String certPath;
} 
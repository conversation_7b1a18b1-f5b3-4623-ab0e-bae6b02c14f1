package com.karrecy.payment.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信支付属性配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayProperties {

    /**
     * 小程序或公众号AppID
     */
    private String appId;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 商户API密钥
     */
    private String key;

    /**
     * 回调地址
     */
    private String returnUrl;

    /**
     * 退款回调地址
     */
    private String refundUrl;

    /**
     * 是否启用降级策略
     */
    private boolean enableFallback = false;

    /**
     * 是否使用公钥模式
     * 设置为true时，使用微信支付公钥进行验签，不下载平台证书
     * 设置为false时，使用平台证书模式
     */
    private Boolean usePublicKeyMode = true;

    /**
     * 商户API证书序列号
     * 仅当usePublicKeyMode为false时需要
     */
    private String merchantSerialNumber;
}

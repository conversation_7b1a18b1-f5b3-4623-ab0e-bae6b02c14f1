<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>福U跑腿系统数据字典 - 表格版</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; margin: 20px; line-height: 1.6; }
        h1 { text-align: center; color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; border-left: 4px solid #3498db; padding-left: 15px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        th { background: #3498db; color: white; padding: 12px 8px; text-align: left; font-weight: bold; }
        td { padding: 10px 8px; border-bottom: 1px solid #ddd; }
        tr:nth-child(even) { background: #f8f9fa; }
        tr:hover { background: #e8f4fd; }
        .pk { background: #e74c3c; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; }
        .fk { background: #f39c12; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; }
        .uk { background: #9b59b6; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; }
        .data-type { color: #27ae60; font-weight: bold; }
        .comment { color: #7f8c8d; font-style: italic; }
        .table-desc { background: #ecf0f1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .export-note { background: #d5f4e6; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #27ae60; }
    </style>
</head>
<body>
    <h1>福U跑腿系统数据字典</h1>
    
    <div class="export-note">
        <h3>📋 Word导入说明：</h3>
        <ol>
            <li><strong>复制粘贴法</strong>：选中下方表格内容 → Ctrl+C → 在Word中Ctrl+V</li>
            <li><strong>另存为法</strong>：浏览器菜单 → 打印 → 另存为PDF → 在Word中插入PDF页面</li>
            <li><strong>截图法</strong>：使用截图工具截取表格 → 粘贴到Word</li>
        </ol>
    </div>

    <h2>1. 用户管理模块</h2>
    
    <h3>1.1 全局用户表 (user)</h3>
    <div class="table-desc">系统核心用户表，统一管理所有用户的基本信息和权限</div>
    <table>
        <tr><th>字段名</th><th>数据类型</th><th>约束</th><th>说明</th></tr>
        <tr><td>uid</td><td class="data-type">bigint(20)</td><td><span class="pk">PK</span></td><td>全局用户ID，自增主键</td></tr>
        <tr><td>device_type</td><td class="data-type">tinyint(4)</td><td>NOT NULL</td><td>设备类型：0-PC，1-小程序</td></tr>
        <tr><td>create_time</td><td class="data-type">datetime</td><td>NOT NULL</td><td>创建时间</td></tr>
        <tr><td>login_time</td><td class="data-type">datetime</td><td>NOT NULL</td><td>最后登录时间</td></tr>
        <tr><td>login_ip</td><td class="data-type">varchar(20)</td><td>NOT NULL</td><td>登录IP地址</td></tr>
        <tr><td>login_region</td><td class="data-type">varchar(50)</td><td>NOT NULL</td><td>登录地区</td></tr>
        <tr><td>user_type</td><td class="data-type">int(11)</td><td>NOT NULL</td><td>用户类型：0-超级管理员，1-校区管理员，2-普通管理员，3-普通用户，4-跑腿用户</td></tr>
        <tr><td>create_id</td><td class="data-type">bigint(20)</td><td>NOT NULL</td><td>创建人ID</td></tr>
        <tr><td>update_time</td><td class="data-type">datetime</td><td>NOT NULL</td><td>更新时间</td></tr>
        <tr><td>update_id</td><td class="data-type">bigint(20)</td><td>NOT NULL</td><td>更新人ID</td></tr>
    </table>

    <h3>1.2 微信用户表 (user_wx)</h3>
    <div class="table-desc">小程序端用户详细信息表，包含跑腿员相关字段</div>
    <table>
        <tr><th>字段名</th><th>数据类型</th><th>约束</th><th>说明</th></tr>
        <tr><td>id</td><td class="data-type">bigint(20)</td><td><span class="pk">PK</span></td><td>主键ID，自增</td></tr>
        <tr><td>uid</td><td class="data-type">bigint(20)</td><td><span class="fk">FK</span></td><td>关联全局用户表</td></tr>
        <tr><td>openid</td><td class="data-type">varchar(128)</td><td><span class="uk">UK</span></td><td>微信小程序唯一标识</td></tr>
        <tr><td>avatar</td><td class="data-type">varchar(255)</td><td>NOT NULL</td><td>用户头像URL</td></tr>
        <tr><td>nickname</td><td class="data-type">varchar(10)</td><td>NOT NULL</td><td>用户昵称</td></tr>
        <tr><td>phone</td><td class="data-type">varchar(11)</td><td>NULL</td><td>手机号码</td></tr>
        <tr><td>points</td><td class="data-type">int(11)</td><td>DEFAULT 0</td><td>用户积分</td></tr>
        <tr><td>is_runner</td><td class="data-type">tinyint(4)</td><td>DEFAULT 0</td><td>是否跑腿员：0-否，1-是</td></tr>
        <tr><td>can_order</td><td class="data-type">tinyint(4)</td><td>DEFAULT 0</td><td>是否可下单：0-否，1-是</td></tr>
        <tr><td>can_take</td><td class="data-type">tinyint(4)</td><td>DEFAULT 0</td><td>是否可接单：0-否，1-是</td></tr>
        <tr><td>school_id</td><td class="data-type">bigint(20)</td><td><span class="fk">FK</span></td><td>绑定学校ID</td></tr>
        <tr><td>realname</td><td class="data-type">varchar(20)</td><td>NULL</td><td>真实姓名</td></tr>
        <tr><td>gender</td><td class="data-type">tinyint(4)</td><td>NULL</td><td>性别：0-女，1-男</td></tr>
        <tr><td>credit_score</td><td class="data-type">int(11)</td><td>NULL</td><td>信用分数</td></tr>
    </table>

    <h3>1.3 PC用户表 (user_pc)</h3>
    <div class="table-desc">PC端管理用户信息表，主要用于后台管理系统</div>
    <table>
        <tr><th>字段名</th><th>数据类型</th><th>约束</th><th>说明</th></tr>
        <tr><td>id</td><td class="data-type">bigint(20)</td><td><span class="pk">PK</span></td><td>主键ID，自增</td></tr>
        <tr><td>uid</td><td class="data-type">bigint(20)</td><td><span class="fk">FK</span></td><td>关联全局用户表</td></tr>
        <tr><td>username</td><td class="data-type">varchar(25)</td><td><span class="uk">UK</span></td><td>登录用户名</td></tr>
        <tr><td>password</td><td class="data-type">varchar(100)</td><td>NOT NULL</td><td>登录密码（加密）</td></tr>
        <tr><td>phone</td><td class="data-type">varchar(11)</td><td>NOT NULL</td><td>手机号码</td></tr>
        <tr><td>name</td><td class="data-type">varchar(20)</td><td>NOT NULL</td><td>真实姓名</td></tr>
        <tr><td>student_card_url</td><td class="data-type">varchar(255)</td><td>NULL</td><td>学生证照片URL</td></tr>
        <tr><td>id_card_url</td><td class="data-type">varchar(255)</td><td>NULL</td><td>身份证照片URL</td></tr>
        <tr><td>sex</td><td class="data-type">tinyint(4)</td><td>NOT NULL</td><td>性别：0-女，1-男</td></tr>
        <tr><td>status</td><td class="data-type">tinyint(4)</td><td>DEFAULT 0</td><td>账户状态：0-禁用，1-启用</td></tr>
        <tr><td>avatar</td><td class="data-type">varchar(255)</td><td>NOT NULL</td><td>头像URL</td></tr>
        <tr><td>email</td><td class="data-type">varchar(50)</td><td>NULL</td><td>邮箱地址</td></tr>
        <tr><td>email_enable</td><td class="data-type">tinyint(4)</td><td>NULL</td><td>邮箱是否启用</td></tr>
    </table>

    <h2>2. 学校管理模块</h2>
    
    <h3>2.1 学校表 (school)</h3>
    <div class="table-desc">学校信息及收益分配配置表</div>
    <table>
        <tr><th>字段名</th><th>数据类型</th><th>约束</th><th>说明</th></tr>
        <tr><td>id</td><td class="data-type">bigint(20)</td><td><span class="pk">PK</span></td><td>学校ID，自增主键</td></tr>
        <tr><td>belong_uid</td><td class="data-type">bigint(20)</td><td><span class="fk">FK</span></td><td>所属管理员用户ID</td></tr>
        <tr><td>adcode</td><td class="data-type">char(6)</td><td>NULL</td><td>城市行政区划代码</td></tr>
        <tr><td>name</td><td class="data-type">varchar(100)</td><td>NOT NULL</td><td>学校名称</td></tr>
        <tr><td>logo</td><td class="data-type">varchar(255)</td><td>NOT NULL</td><td>学校logo图片URL</td></tr>
        <tr><td>create_time</td><td class="data-type">datetime</td><td>NOT NULL</td><td>创建时间</td></tr>
        <tr><td>update_time</td><td class="data-type">datetime</td><td>NOT NULL</td><td>更新时间</td></tr>
        <tr><td>status</td><td class="data-type">tinyint(4)</td><td>NOT NULL</td><td>状态：0-禁用，1-启用</td></tr>
        <tr><td>profit_plat</td><td class="data-type">tinyint(4)</td><td>NOT NULL</td><td>平台收益占比（百分比）</td></tr>
        <tr><td>profit_agent</td><td class="data-type">tinyint(4)</td><td>NOT NULL</td><td>代理收益占比（百分比）</td></tr>
        <tr><td>profit_runner</td><td class="data-type">tinyint(4)</td><td>NOT NULL</td><td>跑腿员收益占比（百分比）</td></tr>
        <tr><td>floor_price</td><td class="data-type">decimal(10,2)</td><td>NOT NULL</td><td>订单底价（元）</td></tr>
        <tr><td>additional_profit_rate</td><td class="data-type">tinyint(4)</td><td>NULL</td><td>追加金额订单平台分成比例</td></tr>
        <tr><td>emergency_min_amount</td><td class="data-type">decimal(10,2)</td><td>NULL</td><td>加急订单最低追加金额</td></tr>
    </table>

    <div style="page-break-before: always;"></div>
    
    <h2>3. 订单管理模块</h2>
    
    <h3>3.1 订单主表 (order_main)</h3>
    <div class="table-desc">订单核心信息表，记录订单基本信息和状态</div>
    <table>
        <tr><th>字段名</th><th>数据类型</th><th>约束</th><th>说明</th></tr>
        <tr><td>id</td><td class="data-type">bigint(20)</td><td><span class="pk">PK</span></td><td>订单ID</td></tr>
        <tr><td>school_id</td><td class="data-type">bigint(20)</td><td><span class="fk">FK</span></td><td>所属学校ID</td></tr>
        <tr><td>service_type</td><td class="data-type">int(11)</td><td>NOT NULL</td><td>服务类型：0-帮取送，1-代买，2-万能服务</td></tr>
        <tr><td>tag</td><td class="data-type">varchar(50)</td><td>NOT NULL</td><td>订单标签</td></tr>
        <tr><td>weight</td><td class="data-type">varchar(20)</td><td>NULL</td><td>物品重量描述</td></tr>
        <tr><td>start_address</td><td class="data-type">json</td><td>NULL</td><td>起点地址信息（JSON格式）</td></tr>
        <tr><td>end_address</td><td class="data-type">json</td><td>NOT NULL</td><td>终点地址信息（JSON格式）</td></tr>
        <tr><td>detail</td><td class="data-type">varchar(100)</td><td>NOT NULL</td><td>订单具体描述</td></tr>
        <tr><td>is_timed</td><td class="data-type">tinyint(4)</td><td>NOT NULL</td><td>是否指定时间：0-否，1-是</td></tr>
        <tr><td>specified_time</td><td class="data-type">datetime</td><td>NULL</td><td>指定完成时间</td></tr>
        <tr><td>auto_cancel_ttl</td><td class="data-type">int(11)</td><td>NOT NULL</td><td>未接单自动取消时间（秒）</td></tr>
        <tr><td>gender</td><td class="data-type">tinyint(4)</td><td>NOT NULL</td><td>跑腿员性别要求：0-女，1-男，2-不限</td></tr>
        <tr><td>estimated_price</td><td class="data-type">decimal(10,2)</td><td>NULL</td><td>预估商品价格</td></tr>
        <tr><td>total_amount</td><td class="data-type">decimal(10,2)</td><td>NOT NULL</td><td>订单总金额</td></tr>
        <tr><td>status</td><td class="data-type">tinyint(4)</td><td>NOT NULL</td><td>订单状态</td></tr>
        <tr><td>user_id</td><td class="data-type">bigint(20)</td><td><span class="fk">FK</span></td><td>下单用户ID</td></tr>
        <tr><td>runner_id</td><td class="data-type">bigint(20)</td><td><span class="fk">FK</span></td><td>接单跑腿员ID</td></tr>
        <tr><td>create_time</td><td class="data-type">datetime</td><td>NOT NULL</td><td>订单创建时间</td></tr>
        <tr><td>update_time</td><td class="data-type">datetime</td><td>NOT NULL</td><td>订单更新时间</td></tr>
    </table>

    <p class="comment" style="text-align: center; margin-top: 30px;">
        <strong>注：</strong>此表格版数据字典可直接复制粘贴到Word文档中，保持格式完整。<br>
        如需完整版本，请查看生成的HTML文件。
    </p>

</body>
</html>

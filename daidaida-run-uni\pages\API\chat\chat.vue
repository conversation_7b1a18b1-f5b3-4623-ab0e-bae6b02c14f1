<template>
	<view class="chat">
    <!-- 顶部标题 -->
    <view class="topTabbar">
      <!-- 返回图标 -->
	  <nut-icon name="rect-left" @click="goBack()"></nut-icon>
      <!-- 标题 -->
      <view class="text">在线沟通</view>
    </view>
		<scroll-view  :style="{height: `${windowHeight-inputHeight - 120}rpx`}"
		id="scrollview"
		scroll-y="true" 
		:scroll-top="scrollTop"
		@scroll="handleScroll"
		class="scroll-view" 
		>
			<div v-show="skeletonLoading" style="width: 90%;margin-left: 5%;">
				<div style="width: 100%;height: 20px;"></div>
				  <nut-skeleton width="240px" height="15px" title animated avatar avatarSize="60px" row="3"> </nut-skeleton>
				  <div style="width: 100%;height: 20px;"></div>
				  <nut-skeleton width="240px" height="15px" title animated avatar avatarSize="60px" row="3"> </nut-skeleton>
				  <div style="width: 100%;height: 20px;"></div>
				  <nut-skeleton width="240px" height="15px" title animated avatar avatarSize="60px" row="3"> </nut-skeleton>
				 <div style="width: 100%;height: 20px;"></div>
				  <nut-skeleton width="240px" height="15px" title animated avatar avatarSize="60px" row="3"> </nut-skeleton>
				  <div style="width: 100%;height: 20px;"></div>
				  <nut-skeleton width="240px" height="15px" title animated avatar avatarSize="60px" row="3"> </nut-skeleton>
				  <div style="width: 100%;height: 20px;"></div>
				  <nut-skeleton width="240px" height="15px" title animated avatar avatarSize="60px" row="3"> </nut-skeleton>
			</div>
			<!-- 聊天主体 -->
			<view v-show="!skeletonLoading" id="msglistview" class="chat-body">
				<!-- 聊天记录 -->
				<view v-for="(item,index) in rows" :key="index">
					<!-- 自己发的消息 -->
					<view class="item self" v-if="item.senderId == userInfo.uid" >
						<!-- 文字内容 -->
						<div class="contentBox">
							<div class="contentTop textRight"> 
								<nut-tag v-if="userInfo.userType == 0" custom-color="#1aad19" type="primary">平台管理员</nut-tag>
								<nut-tag v-if="userInfo.userType == 2" custom-color="#7a22cc" type="primary">管理员</nut-tag>
								<nut-tag v-if="chatInitData.userId == userInfo.uid" custom-color="#cccccc" type="primary">我</nut-tag>
								<nut-tag v-if="chatInitData.runnerId == userInfo.uid" custom-color="#cccccc" type="primary">我</nut-tag>
								<nut-tag v-if="userInfo.userType == 5" type="primary">系统</nut-tag>
								<nut-tag v-if="userInfo.userType == 3" custom-color="#909399" type="primary">普通用户</nut-tag>
								<nut-tag v-if="userInfo.userType == 4" custom-color="#fa6400" type="primary">跑腿用户</nut-tag>
								<span class="ellipsis">{{userInfo.userWx.nickname}}</span>
							</div>
							<view class="content right">
								<!-- 文本消息 -->
								<span v-if="item.msgType == 1" style="display: block;">{{item.message}}</span>
								<!-- 图片消息 -->
								<image v-if="item.msgType == 2" class="chat-image" :src="item.message" mode="widthFix" @click="previewImage(item.message)" @load="handleImageLoaded(item)" @error="handleImageError(item)" :data-msgid="index"></image>
								<span class="chatTime">{{item.createTime}}</span>
							</view>
						</div>
						<!-- 头像 -->
						<image class="avatar" :src="userInfo.userWx.avatar">
						</image>
					</view>
					<!-- 机器人发的消息 -->
					<view class="item Ai" v-if="item.senderId != userInfo.uid">
						<!-- 头像 -->     
						<image v-if="item.senderType == 0" class="avatar" :src="chatInitData.adminAvatar"></image>
						<image v-if="item.senderType == 1" class="avatar" :src="chatInitData.agentAvatar"></image>
						<image v-if="item.senderId == chatInitData.userId" class="avatar" :src="chatInitData.userAvatar"></image>
						<image v-if="item.senderId == chatInitData.runnerId" class="avatar" :src="chatInitData.runnerAvatar"></image>
						<!-- 文字内容 -->
						<div class="contentBox">
							<div v-if="item.senderType != 5" class="contentTop">
								<nut-tag v-if="item.senderType == 0" custom-color="#1aad19" type="primary">平台管理员</nut-tag>
								<nut-tag v-if="item.senderType == 2" custom-color="#7a22cc" type="primary">管理员</nut-tag>
								<nut-tag v-if="item.senderType == 3" custom-color="#909399" type="primary">普通用户</nut-tag>
								<nut-tag v-if="item.senderType == 4" custom-color="#fa6400" type="primary">跑腿用户</nut-tag>
								<nut-tag v-if="chatInitData.userId == item.senderId" custom-color="#cccccc" type="primary">我</nut-tag>
								<nut-tag v-if="chatInitData.runnerId == item.senderId" custom-color="#cccccc" type="primary">我</nut-tag>
								<span class="ellipsis">{{item.senderName}}</span>
							</div>
							<view class="content left">
								<!-- 文本消息 -->
								<span v-if="item.msgType == 1" style="display: block;">{{item.message}}</span>
								<!-- 图片消息 -->
								<image v-if="item.msgType == 2" class="chat-image" :src="item.message" mode="widthFix" @click="previewImage(item.message)" @load="handleImageLoaded(item)" @error="handleImageError(item)" :data-msgid="index"></image>
								<span class="chatTime">{{item.createTime}}</span>
							</view>
						</div>
						
					</view>
				</view>
			</view>
		</scroll-view>
		<!-- 底部消息发送栏 -->
		<!-- 用来占位，防止聊天消息被发送框遮挡 -->
		<view class="chat-bottom" :style="{height: `${inputHeight}rpx`}">
			<view class="send-msg" :style="{bottom:`${keyboardHeight}rpx`}">
        <view class="uni-textarea">
          <textarea v-model="chatText"
            maxlength="300"
            confirm-type="send"
            @confirm="handleSend"
            placeholder="请文明沟通~"
            :show-confirm-bar="false"
            :adjust-position="false"
            @linechange="sendHeight"
            @focus="focus" @blur="blur"
           auto-height></textarea>
        </view>
        <view class="upload-img" @click="chooseImage">
          <nut-icon name="photograph" size="20"></nut-icon>
        </view>
				<button :disabled="skeletonLoading" @click="handleSend" class="send-btn">发送</button>
			</view>
		</view>
	</view>
</template>
<script>
	import dayjs from 'dayjs';
	import relativeTime from 'dayjs/plugin/relativeTime'; // 引入 relativeTime 插件
	import 'dayjs/locale/zh-cn';  // 导入中文语言包
	// 扩展 dayjs 插件
	dayjs.extend(relativeTime);
	// 设置为中文
	dayjs.locale('zh-cn');
	import ws from '@/request/websocket.js'
	import { getInitChat,getPageOrderChat } from '@/request/apis/order.js'
	import { upload_url } from '@/request/request.js'
	export default{
		data() {
			return {
				skeletonLoading:true,
				userInfo:{},
				//键盘高度
				keyboardHeight:0,
				//底部消息发送高度
				bottomHeight: 0,
				//滚动距离
				scrollTop: 0,
				userId:'',
				//发送的消息
				chatText:"",
				chatInitData:{},
				msgData:{
					'orderId':'',
					'isBroadcast':1,
					'recipientIds':[],
					'msgType':1, // 1文本，2图片
					'message':'',
					'senderId':'',
					'senderType':'',
					'createTime':'1999-02-02 12:12:12'
				},
				// 查询参数
				queryParams: {
					pageNum: 1,
					pageSize: 20,
				},
				// 总条数
				total: 0,
				rows:[],
				hasMore: true,
				// WebSocket连接状态
				wsConnected: false,
				// 发送图片时的重试机制
				uploadRetryCount: 0,
				maxUploadRetry: 3,
				// 是否正在上传图片
				isUploading: false,
				// 上传失败的图片路径
				failedImagePath: '',
				// 消息发送队列
				pendingMessages: []
			}
		},
		updated(){
			//页面更新时调用聊天消息定位到最底部
			// this.scrollToBottom();
			console.log('聊天页面更新，当前消息数:', this.rows.length);
			// 检查是否有图片消息
			const imageMessages = this.rows.filter(item => item.msgType === 2);
			console.log('当前图片消息数:', imageMessages.length);
		},
		computed: {
		
			windowHeight() {
			    return this.rpxTopx(uni.getSystemInfoSync().windowHeight)
			},
			// 键盘弹起来的高度+发送框高度
			inputHeight(){
				return this.bottomHeight+this.keyboardHeight
			}
		},
		onLoad(options){
			console.log("chat onLoad");
			this.skeletonLoading = true;
			
			// 关闭可能存在的WebSocket连接
			try {
				ws.completeClose();
			} catch (e) {
				console.error('关闭WebSocket出错:', e);
			}
			
			const checkOperationStatus = setInterval(() => {
			  if (this.$store.state.appLaunch) {
				  // 初始化数据
				  this.initData(options.orderId)
				  this.pageQuery()
				  
				  // 确保之前的WebSocket连接已关闭后再创建新连接
				  setTimeout(() => {
				  	// 初始化WebSocket
				  	ws.init();
					// 设置WebSocket事件监听
					this.setupWebSocketListeners();
				  }, 500);
				  
				  uni.onKeyboardHeightChange(res => {
				  	// 键盘高度处理，优化显示
				  	this.keyboardHeight = this.rpxTopx(res.height)
				  	if(this.keyboardHeight < 0) this.keyboardHeight = 0;
					// 键盘弹起时自动滚动到底部
					if(this.keyboardHeight > 0) {
						setTimeout(() => {
							this.scrollToBottom();
						}, 100);
					}
				  })
				  this.scrollToBottom()
				  
				  clearInterval(checkOperationStatus);
				  console.log('聊天页面初始化完成');
			  }
			}, 100);
		},
		
		onReady() {
			// 监听器已在initializeWebSocket中设置
		},
		
		onShow() {
			console.log('聊天页面显示');
			// 检查连接状态
			const status = ws.getStatus();
			if (!status.isConnected && !status.isInitializing) {
				console.log('WebSocket连接已断开');
				this.wsConnected = false;
			}
		},
		
		onHide() {
			console.log('聊天页面隐藏');
			// 不要在隐藏时关闭连接，以免频繁创建连接超出限制
		},
		
		onUnload(){
			console.log('聊天页面卸载，关闭所有连接');
			// 移除所有事件监听
			this.removeWebSocketListeners();
			// 清空消息队列
			this.pendingMessages = [];
			// 完全关闭WebSocket连接
			try {
				ws.completeClose();
			} catch (e) {
				console.error('关闭WebSocket出错:', e);
			}
		},
		
		methods: {
			// 设置WebSocket事件监听
			setupWebSocketListeners() {
				// 监听WebSocket消息
				uni.$on('ws-message', this.handleWsMessage);
				
				// 监听WebSocket连接状态
				uni.$on('ws-connected', this.handleWsConnected);
				
				// 监听WebSocket错误
				uni.$on('ws-error', this.handleWsError);
				
				// 监听WebSocket重连失败
				uni.$on('ws-reconnect-failed', this.handleWsReconnectFailed);
				
				// 监听WebSocket消息发送失败
				uni.$on('ws-send-failed', this.handleWsSendFailed);
			},
			
			// 移除WebSocket事件监听
			removeWebSocketListeners() {
				uni.$off('ws-message', this.handleWsMessage);
				uni.$off('ws-connected', this.handleWsConnected);
				uni.$off('ws-error', this.handleWsError);
				uni.$off('ws-reconnect-failed', this.handleWsReconnectFailed);
				uni.$off('ws-send-failed', this.handleWsSendFailed);
			},
			
			// 处理WebSocket消息
			handleWsMessage(message) {
				console.log('收到WebSocket消息:', message);
				// 确保消息有正确的msgType和其他必要字段
				if (!message || (typeof message.msgType === 'undefined')) {
					console.error('收到无效的WebSocket消息格式:', message);
					return;
				}
				
				// 确保消息有createTime字段
				if (!message.createTime) {
					message.createTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
				}
				
				// 将接收到的消息添加到消息列表
				this.rows.push(message);
				this.scrollToBottom();
			},
			
			// 处理WebSocket连接状态变化
			handleWsConnected(connected) {
				console.log('WebSocket连接状态变化:', connected);
				this.wsConnected = connected;
				
				if (connected) {
					console.log('WebSocket连接已建立');
					// 连接恢复后，尝试处理待发送队列
					if (this.pendingMessages.length > 0) {
						setTimeout(() => {
							this.processPendingMessages();
						}, 1000);
					}
				
				// 如果连接恢复且有失败的图片，尝试重新发送
					if (this.failedImagePath && !this.isUploading) {
					this.retryUploadImage();
					}
				} else {
					console.log('WebSocket连接已断开');
					// 连接断开，不做特殊处理
					// 发送消息时会自动尝试重连
				}
			},
			
			// 处理WebSocket错误
			handleWsError(error) {
				console.error('WebSocket错误:', error);
				this.wsConnected = false;
			},
			
			// 处理WebSocket重连失败
			handleWsReconnectFailed() {
				console.error('WebSocket重连失败');
				this.wsConnected = false;
				uni.showToast({
					title: '连接服务器失败',
					icon: 'none',
					duration: 2000
				});
			},
			
			// 处理WebSocket消息发送失败
			handleWsSendFailed(message) {
				console.log('WebSocket消息发送失败:', message);
				// 确保消息不重复添加到队列
				const isDuplicate = this.pendingMessages.some(msg => 
					msg.msgType === message.msgType && 
					msg.message === message.message && 
					msg.createTime === message.createTime
				);
				
				if (!isDuplicate) {
					// 将消息添加到队列
					this.pendingMessages.push(message);
					
					// 延迟再次尝试处理队列
					setTimeout(() => {
						this.processPendingMessages();
					}, 3000);
				}
			},
			
			// 重试上传失败的图片
			retryUploadImage() {
				if (!this.failedImagePath) return;
				
				console.log('尝试重新上传图片:', this.failedImagePath);
				
				// 检查网络状态
				uni.getNetworkType({
					success: (res) => {
						if (res.networkType === 'none') {
							uni.showToast({
								title: '无网络连接，请检查网络设置',
								icon: 'none'
							});
						} else {
							this.uploadImageSafely(this.failedImagePath);
				this.failedImagePath = '';
						}
					}
				});
			},
			handleScroll(e) {
				// console.log(e);
				// console.log(this.scrollTop);
				
				if (e.target.scrollTop === 0 && this.hasMore) {
					this.pageQuery(); // 滚动到顶部加载新数据
				}
			},
			goBack() {
				uni.navigateBack()
			},
			// 选择图片
			chooseImage() {
				// 检查是否正在上传中
				if (this.isUploading) {
					uni.showToast({
						title: '正在上传中，请稍候',
						icon: 'none'
					});
					return;
				}
				
				// 检查网络状态
				uni.getNetworkType({
					success: (res) => {
						if (res.networkType === 'none') {
							uni.showToast({
								title: '无网络连接，请检查网络设置',
								icon: 'none'
							});
							return;
						}
						
						// 选择图片
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePaths = res.tempFilePaths;
								// 上传图片但不立即通过WebSocket发送
								this.uploadImageSafely(tempFilePaths[0]);
							}
						});
					}
				});
			},
			
			// 处理图片加载错误
			handleImageError(item) {
				console.error('图片加载失败:', item);
				// 记录详细的错误信息
				const errorInfo = {
					messageType: item.msgType,
					messageUrl: item.message,
					senderId: item.senderId,
					timestamp: new Date().toISOString()
				};
				console.error('图片加载详细错误:', JSON.stringify(errorInfo));
				
				// 尝试使用完整的URL
				if (item.message && !item.message.startsWith('http')) {
					console.log('尝试修复图片URL');
					// 获取基础URL
					const baseUrl = this.getBaseImageUrl();
					// 创建完整URL
					const fullUrl = `${baseUrl}${item.message}`;
					
					// 更新消息中的URL
					this.$set(item, 'message', fullUrl);
					console.log('已更新图片URL:', fullUrl);
				}
				
				uni.showToast({
					title: '图片加载失败',
					icon: 'none',
					duration: 1500
				});
			},
			
			// 处理图片加载成功
			handleImageLoaded(item) {
				console.log('图片加载成功:', item.message);
				setTimeout(() => {
					this.scrollToBottom();
				}, 100);
			},
			
			// 获取基础图片URL
			getBaseImageUrl() {
				// 优先使用配置的图片基础URL
				if (this.$imageBaseUrl) {
					return this.$imageBaseUrl;
				}
				
				// 使用API基础URL
				if (this.$baseUrl) {
					// 移除末尾的斜杠(如果有)
					const baseUrl = this.$baseUrl.endsWith('/') 
						? this.$baseUrl.slice(0, -1) 
						: this.$baseUrl;
					return baseUrl;
				}
				
				// 使用当前域名
				return window.location.origin;
			},
			
			// 在本地显示图片消息
			addImageMessageToLocal(imageUrl) {
				try {
					// 验证图片URL
					if (!imageUrl) {
						console.error('图片URL为空');
						uni.showToast({
							title: '图片URL无效',
							icon: 'none'
						});
						return;
					}
					
					console.log('添加本地图片消息, URL:', imageUrl);
					
					// 测试图片可访问性
					uni.getImageInfo({
						src: imageUrl,
						success: (res) => {
							console.log('图片信息获取成功:', res);
						},
						fail: (err) => {
							console.error('图片信息获取失败:', err);
							// 尝试修复URL
							if (!imageUrl.startsWith('http')) {
								const fullUrl = this.getBaseImageUrl() + imageUrl;
								console.log('尝试使用完整URL:', fullUrl);
								imageUrl = fullUrl;
							}
						}
					});
					
					// 设置消息类型为图片
					const msgCopy = JSON.parse(JSON.stringify(this.msgData));
					msgCopy.msgType = 2; // 确保设置为图片类型
					msgCopy.message = imageUrl;
					msgCopy.senderId = this.userInfo.uid; // 确保正确设置发送者ID
					msgCopy.senderType = this.userInfo.userType; // 确保正确设置发送者类型
					msgCopy.createTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
					
					console.log('添加本地图片消息对象:', msgCopy);
					
					// 只添加到本地消息列表
					this.rows.push(msgCopy);
					
					// 滚动到底部
					this.scrollToBottom();
				} catch (e) {
					console.error('处理本地图片消息出错:', e);
				}
			},
			
			// 安全上传图片 - 完全与WebSocket分离
			uploadImageSafely(filePath) {
				this.isUploading = true;
				uni.showLoading({
					title: '图片上传中...'
				});
				
				// 保存当前WebSocket状态
				const originalWsConnected = this.wsConnected;
				
				// 获取上传URL
				const uploadUrl = this.getUploadUrl();
				
				console.log('开始上传图片，上传URL:', uploadUrl);
				
				// 直接使用封装好的上传方法，不走WebSocket
				uni.uploadFile({
					url: uploadUrl,
					filePath: filePath,
					name: 'file',
					formData: {
						type: 0,
						name: 'chat_img_' + new Date().getTime()
					},
					header: {
						'Authorization': 'Bearer ' + uni.getStorageSync('token')
					},
					success: (uploadRes) => {
						uni.hideLoading();
						try {
							console.log('上传结果原始数据:', uploadRes);
							
							const result = JSON.parse(uploadRes.data);
							if (result.code === 200) {
								console.log('图片上传成功:', result);
								
								// 确保URL正确
								let imageUrl = result.data.url;
								
								// 服务器返回的URL格式本身就是正确的（扩展名前没有点号）
								// 例如：https://cdn.daidaida.xyz/path/ea98225ec2cc4db09ecec100c59e6652jpg
								// 不需要添加点号，直接使用
								if (imageUrl) {
									console.log('图片URL（服务器原始格式）:', imageUrl);

									// 确保URL有http前缀
									if (!imageUrl.startsWith('http')) {
										imageUrl = this.getBaseImageUrl() + imageUrl;
										console.log('已修正图片URL:', imageUrl);
									}
								}
								
								// 先在本地显示图片
								this.addImageMessageToLocal(imageUrl);
								
								// 延迟一段时间后再通过WebSocket发送，避免并发问题
								setTimeout(() => {
									// 确保WebSocket已重新连接
									const status = ws.getStatus();
									if (!status.isConnected) {
										console.log('重新连接WebSocket...');
										this.reconnectWebSocket();
										// 在连接恢复后发送
										setTimeout(() => {
											this.sendImageMessageSafely(imageUrl);
										}, 1500);
									} else {
										this.sendImageMessageSafely(imageUrl);
									}
								}, 1000);
							} else {
								uni.showToast({
									title: '图片上传失败: ' + (result.msg || '服务器错误'),
									icon: 'none'
								});
								console.error('上传失败:', result);
							}
						} catch (e) {
							console.error('解析上传结果失败:', e, uploadRes);
							
							// 尝试处理特殊情况：有时服务器返回的可能是带双引号的JSON字符串
							try {
								const cleanData = uploadRes.data.replace(/^"/, '').replace(/"$/, '').replace(/\\"/g, '"');
								const result = JSON.parse(cleanData);
								if (result.code === 200) {
									console.log('使用清理后的数据解析成功:', result);
									
									// 确保URL正确
									let imageUrl = result.data.url;
									if (imageUrl && !imageUrl.startsWith('http')) {
										imageUrl = this.getBaseImageUrl() + imageUrl;
										console.log('已修正图片URL:', imageUrl);
									}
									
									this.addImageMessageToLocal(imageUrl);
									setTimeout(() => {
										this.sendImageMessageSafely(imageUrl);
									}, 1000);
									return;
								}
							} catch (e2) {
								console.error('第二次尝试解析上传结果也失败:', e2);
							}
							
							uni.showToast({
								title: '图片上传失败，请重试',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.hideLoading();
						console.error('上传错误:', err);
						uni.showToast({
							title: '图片上传失败，网络错误',
							icon: 'none'
						});
					},
					complete: () => {
						this.isUploading = false;
						// 确保连接恢复
						if (originalWsConnected && !this.wsConnected) {
							this.reconnectWebSocket();
						}
					}
				});
			},
			
			// 安全地通过WebSocket发送图片消息
			sendImageMessageSafely(imageUrl) {
				try {
					// 图片URL验证
					if (!imageUrl) {
						console.error('图片URL为空，无法发送');
						uni.showToast({
							title: '图片上传失败，URL无效',
							icon: 'none'
						});
						return;
					}
					
					// 准备消息数据
					const msgData = JSON.parse(JSON.stringify(this.msgData));
					msgData.msgType = 2; // 明确设置为图片类型
					msgData.message = imageUrl;
					msgData.createTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
					
					console.log('准备发送图片消息:', msgData);
					
					// 检查WebSocket连接状态
					const status = ws.getStatus();
					
					if (!status.isConnected) {
						console.log('WebSocket未连接，尝试重连后发送');
						// 先尝试重连
						ws.init();
					
						// 将消息添加到待发送队列
						this.pendingMessages.push(msgData);
					
						// 延迟处理待发送消息
					setTimeout(() => {
						this.processPendingMessages();
						}, 2000);
					} else {
						// 直接发送消息
						const messageStr = JSON.stringify(msgData);
						console.log('通过WebSocket发送图片消息:', messageStr);
						ws.send(messageStr);
					}
				} catch (e) {
					console.error('WebSocket发送图片消息出错:', e);
				}
			},
			
			// 处理待发送消息队列
			processPendingMessages() {
				if (this.pendingMessages.length === 0) {
					return;
				}
				
				console.log('开始处理消息队列，共有消息:', this.pendingMessages.length);
				
				// 检查WebSocket连接状态
				const status = ws.getStatus();
				if (!status.isConnected) {
					console.log('WebSocket未连接，延迟处理');
					
					// 延迟再次尝试处理
					setTimeout(() => {
						this.processPendingMessages();
					}, 3000);
					
					return;
				}
				
				// 一次处理一条消息，然后等待下一次处理
				const message = this.pendingMessages.shift();
				try {
					const messageStr = JSON.stringify(message);
					console.log('从队列发送WebSocket消息:', message);
					ws.send(messageStr);
				} catch (e) {
					console.error('从队列发送WebSocket消息失败:', e);
					// 失败时放回队列
					this.pendingMessages.unshift(message);
				}
				
				// 如果还有未处理的消息，设置延迟处理下一条
				if (this.pendingMessages.length > 0) {
					setTimeout(() => {
						this.processPendingMessages();
					}, 1000);
				}
			},
			
			// 预览图片
			previewImage(url) {
				// 确保URL有效
				if (!url) {
					console.error('预览图片无效URL:', url);
					return;
				}
				
				console.log('预览图片:', url);
				
				try {
				uni.previewImage({
					urls: [url],
						current: url,
						success: () => {
							console.log('图片预览成功');
			},
						fail: (err) => {
							console.error('图片预览失败:', err);
							// 显示错误提示
							uni.showToast({
								title: '图片预览失败',
								icon: 'none'
							});
						}
					});
				} catch (e) {
					console.error('图片预览异常:', e);
				}
			},
			
			// 发送文本消息
			handleSend() {
				//如果消息不为空
				if(!/^\s*$/.test(this.chatText)){
					// 设置消息类型为文本
					const message = JSON.parse(JSON.stringify(this.msgData));
					message.msgType = 1;
					message.message = this.chatText;
					message.createTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
					
					// 添加到本地消息列表
					this.rows.push(message);
					
					// 清空输入框
					this.chatText = '';
					
					// 检查WebSocket连接状态
					const status = ws.getStatus();
					if (!status.isConnected) {
						console.log('文本消息: WebSocket未连接，添加到待发送队列');
					// 添加到待发送队列
					this.pendingMessages.push(message);
					
						// 尝试重连
						ws.init();
						
						// 延迟处理队列
						setTimeout(() => {
					this.processPendingMessages();
						}, 2000);
					} else {
						// 直接发送消息
						try {
							const messageStr = JSON.stringify(message);
							console.log('直接发送文本WebSocket消息:', message);
							ws.send(messageStr);
						} catch (e) {
							console.error('发送文本消息失败:', e);
							// 失败时加入队列
							this.pendingMessages.push(message);
							// 延迟处理队列
							setTimeout(() => {
								this.processPendingMessages();
							}, 1000);
						}
					}
					
					// 滚动到底部
					this.scrollToBottom();
				} else {
					this.$modal.showToast('不能发送空白消息');
				}
			},
			initData(orderId) {
				this.msgData.orderId = orderId
				this.userInfo = this.$store.state.userInfo
				this.msgData.senderType = this.userInfo.userType
				this.msgData.senderId = this.userInfo.uid
				
				getInitChat(orderId).then(res => {
					console.log(res);
					this.chatInitData = res.data
					this.msgData.recipientIds.push(res.data.adminId)
					this.msgData.recipientIds.push(res.data.agentId)
					if(this.userInfo.uid == res.data.userId) {
						this.msgData.recipientIds.push(res.data.runnerId)
					}
					if(this.userInfo.uid == res.data.runnerId) {
						this.msgData.recipientIds.push(res.data.userId)
					}
					
				})
			},
			pageQuery() {
				getPageOrderChat(this.msgData.orderId,this.queryParams).then(res => {
					console.log(res);
					this.total = res.total
					let data = res.rows
					data.reverse()
					this.rows.unshift(...data)
					this.queryParams.pageNum += 1;
					this.hasMore = data.length > 0
					this.skeletonLoading = false
				})
			},
			focus(){
				this.scrollToBottom()
			},
			blur(){
				this.scrollToBottom()
			},
			// px转换成rpx
			rpxTopx(px){
				let deviceWidth = uni.getSystemInfoSync().windowWidth
				let rpx = ( 750 / deviceWidth ) * Number(px)
				return Math.floor(rpx)
			},
			// 监视聊天发送栏高度
			sendHeight(){
				setTimeout(()=>{
					let query = uni.createSelectorQuery();
					query.select('.send-msg').boundingClientRect()
					query.exec(res =>{
						this.bottomHeight = this.rpxTopx(res[0].height)
					})
				},10)
			},
			// 滚动至聊天底部
			scrollToBottom(){
				setTimeout(()=>{
					let query = uni.createSelectorQuery().in(this);
					query.select('#scrollview').boundingClientRect();
					query.select('#msglistview').boundingClientRect();
					query.exec((res) =>{
						if(res[0] && res[1] && res[1].height > res[0].height){
							this.scrollTop = this.rpxTopx(res[1].height - res[0].height) + 20; // 添加额外偏移，确保滚动到底部
						}
					})
				},15)
			},
			// 重写上传URL的获取方法
			getUploadUrl() {
				try {
					// 首先尝试从导入的常量获取
					if (upload_url && upload_url.length > 10) {
						console.log('使用导入的upload_url:', upload_url);
						return upload_url;
					}
					
					// 如果导入的常量不可用，尝试从全局变量获取
					if (this.$uploadUrl) {
						console.log('使用$uploadUrl:', this.$uploadUrl);
						return this.$uploadUrl;
					} else if (this.$baseUrl) {
						const uploadUrl = this.$baseUrl + '/system/oss/upload';
						console.log('使用$baseUrl构建上传URL:', uploadUrl);
						return uploadUrl;
					}
					
					// 使用request.js中定义的上传URL
					const uploadUrl = upload_url;
					console.log('使用request.js定义的上传URL:', uploadUrl);
					return uploadUrl;
				} catch (e) {
					console.error('获取上传URL出错:', e);
					return 'http://daidaida.xyz:8081/system/oss/upload';
				}
			},
			// 重新连接WebSocket
			reconnectWebSocket() {
				const status = ws.getStatus();
				console.log('WebSocket当前状态:', status);
				
				if (!status.isConnected && !status.isInitializing) {
					console.log('尝试重新连接WebSocket');
					uni.showToast({
						title: '正在重连...',
						icon: 'none',
						duration: 1500
					});
					
					// 使用随机延迟，避免多个客户端同时重连
					const delay = 300 + Math.floor(Math.random() * 700);
					
					setTimeout(() => {
						ws.init();
						
						// 设置最长连接等待时间
						setTimeout(() => {
							const newStatus = ws.getStatus();
							if (!newStatus.isConnected) {
								console.log('WebSocket重连超时');
								uni.showToast({
									title: '连接服务器超时，正在重试',
									icon: 'none',
									duration: 2000
								});
								// 可以考虑再次重连或提示用户
							}
						}, 5000);
					}, delay);
				}
			},
		}
	}
</script>
<style lang="scss" scoped>
$chatContentbgc: #E3EDFF; // 更亮柔和的蓝色背景
$sendBtnbgc: #4F7DF5; // 蓝色按钮与全局主色调一致

view, button, text, input, textarea {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.nut-tag {
  border-width: unset !important;
  margin-right: 2px !important;
  font-size: 10px !important;
  padding: 1px 6px !important;
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 200px;
  display: inline-block;
  padding-left: 2px;
}

.chatTime {
  font-size: 10px;
  color: #aaa;
  display: inline-block;
  margin-top: 4px;
}

/* 聊天消息 */
.chat {
  .topTabbar {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    display: flex;
    margin-top: 80rpx;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-radius: 0 0 20px 20px;
    padding: 0 30rpx;
    
    .icon {
      color: $sendBtnbgc;
    }

    .text {
      margin: auto;
      font-size: 18px;
      font-weight: 600;
      color: $sendBtnbgc;
    }

    .button {
      width: 10%;
      margin: auto 20rpx auto 0rpx;
    }
  }

  .scroll-view {
    background-color: #F7F9FF; // 淡蓝色背景
    
    .chat-body {
      display: flex;
      flex-direction: column;
      padding-top: 30rpx;
      padding-bottom: 30rpx;

      .contentTop {
        margin: 0 12px 4px 12px;
        max-width: 486rpx;
        color: #666;
        font-size: 12px;
        font-weight: 500;
        display: flex;
        align-items: center;
      }

      .textLeft {
        justify-content: flex-start;
      }

      .textRight {
        justify-content: flex-end;
      }

      .self {
        justify-content: flex-end;
      }

      .item {
        display: flex;
        padding: 16rpx 30rpx;
        margin: 10rpx 0;

        .right {
          background-color: $chatContentbgc;
          border-radius: 18px 4px 18px 18px; // 不对称圆角
          box-shadow: 0 2px 6px rgba(79, 125, 245, 0.1);
        }

        .left {
          background-color: #FFFFFF;
          border-radius: 4px 18px 18px 18px; // 不对称圆角
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }

        /* 移除聊天消息三角形，使用圆角代替 */
        .right::after,
        .left::after {
          display: none;
        }

        .content {
          position: relative;
          max-width: 486rpx;
          word-wrap: break-word;
          padding: 20rpx 24rpx;
          margin: 0 16rpx;
          font-size: 28rpx;
          font-weight: 400;
          color: #333333;
          line-height: 40rpx;
        }

        .avatar {
          display: flex;
          justify-content: center;
          width: 78rpx;
          height: 78rpx;
          background: $sendBtnbgc;
          border-radius: 50%;
          overflow: hidden;
          border: 2px solid #f0f2f5;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          image {
            align-self: center;
            transform: translateY(8px);
          }
        }
      }
    }
  }

  /* 底部聊天发送栏 */
  .chat-bottom {
    width: 100%;
    height: 120rpx;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.1s ease;
    border-top: 1px solid #f0f2f5;

    .send-msg {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      display: flex;
      align-items: center;
      padding: 16rpx 24rpx;
      background-color: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-top: 1px solid #f0f2f5;
      box-sizing: border-box;
      
      .uni-textarea {
        flex: 1;
        background-color: #f7f8fa;
        border-radius: 36rpx;
        padding: 16rpx 24rpx;
        margin: 10rpx 0;
        border: 1px solid #ebedf0;
        
        textarea {
          width: 100%;
          min-height: 60rpx;
          max-height: 150rpx;
          font-size: 28rpx;
        }
      }
      
      .upload-img {
        padding: 0 20rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        color: $sendBtnbgc;
      }
      
      .send-btn {
        width: 128rpx;
        height: 72rpx;
        line-height: 72rpx;
        font-size: 28rpx;
        margin-left: 16rpx;
        background-color: $sendBtnbgc;
        color: #ffffff;
        border-radius: 36rpx;
        box-shadow: 0 4px 8px rgba(79, 125, 245, 0.2);
        border: none;
      }
    }
  }
}

.chat-image {
  max-width: 400rpx;
  border-radius: 12rpx;
  margin: 10rpx 0;
  display: block;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>

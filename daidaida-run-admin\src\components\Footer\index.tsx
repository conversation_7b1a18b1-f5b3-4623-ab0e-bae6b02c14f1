import { GithubOutlined } from '@ant-design/icons';
import { DefaultFooter } from '@ant-design/pro-components';
import React from 'react';

const Footer: React.FC = () => {
  return (
    <>
    <DefaultFooter
      style={{
        background: 'none',
      }}
      // links={[
      //   {
      //     key: 'Ant Design Pro',
      //     title: 'Ant Design Pro',
      //     href: 'https://pro.ant.design',
      //     blankTarget: true,
      //   },
      //   {
      //     key: 'github',
      //     title: <GithubOutlined />,
      //     href: 'https://github.com/ant-design/ant-design-pro',
      //     blankTarget: true,
      //   },
      //   {
      //     key: 'Ant Design',
      //     title: 'Ant Design',
      //     href: 'https://ant.design',
      //     blankTarget: true,
      //   },
      // ]}
    />
      <div style={{ textAlign: 'center', fontSize: 12, color: '#999', marginTop: 8 }}>
        ©2024 daidaida.xyz | <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer">辽ICP备2025056177号-1</a>
      </div>
    </>
  );
};

export default Footer;

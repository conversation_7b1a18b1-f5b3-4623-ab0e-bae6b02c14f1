-- 福U跑腿系统 - 标准E-R图DDL语句
-- 基于init.sql生成，包含完整的外键约束关系
-- 生成时间: 2025-01-10

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===========================
-- 1. 核心用户表
-- ===========================

-- 全局用户表（强实体）
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `uid` bigint NOT NULL AUTO_INCREMENT COMMENT '全局uid',
  `device_type` tinyint NOT NULL COMMENT '设备类型: 0=PC, 1=小程序',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `login_time` datetime NOT NULL COMMENT '上次登录时间',
  `login_ip` varchar(20) NOT NULL COMMENT '登录IP',
  `login_region` varchar(50) NOT NULL COMMENT '登录地址',
  `user_type` int NOT NULL COMMENT '用户类型: 0=超级管理员, 1=校区管理员, 2=普通管理员, 3=普通用户, 4=跑腿用户',
  `create_id` bigint NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_id` bigint NOT NULL COMMENT '更新人',
  PRIMARY KEY (`uid`),
  INDEX `idx_user_type` (`user_type`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='全局用户表';

-- PC用户表（强实体，与user一对一关系）
DROP TABLE IF EXISTS `user_pc`;
CREATE TABLE `user_pc` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uid` bigint NOT NULL COMMENT '关联用户表',
  `username` varchar(25) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `phone` varchar(11) NOT NULL COMMENT '手机号',
  `name` varchar(20) NOT NULL COMMENT '真实姓名',
  `student_card_url` varchar(255) DEFAULT NULL COMMENT '学生证',
  `id_card_url` varchar(255) DEFAULT NULL COMMENT '身份证',
  `sex` tinyint NOT NULL COMMENT '性别: 0=女, 1=男',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态: 0=禁用, 1=启用',
  `avatar` varchar(255) NOT NULL COMMENT '头像',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `email_enable` tinyint DEFAULT NULL COMMENT '是否启用邮箱',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uid` (`uid`),
  UNIQUE KEY `uk_username` (`username`),
  CONSTRAINT `fk_user_pc_uid` FOREIGN KEY (`uid`) REFERENCES `user` (`uid`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='PC用户表（平台管理员）';

-- 微信用户表（强实体，与user一对一关系）
DROP TABLE IF EXISTS `user_wx`;
CREATE TABLE `user_wx` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uid` bigint NOT NULL COMMENT '关联用户表',
  `openid` varchar(128) NOT NULL COMMENT '小程序唯一ID',
  `avatar` varchar(255) NOT NULL COMMENT '头像',
  `nickname` varchar(10) NOT NULL COMMENT '昵称',
  `phone` varchar(11) DEFAULT NULL COMMENT '手机号',
  `points` int NOT NULL DEFAULT 0 COMMENT '积分',
  `is_runner` tinyint NOT NULL DEFAULT 0 COMMENT '是否跑腿员: 0=否, 1=是',
  `can_order` tinyint NOT NULL DEFAULT 0 COMMENT '是否可下单: 0=否, 1=是',
  `can_take` tinyint NOT NULL DEFAULT 0 COMMENT '是否可接单: 0=否, 1=是',
  `school_id` bigint DEFAULT NULL COMMENT '绑定学校ID',
  `realname` varchar(20) DEFAULT NULL COMMENT '跑腿真实姓名',
  `gender` tinyint DEFAULT NULL COMMENT '跑腿性别',
  `credit_score` int DEFAULT NULL COMMENT '信用分',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uid` (`uid`),
  UNIQUE KEY `uk_openid` (`openid`),
  INDEX `idx_school_id` (`school_id`),
  CONSTRAINT `fk_user_wx_uid` FOREIGN KEY (`uid`) REFERENCES `user` (`uid`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_wx_school` FOREIGN KEY (`school_id`) REFERENCES `school` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='微信用户表（小程序用户）';

-- ===========================
-- 2. 学校相关表
-- ===========================

-- 学校表（强实体）
DROP TABLE IF EXISTS `school`;
CREATE TABLE `school` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `belong_uid` bigint NOT NULL COMMENT '管理员用户ID',
  `adcode` char(6) DEFAULT NULL COMMENT '城市编码',
  `name` varchar(100) NOT NULL COMMENT '学校名称',
  `logo` varchar(255) NOT NULL COMMENT '学校logo',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `status` tinyint NOT NULL COMMENT '状态: 0=禁用, 1=启用',
  `profit_plat` tinyint NOT NULL COMMENT '平台收益占比',
  `profit_agent` tinyint NOT NULL COMMENT '代理收益占比',
  `profit_runner` tinyint NOT NULL COMMENT '跑腿收益占比',
  `floor_price` decimal(10,2) NOT NULL COMMENT '底价',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  INDEX `idx_belong_uid` (`belong_uid`),
  INDEX `idx_status` (`status`),
  CONSTRAINT `fk_school_belong_uid` FOREIGN KEY (`belong_uid`) REFERENCES `user` (`uid`) ON DELETE RESTRICT
) ENGINE=InnoDB COMMENT='学校表';

-- 学校区域表（强实体，与school多对一关系）
DROP TABLE IF EXISTS `school_region`;
CREATE TABLE `school_region` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `school_id` bigint NOT NULL COMMENT '学校ID',
  `type` tinyint NOT NULL COMMENT '类型: 0=区域, 1=楼栋',
  `name` varchar(12) NOT NULL COMMENT '名称',
  `lon` varchar(50) DEFAULT NULL COMMENT '经度',
  `lat` varchar(50) DEFAULT NULL COMMENT '纬度',
  `parent_id` bigint DEFAULT NULL COMMENT '父区域ID',
  `remark` varchar(20) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_id` bigint NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `update_id` bigint NOT NULL COMMENT '修改人',
  PRIMARY KEY (`id`),
  INDEX `idx_school_id` (`school_id`),
  INDEX `idx_parent_id` (`parent_id`),
  CONSTRAINT `fk_school_region_school` FOREIGN KEY (`school_id`) REFERENCES `school` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_school_region_parent` FOREIGN KEY (`parent_id`) REFERENCES `school_region` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='学校区域表（楼栋管理）';

-- 标签表（强实体，与school多对一关系）
DROP TABLE IF EXISTS `tags`;
CREATE TABLE `tags` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `school_id` bigint NOT NULL COMMENT '学校ID',
  `name` varchar(10) NOT NULL COMMENT '标签名称',
  `remark` varchar(50) DEFAULT NULL COMMENT '备注',
  `service_type` int NOT NULL COMMENT '服务类型: 0=帮取送, 1=代买, 2=万能服务',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_id` bigint NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_id` bigint NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_school_id` (`school_id`),
  INDEX `idx_service_type` (`service_type`),
  CONSTRAINT `fk_tags_school` FOREIGN KEY (`school_id`) REFERENCES `school` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='标签表';

-- ===========================
-- 3. 订单相关表
-- ===========================

-- 订单主表（强实体）
DROP TABLE IF EXISTS `order_main`;
CREATE TABLE `order_main` (
  `id` bigint NOT NULL COMMENT '订单ID（非自增）',
  `school_id` bigint NOT NULL COMMENT '学校ID',
  `service_type` int NOT NULL COMMENT '服务类型: 0=帮取送, 1=代买, 2=万能服务',
  `tag` varchar(10) NOT NULL COMMENT '标签',
  `weight` varchar(20) DEFAULT NULL COMMENT '物品重量',
  `start_address` json DEFAULT NULL COMMENT '起点地址',
  `end_address` json NOT NULL COMMENT '终点地址',
  `detail` varchar(100) NOT NULL COMMENT '具体描述',
  `is_timed` tinyint NOT NULL COMMENT '是否指定时间: 0=否, 1=是',
  `specified_time` datetime DEFAULT NULL COMMENT '指定时间',
  `auto_cancel_ttl` int NOT NULL COMMENT '未结单取消时间（秒）',
  `gender` tinyint NOT NULL COMMENT '性别要求: 0=女, 1=男, 2=不限',
  `estimated_price` decimal(10,2) DEFAULT NULL COMMENT '预估商品价格',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `status` tinyint NOT NULL COMMENT '订单状态',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `user_id` bigint NOT NULL COMMENT '下单用户ID',
  `runner_id` bigint DEFAULT NULL COMMENT '跑腿员ID',
  PRIMARY KEY (`id`),
  INDEX `idx_school_id` (`school_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_runner_id` (`runner_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_create_time` (`create_time`),
  CONSTRAINT `fk_order_school` FOREIGN KEY (`school_id`) REFERENCES `school` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_order_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`uid`) ON DELETE RESTRICT,
  CONSTRAINT `fk_order_runner` FOREIGN KEY (`runner_id`) REFERENCES `user` (`uid`) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='订单主表';

-- 订单支付表（弱实体，与order_main一对一关系）
DROP TABLE IF EXISTS `order_payment`;
CREATE TABLE `order_payment` (
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `additional_amount` decimal(10,2) DEFAULT NULL COMMENT '附加金额',
  `actual_payment` decimal(10,2) DEFAULT NULL COMMENT '实付金额',
  `payment_status` tinyint NOT NULL COMMENT '支付状态: 0=未支付, 1=已支付, 2=退款中, 3=已退款',
  `payment_time` datetime DEFAULT NULL COMMENT '付款时间',
  `refund_pending_time` datetime DEFAULT NULL COMMENT '退款中时间',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `is_couponed` tinyint NOT NULL COMMENT '是否使用优惠券: 0=否, 1=是',
  `coupon_id` bigint DEFAULT NULL COMMENT '优惠券ID',
  `discount_amount` decimal(10,2) DEFAULT NULL COMMENT '优惠金额',
  PRIMARY KEY (`order_id`),
  INDEX `idx_payment_status` (`payment_status`),
  INDEX `idx_coupon_id` (`coupon_id`),
  CONSTRAINT `fk_payment_order` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='订单支付表';

-- 订单进度表（弱实体，与order_main一对一关系）
DROP TABLE IF EXISTS `order_progress`;
CREATE TABLE `order_progress` (
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `accepted_time` datetime DEFAULT NULL COMMENT '接单时间',
  `delivering_time` datetime DEFAULT NULL COMMENT '开始配送时间',
  `delivered_time` datetime DEFAULT NULL COMMENT '送达时间',
  `completed_time` datetime DEFAULT NULL COMMENT '完成时间',
  `completed_type` tinyint DEFAULT NULL COMMENT '完成类型',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(100) DEFAULT NULL COMMENT '取消原因',
  `cancel_user_type` int DEFAULT NULL COMMENT '取消人类型',
  `cancel_user_id` bigint DEFAULT NULL COMMENT '取消人ID',
  PRIMARY KEY (`order_id`),
  CONSTRAINT `fk_progress_order` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='订单进度表';

-- 订单聊天表（强实体，与order_main一对多关系）
DROP TABLE IF EXISTS `order_chat`;
CREATE TABLE `order_chat` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `sender_id` bigint DEFAULT NULL COMMENT '发送者ID',
  `sender_type` int DEFAULT NULL COMMENT '发送者类型',
  `recipients` varchar(255) DEFAULT NULL COMMENT '接收者IDs',
  `msg_type` int DEFAULT NULL COMMENT '消息类型',
  `message` varchar(255) DEFAULT NULL COMMENT '消息内容',
  `readIds` varchar(255) DEFAULT NULL COMMENT '已读IDs',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id` DESC),
  INDEX `idx_order_id` (`order_id`),
  INDEX `idx_create_time` (`create_time`),
  CONSTRAINT `fk_chat_order` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='订单聊天表';

-- 订单申诉表（强实体，与order_main一对多关系）
DROP TABLE IF EXISTS `order_appeal`;
CREATE TABLE `order_appeal` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `school_id` bigint DEFAULT NULL COMMENT '学校ID',
  `appeal_time` datetime DEFAULT NULL COMMENT '申诉时间',
  `appeal_reason` varchar(100) DEFAULT NULL COMMENT '申诉理由',
  `appeal_status` tinyint DEFAULT NULL COMMENT '申诉状态: 0=不通过, 1=通过, 2=申诉中',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remarks` varchar(100) DEFAULT NULL COMMENT '申诉备注',
  `update_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_type` int DEFAULT NULL COMMENT '更新人类型',
  PRIMARY KEY (`id`),
  INDEX `idx_order_id` (`order_id`),
  INDEX `idx_school_id` (`school_id`),
  INDEX `idx_appeal_status` (`appeal_status`),
  CONSTRAINT `fk_appeal_order` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_appeal_school` FOREIGN KEY (`school_id`) REFERENCES `school` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='订单申诉表';

-- 订单附件表（强实体，与order_main一对多关系）
DROP TABLE IF EXISTS `order_attachment`;
CREATE TABLE `order_attachment` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `file_name` varchar(100) NOT NULL COMMENT '文件原始名',
  `file_url` varchar(255) NOT NULL COMMENT '文件地址',
  `file_type` varchar(50) NOT NULL COMMENT '文件后缀',
  `file_size` int NOT NULL COMMENT '文件大小（字节）',
  `type` int NOT NULL COMMENT '类型: 1=订单附件, 2=完成凭证, 3=申诉凭证',
  PRIMARY KEY (`id`),
  INDEX `idx_order_id` (`order_id`),
  INDEX `idx_type` (`type`),
  CONSTRAINT `fk_attachment_order` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='订单附件表';

-- ===========================
-- 4. 财务相关表
-- ===========================

-- 钱包表（弱实体，与user一对一关系）
DROP TABLE IF EXISTS `wallet`;
CREATE TABLE `wallet` (
  `uid` bigint NOT NULL COMMENT '用户ID',
  `withdrawn` decimal(10,2) NOT NULL COMMENT '当前余额',
  `balance` decimal(10,2) NOT NULL COMMENT '已提现',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`uid`),
  CONSTRAINT `fk_wallet_user` FOREIGN KEY (`uid`) REFERENCES `user` (`uid`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='用户钱包表';

-- 资金流水表（强实体）
DROP TABLE IF EXISTS `capital_flow`;
CREATE TABLE `capital_flow` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint DEFAULT NULL COMMENT '订单ID',
  `agent_id` bigint DEFAULT NULL COMMENT '代理ID',
  `profit_agent` decimal(10,2) DEFAULT NULL COMMENT '代理收益',
  `runner_id` bigint DEFAULT NULL COMMENT '跑腿员ID',
  `profit_runner` decimal(10,2) DEFAULT NULL COMMENT '跑腿收益',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `profit_user` decimal(10,2) DEFAULT NULL COMMENT '用户收益',
  `profit_plat` decimal(10,2) DEFAULT NULL COMMENT '平台收益',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `type` tinyint UNSIGNED DEFAULT NULL COMMENT '类型: 订单收益/跑腿提现/代理提现',
  PRIMARY KEY (`id`),
  INDEX `idx_order_id` (`order_id`),
  INDEX `idx_agent_id` (`agent_id`),
  INDEX `idx_runner_id` (`runner_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_create_time` (`create_time`),
  CONSTRAINT `fk_capital_order` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_capital_agent` FOREIGN KEY (`agent_id`) REFERENCES `user` (`uid`) ON DELETE SET NULL,
  CONSTRAINT `fk_capital_runner` FOREIGN KEY (`runner_id`) REFERENCES `user` (`uid`) ON DELETE SET NULL,
  CONSTRAINT `fk_capital_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`uid`) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='资金流水表';

-- 提现记录表（强实体，与user一对多关系）
DROP TABLE IF EXISTS `money_recode`;
CREATE TABLE `money_recode` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uid` bigint NOT NULL COMMENT '用户ID',
  `cash` decimal(10,2) NOT NULL COMMENT '提现金额',
  `platform` varchar(10) NOT NULL COMMENT '提现平台',
  `card` varchar(100) NOT NULL COMMENT '卡号',
  `status` tinyint NOT NULL COMMENT '状态: 0=驳回, 1=通过, 2=审核中',
  `type` tinyint NOT NULL COMMENT '用户类型',
  `remark` varchar(50) NOT NULL COMMENT '用户备注',
  `update_id` bigint NOT NULL COMMENT '审核人',
  `update_time` datetime NOT NULL COMMENT '审核时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `feedback` varchar(50) DEFAULT NULL COMMENT '审核反馈',
  PRIMARY KEY (`id`),
  INDEX `idx_uid` (`uid`),
  INDEX `idx_status` (`status`),
  CONSTRAINT `fk_money_recode_user` FOREIGN KEY (`uid`) REFERENCES `user` (`uid`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='提现记录表';

-- ===========================
-- 5. 其他辅助表
-- ===========================

-- 地址表（强实体，与user一对多关系）
DROP TABLE IF EXISTS `address`;
CREATE TABLE `address` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uid` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(50) NOT NULL COMMENT '地点',
  `detail` varchar(50) NOT NULL COMMENT '地址详情',
  `lon` varchar(50) NOT NULL COMMENT '经度',
  `lat` varchar(50) NOT NULL COMMENT '纬度',
  `name` varchar(20) NOT NULL COMMENT '姓名',
  `phone` varchar(11) NOT NULL COMMENT '电话',
  `is_default` tinyint NOT NULL COMMENT '是否默认: 0=否, 1=是',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_id` bigint NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `update_id` bigint NOT NULL COMMENT '修改人',
  PRIMARY KEY (`id`),
  INDEX `idx_uid` (`uid`),
  CONSTRAINT `fk_address_user` FOREIGN KEY (`uid`) REFERENCES `user` (`uid`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='用户地址表';

-- 跑腿申请表（强实体）
DROP TABLE IF EXISTS `runner_apply`;
CREATE TABLE `runner_apply` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `uid` bigint NOT NULL COMMENT '用户ID',
  `school_id` bigint NOT NULL COMMENT '学校ID',
  `school_name` varchar(100) NOT NULL COMMENT '学校名称',
  `realname` varchar(20) NOT NULL COMMENT '真实姓名',
  `gender` tinyint NOT NULL COMMENT '性别: 0=女, 1=男',
  `student_card_url` varchar(255) NOT NULL COMMENT '学生证',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `status` tinyint NOT NULL COMMENT '申请状态: 0=驳回, 1=通过, 2=申请中',
  `remarks` varchar(100) DEFAULT NULL COMMENT '备注',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_id` bigint NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_uid` (`uid`),
  INDEX `idx_school_id` (`school_id`),
  INDEX `idx_status` (`status`),
  CONSTRAINT `fk_runner_apply_user` FOREIGN KEY (`uid`) REFERENCES `user` (`uid`) ON DELETE CASCADE,
  CONSTRAINT `fk_runner_apply_school` FOREIGN KEY (`school_id`) REFERENCES `school` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='跑腿申请表';

-- ===========================
-- 6. 系统管理表
-- ===========================

-- 权限表（强实体）
DROP TABLE IF EXISTS `perm`;
CREATE TABLE `perm` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `parent_id` bigint DEFAULT 0 COMMENT '父级ID',
  `sort` int DEFAULT 0 COMMENT '排序字段',
  `perms` varchar(50) DEFAULT NULL COMMENT '权限标识',
  PRIMARY KEY (`id`),
  INDEX `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB COMMENT='权限表';

-- 角色权限关联表（关联实体）
DROP TABLE IF EXISTS `role_perm`;
CREATE TABLE `role_perm` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `perm_id` bigint NOT NULL COMMENT '权限ID',
  PRIMARY KEY (`role_id`, `perm_id`),
  CONSTRAINT `fk_role_perm_perm` FOREIGN KEY (`perm_id`) REFERENCES `perm` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='角色权限关联表';

-- OSS存储表（强实体）
DROP TABLE IF EXISTS `oss`;
CREATE TABLE `oss` (
  `id` bigint NOT NULL COMMENT '对象存储主键',
  `file_size` int NOT NULL COMMENT '文件大小',
  `file_name` varchar(255) NOT NULL DEFAULT '' COMMENT '文件名',
  `type` int NOT NULL DEFAULT 0 COMMENT '文件类型',
  `original_name` varchar(255) NOT NULL DEFAULT '' COMMENT '原始文件名',
  `file_suffix` varchar(10) NOT NULL DEFAULT '' COMMENT '文件后缀',
  `url` varchar(500) NOT NULL COMMENT 'URL地址',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_id` varchar(64) DEFAULT '' COMMENT '上传人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_id` varchar(64) DEFAULT '' COMMENT '更新人',
  `service` varchar(20) NOT NULL DEFAULT 'minio' COMMENT '服务商',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='OSS对象存储表';

-- OSS配置表（强实体）
DROP TABLE IF EXISTS `oss_config`;
CREATE TABLE `oss_config` (
  `id` bigint NOT NULL COMMENT '主键',
  `config_key` varchar(20) NOT NULL DEFAULT '' COMMENT '配置key',
  `access_key` varchar(255) DEFAULT '' COMMENT 'accessKey',
  `secret_key` varchar(255) DEFAULT '' COMMENT '秘钥',
  `bucket_name` varchar(255) DEFAULT '' COMMENT '桶名称',
  `prefix` varchar(255) DEFAULT '' COMMENT '前缀',
  `endpoint` varchar(255) DEFAULT '' COMMENT '访问站点',
  `domain` varchar(255) DEFAULT '' COMMENT '自定义域名',
  `is_https` char(1) DEFAULT 'N' COMMENT '是否https: Y=是, N=否',
  `region` varchar(255) DEFAULT '' COMMENT '域',
  `access_policy` char(1) NOT NULL DEFAULT '1' COMMENT '桶权限类型: 0=private, 1=public, 2=custom',
  `status` char(1) DEFAULT '1' COMMENT '是否默认: 0=是, 1=否',
  `ext1` varchar(255) DEFAULT '' COMMENT '扩展字段',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='OSS配置表';

-- 统计数据表（强实体）
DROP TABLE IF EXISTS `statistics_daily`;
CREATE TABLE `statistics_daily` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `total_orders` int NOT NULL DEFAULT 0 COMMENT '总订单量',
  `canceled_orders` int NOT NULL DEFAULT 0 COMMENT '取消订单量',
  `appealed_orders` int NOT NULL DEFAULT 0 COMMENT '申诉订单量',
  `completed_orders` int NOT NULL DEFAULT 0 COMMENT '完成订单量',
  `completion_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '完单率(%)',
  `delivery_orders` int NOT NULL DEFAULT 0 COMMENT '帮取送订单量',
  `purchase_orders` int NOT NULL DEFAULT 0 COMMENT '代买订单量',
  `universal_orders` int NOT NULL DEFAULT 0 COMMENT '万能服务订单量',
  `delivery_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '帮取送订单占比(%)',
  `purchase_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '代买订单占比(%)',
  `universal_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '万能订单占比(%)',
  `total_payment` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '总收款金额',
  `total_refund` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '总退款金额',
  `platform_profit` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '平台总收益',
  `agent_profit` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '代理总收益',
  `runner_profit` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '跑腿总收益',
  `total_visits` int NOT NULL DEFAULT 0 COMMENT '总访问量',
  `unique_visitors` int NOT NULL DEFAULT 0 COMMENT '独立访问用户数',
  `malicious_requests` int NOT NULL DEFAULT 0 COMMENT '恶意请求数量',
  `new_users` int NOT NULL DEFAULT 0 COMMENT '新增用户数',
  `active_users` int NOT NULL DEFAULT 0 COMMENT '活跃用户数',
  `new_runners` int NOT NULL DEFAULT 0 COMMENT '新增跑腿用户数',
  `active_runners` int NOT NULL DEFAULT 0 COMMENT '活跃跑腿用户数',
  `create_time` date NOT NULL COMMENT '统计日期',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='每日统计数据表';

SET FOREIGN_KEY_CHECKS = 1;

-- ===========================
-- E-R图关系说明
-- ===========================
/*
主要实体关系：
1. USER (1:1) USER_PC - 用户拥有PC账户
2. USER (1:1) USER_WX - 用户拥有微信账户  
3. USER (1:1) WALLET - 用户拥有钱包
4. USER (1:N) ADDRESS - 用户拥有多个地址
5. USER (1:N) SCHOOL - 用户管理多个学校
6. USER (1:N) RUNNER_APPLY - 用户可多次申请跑腿
7. USER (1:N) MONEY_RECODE - 用户有多个提现记录
8. SCHOOL (1:N) USER_WX - 学校绑定多个微信用户
9. SCHOOL (1:N) SCHOOL_REGION - 学校有多个区域
10. SCHOOL (1:N) TAGS - 学校有多个标签
11. SCHOOL (1:N) ORDER_MAIN - 学校有多个订单
12. USER_WX (1:N) ORDER_MAIN - 微信用户下多个订单（user_id）
13. USER_WX (1:N) ORDER_MAIN - 微信用户接多个订单（runner_id）
14. ORDER_MAIN (1:1) ORDER_PAYMENT - 订单有一个支付记录
15. ORDER_MAIN (1:1) ORDER_PROGRESS - 订单有一个进度记录
16. ORDER_MAIN (1:N) ORDER_CHAT - 订单有多个聊天记录
17. ORDER_MAIN (1:N) ORDER_APPEAL - 订单有多个申诉记录
18. ORDER_MAIN (1:N) ORDER_ATTACHMENT - 订单有多个附件
19. ORDER_MAIN (1:N) CAPITAL_FLOW - 订单产生多个资金流水
20. PERM (M:N) ROLE_PERM - 权限和角色多对多关系
*/

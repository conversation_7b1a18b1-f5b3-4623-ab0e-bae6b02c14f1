.chatContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}
  
  .messageList {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}
    
    .messageItem {
      display: flex;
      margin-bottom: 16px;
  position: relative;
}

.self {
  flex-direction: row-reverse;
}
      
      .avatar {
  width: 36px;
  height: 36px;
        border-radius: 50%;
  margin: 0 8px;
        flex-shrink: 0;
      }
      
      .contentBox {
        max-width: 70%;
}
        
        .contentTop {
          display: flex;
          align-items: center;
          margin-bottom: 4px;
}

.textLeft {
  justify-content: flex-start;
}

.textRight {
            justify-content: flex-end;
          }
          
        .content {
  border-radius: 12px;
          padding: 8px 12px;
  position: relative;
  word-break: break-word;
}

.left {
          background-color: #f0f0f0;
}

.right {
  background-color: #e2f4ff;
}
          
          .chatTime {
            font-size: 12px;
            color: #999;
  margin-top: 4px;
  display: block;
          }

.ellipsis {
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0 4px;
}

.inputArea {
  display: flex;
  align-items: flex-end;
  padding: 12px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
}

.inputArea :global(.ant-input-textarea) {
  flex: 1;
  margin: 0 8px;
}

.inputArea :global(.ant-upload) {
  display: flex;
  align-items: center;
  }
  
.inputArea :global(.ant-upload button) {
  padding: 0 8px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 全屏模式下图片显示优化 */
.fullscreen {
  :global {
    .ant-drawer-content {
      height: 100vh;
    }
    
    .chat-image {
      max-width: 400px;
      max-height: 400px;
    }
  }
} 